package org.dromara.system.controller.im;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImTranslationPackageBo;
import org.dromara.im.domain.vo.ImTranslationPackageVo;
import org.dromara.im.service.IImTranslationPackageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 套餐(灵活版)
 *
 * <AUTHOR> Li
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/translatePackage")
public class ImTranslatePackageCtrl extends BaseController {

    private final IImTranslationPackageService imTranslationPackageService;

    /**
     * 查询翻译套餐列表
     */
    @GetMapping("/list")
    public R<TableDataInfo<ImTranslationPackageVo>> list(ImTranslationPackageBo bo, PageQuery pageQuery) {
        return R.ok(imTranslationPackageService.queryPageList(bo, pageQuery));
    }

    /**
     * 获取翻译套餐详细信息
     *
     * @param translationPackageId 主键
     */
    @GetMapping("/{translationPackageId}")
    public R<ImTranslationPackageVo> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long translationPackageId) {
        return R.ok(imTranslationPackageService.queryById(translationPackageId));
    }

    /**
     * 新增翻译套餐
     */
    @Log(title = "翻译套餐", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImTranslationPackageBo bo) {
        return toAjax(imTranslationPackageService.insertByBo(bo));
    }

    /**
     * 修改翻译套餐
     */
    @Log(title = "翻译套餐", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImTranslationPackageBo bo) {
        return toAjax(imTranslationPackageService.updateByBo(bo));
    }

    /**
     * 删除翻译套餐
     *
     * @param translationPackageIds 主键串
     */
    @Log(title = "翻译套餐", businessType = BusinessType.DELETE)
    @DeleteMapping("/{translationPackageIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] translationPackageIds) {
        return toAjax(imTranslationPackageService.deleteWithValidByIds(List.of(translationPackageIds), true));
    }
}
