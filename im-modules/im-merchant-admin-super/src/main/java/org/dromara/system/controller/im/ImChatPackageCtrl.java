package org.dromara.system.controller.im;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImChatPackageBo;
import org.dromara.im.domain.vo.ImChatPackageVo;
import org.dromara.im.service.IImChatPackageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 套餐(灵活版)
 *
 * <AUTHOR> Li
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/chatPackage")
public class ImChatPackageCtrl extends BaseController {

    private final IImChatPackageService imChatPackageService;

    /**
     * 查询套餐(灵活版)列表
     */
    @GetMapping("/list")
    public R<TableDataInfo<ImChatPackageVo>> list(ImChatPackageBo bo, PageQuery pageQuery) {
        return R.ok(imChatPackageService.queryPageList(bo, pageQuery));
    }

    /**
     * 获取套餐(灵活版)详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ImChatPackageVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long id) {
        return R.ok(imChatPackageService.queryById(id));
    }

    /**
     * 新增套餐(灵活版)
     */
    @Log(title = "套餐(灵活版)", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImChatPackageBo bo) {
        return toAjax(imChatPackageService.insertByBo(bo));
    }

    /**
     * 修改套餐(灵活版)
     */
    @Log(title = "套餐(灵活版)", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImChatPackageBo bo) {
        return toAjax(imChatPackageService.updateByBo(bo));
    }

    /**
     * 删除套餐(灵活版)
     *
     * @param ids 主键串
     */
    @Log(title = "套餐(灵活版)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(imChatPackageService.deleteWithValidByIds(List.of(ids), true));
    }
}
