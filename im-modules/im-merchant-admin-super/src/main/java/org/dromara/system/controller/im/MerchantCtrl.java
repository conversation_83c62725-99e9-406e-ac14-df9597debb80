package org.dromara.system.controller.im;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.service.IImMerchantService;
import org.dromara.im.domain.bo.AddMerchantBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/admin/merchants")
public class MerchantCtrl {

    @Autowired
    private IImMerchantService imMerchantService;

    /**
     * 成员列表
     */
    @GetMapping("/list")
    public R<?> list(ImMerchantBo bo, PageQuery pageQuery) {
        return R.ok(imMerchantService.queryPageList(bo, pageQuery));
    }

    @PostMapping("/add")
    public R<?> add(@RequestBody AddMerchantBo bo) {
        imMerchantService.add(bo);
        return R.ok();
    }

}
