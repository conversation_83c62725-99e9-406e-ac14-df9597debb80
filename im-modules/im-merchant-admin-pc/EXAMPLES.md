# 功能演示与使用示例

## BaseMapperPlus增强功能演示

### 1. 自动VO转换查询

```java
@RestController
@RequestMapping("/api/merchants")
public class MerchantController {
    
    @Autowired
    private MerchantMapper merchantMapper;
    
    // 查询商户列表，自动转换为VO
    @GetMapping("/list")
    public List<MerchantVO> getMerchantList() {
        return merchantMapper.selectVoList(
            Wrappers.<MerchantDO>lambdaQuery()
                .eq(MerchantDO::getStatus, 1)
                .orderByDesc(MerchantDO::getCreateTime)
        );
    }
    
    // 根据ID查询单个商户VO
    @GetMapping("/{id}")
    public MerchantVO getMerchantById(@PathVariable Long id) {
        return merchantMapper.selectVoById(id);
    }
    
    // 分页查询商户VO
    @GetMapping("/page")
    public Page<MerchantVO> getMerchantPage(
        @RequestParam(defaultValue = "1") int current,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String companyName) {
        
        LambdaQueryWrapper<MerchantDO> wrapper = Wrappers.<MerchantDO>lambdaQuery()
            .like(StringUtils.isNotBlank(companyName), MerchantDO::getCompanyName, companyName)
            .orderByDesc(MerchantDO::getCreateTime);
            
        return merchantMapper.selectVoPage(new Page<>(current, size), wrapper);
    }
}
```

### 2. 批量操作演示

```java
@Service
public class MerchantBatchService {
    
    @Autowired
    private MerchantMapper merchantMapper;
    
    // 批量插入商户
    @Transactional
    public boolean batchCreateMerchants(List<CreateMerchantCommand> commands) {
        List<MerchantDO> merchantDOs = commands.stream()
            .map(this::convertToMerchantDO)
            .collect(Collectors.toList());
            
        return merchantMapper.insertBatch(merchantDOs);
    }
    
    // 批量更新商户状态
    @Transactional
    public boolean batchUpdateStatus(List<Long> merchantIds, Integer status) {
        List<MerchantDO> merchants = merchantIds.stream()
            .map(id -> {
                MerchantDO merchant = new MerchantDO();
                merchant.setId(id);
                merchant.setStatus(status);
                return merchant;
            })
            .collect(Collectors.toList());
            
        return merchantMapper.updateBatchById(merchants);
    }
    
    // 批量插入或更新
    @Transactional
    public boolean batchSaveOrUpdate(List<MerchantDO> merchants) {
        return merchantMapper.insertOrUpdateBatch(merchants);
    }
}
```

## MapstructUtils转换演示

### 1. 基础对象转换

```java

@Component
public class MerchantConverterExample {

    // 单对象转换
    public MerchantVO convertToVO(Merchant merchant) {
        return MapstructUtils.convert(merchant, MerchantVO.class);
    }

    // 列表转换
    public List<MerchantVO> convertToVOList(List<Merchant> merchants) {
        return MapstructUtils.convert(merchants, MerchantVO.class);
    }

    // Map转换为对象
    public MerchantDTO convertMapToDTO(Map<String, Object> paramMap) {
        return MapstructUtils.convert(paramMap, MerchantDTO.class);
    }

    // 复杂嵌套对象转换
    public MerchantDetailVO convertToDetailVO(Merchant merchant) {
        MerchantDetailVO vo = MapstructUtils.convert(merchant, MerchantDetailVO.class);

        // 手动处理特殊字段
        if (merchant.getAdministrators() != null) {
            List<AdminVO> adminVOs = MapstructUtils.convert(
                merchant.getAdministrators(),
                AdminVO.class
            );
            vo.setAdministrators(adminVOs);
        }

        return vo;
    }
}
```

### 2. 性能对比演示

```java
@Component
public class PerformanceComparison {
    
    private static final int TEST_COUNT = 10000;
    
    // MapstructUtils转换（推荐）
    public void testMapstructConversion() {
        List<MerchantDO> merchants = generateTestData();
        
        long start = System.currentTimeMillis();
        for (int i = 0; i < TEST_COUNT; i++) {
            List<MerchantVO> vos = MapstructUtils.convert(merchants, MerchantVO.class);
        }
        long end = System.currentTimeMillis();
        
        System.out.println("MapStruct转换耗时: " + (end - start) + "ms");
    }
    
    // 反射转换（不推荐）
    public void testReflectionConversion() {
        List<MerchantDO> merchants = generateTestData();
        
        long start = System.currentTimeMillis();
        for (int i = 0; i < TEST_COUNT; i++) {
            List<MerchantVO> vos = merchants.stream()
                .map(this::reflectionConvert)
                .collect(Collectors.toList());
        }
        long end = System.currentTimeMillis();
        
        System.out.println("反射转换耗时: " + (end - start) + "ms");
    }
    
    // 结果：MapStruct比反射快10倍左右
}
```

## 高级查询示例

### 1. 复杂条件查询

```java
@Service
public class MerchantQueryService {
    
    @Autowired
    private MerchantMapper merchantMapper;
    
    // 多条件动态查询
    public List<MerchantVO> searchMerchants(MerchantSearchQuery query) {
        LambdaQueryWrapper<MerchantDO> wrapper = Wrappers.<MerchantDO>lambdaQuery()
            .like(StringUtils.isNotBlank(query.getCompanyName()), 
                  MerchantDO::getCompanyName, query.getCompanyName())
            .eq(query.getStatus() != null, 
                MerchantDO::getStatus, query.getStatus())
            .between(query.getStartDate() != null && query.getEndDate() != null,
                     MerchantDO::getCreateTime, query.getStartDate(), query.getEndDate())
            .in(CollectionUtils.isNotEmpty(query.getMerchantIds()),
                MerchantDO::getId, query.getMerchantIds())
            .orderByDesc(MerchantDO::getCreateTime);
            
        return merchantMapper.selectVoList(wrapper);
    }
    
    // JSON字段查询
    public List<MerchantVO> findByPackageLevel(Integer packageLevel) {
        QueryWrapper<MerchantDO> wrapper = new QueryWrapper<>();
        wrapper.apply("JSON_EXTRACT(service_package, '$.packageLevel') = {0}", packageLevel);
        
        return merchantMapper.selectVoList(wrapper);
    }
    
    // 统计查询
    public Map<String, Object> getMerchantStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总数统计
        stats.put("total", merchantMapper.selectCount(null));
        
        // 按状态统计
        LambdaQueryWrapper<MerchantDO> activeWrapper = Wrappers.<MerchantDO>lambdaQuery()
            .eq(MerchantDO::getStatus, 2); // 正常状态
        stats.put("active", merchantMapper.selectCount(activeWrapper));
        
        // 本月新增
        LocalDateTime monthStart = LocalDateTime.of(LocalDate.now().withDayOfMonth(1), LocalTime.MIN);
        LambdaQueryWrapper<MerchantDO> monthWrapper = Wrappers.<MerchantDO>lambdaQuery()
            .ge(MerchantDO::getCreateTime, monthStart);
        stats.put("thisMonth", merchantMapper.selectCount(monthWrapper));
        
        return stats;
    }
}
```

### 2. 关联查询示例

```java

@Service
public class MerchantWithAdminService {

    @Autowired
    private MerchantMapper merchantMapper;

    @Autowired
    private MerchantAdministratorMapper adminMapper;

    // 查询商户及其管理员信息
    public MerchantDetailVO getMerchantWithAdmins(Long merchantId) {
        // 查询商户信息
        MerchantVO merchantVO = merchantMapper.selectVoById(merchantId);
        if (merchantVO == null) {
            return null;
        }

        // 查询管理员列表
        List<AdminVO> adminVOs = adminMapper.selectVoList(
            Wrappers.<MerchantAdministratorDO>lambdaQuery()
                .eq(MerchantAdministratorDO::getMerchantId, merchantId)
                .eq(MerchantAdministratorDO::getStatus, 1)
                .orderByAsc(MerchantAdministratorDO::getCreateTime)
        );

        // 组装详情VO
        MerchantDetailVO detailVO = MapstructUtils.convert(merchantVO, MerchantDetailVO.class);
        detailVO.setAdministrators(adminVOs);

        return detailVO;
    }

    // 批量查询商户及管理员数量
    public List<MerchantWithCountVO> getMerchantsWithAdminCount() {
        // 先查询所有商户
        List<MerchantVO> merchants = merchantMapper.selectVoList(
            Wrappers.<MerchantDO>lambdaQuery()
                .orderByDesc(MerchantDO::getCreateTime)
        );

        if (CollectionUtils.isEmpty(merchants)) {
            return Collections.emptyList();
        }

        // 提取商户ID列表
        List<Long> merchantIds = merchants.stream()
            .map(MerchantVO::getId)
            .collect(Collectors.toList());

        // 批量查询每个商户的管理员数量
        Map<Long, Long> adminCountMap = adminMapper.selectList(
                Wrappers.<MerchantAdministratorDO>lambdaQuery()
                    .in(MerchantAdministratorDO::getMerchantId, merchantIds)
                    .eq(MerchantAdministratorDO::getStatus, 1)
            ).stream()
            .collect(Collectors.groupingBy(
                MerchantAdministratorDO::getMerchantId,
                Collectors.counting()
            ));

        // 组装结果
        return merchants.stream()
            .map(merchant -> {
                MerchantWithCountVO vo = MapstructUtils.convert(merchant, MerchantWithCountVO.class);
                vo.setAdminCount(adminCountMap.getOrDefault(merchant.getId(), 0L));
                return vo;
            })
            .collect(Collectors.toList());
    }
}
```

## 事务与异常处理

### 1. 事务管理示例

```java
@Service
public class MerchantTransactionService {
    
    @Autowired
    private MerchantMapper merchantMapper;
    
    @Autowired
    private MerchantAdministratorMapper adminMapper;
    
    // 创建商户和管理员（事务）
    @Transactional(rollbackFor = Exception.class)
    public MerchantVO createMerchantWithAdmin(CreateMerchantCommand command) {
        try {
            // 1. 创建商户
            MerchantDO merchantDO = MapstructUtils.convert(command, MerchantDO.class);
            merchantDO.setStatus(1); // 待审核
            merchantMapper.insert(merchantDO);
            
            // 2. 创建默认管理员
            MerchantAdministratorDO adminDO = new MerchantAdministratorDO();
            adminDO.setMerchantId(merchantDO.getId());
            adminDO.setEmail(command.getEmail());
            adminDO.setUsername(command.getCompanyName() + "管理员");
            adminDO.setPassword(command.getPassword()); // 实际应用中需要加密
            adminDO.setStatus(1);
            adminMapper.insert(adminDO);
            
            // 3. 返回结果
            return merchantMapper.selectVoById(merchantDO.getId());
            
        } catch (Exception e) {
            log.error("创建商户失败", e);
            throw new BusinessException("创建商户失败：" + e.getMessage());
        }
    }
    
    // 批量创建（事务）
    @Transactional(rollbackFor = Exception.class)
    public List<MerchantVO> batchCreateMerchants(List<CreateMerchantCommand> commands) {
        List<Long> merchantIds = new ArrayList<>();
        
        try {
            for (CreateMerchantCommand command : commands) {
                MerchantDO merchantDO = MapstructUtils.convert(command, MerchantDO.class);
                merchantMapper.insert(merchantDO);
                merchantIds.add(merchantDO.getId());
            }
            
            // 批量查询返回结果
            return merchantMapper.selectVoByIds(merchantIds);
            
        } catch (Exception e) {
            log.error("批量创建商户失败", e);
            throw new BusinessException("批量创建失败：" + e.getMessage());
        }
    }
}
```

### 2. 异常处理最佳实践

```java
@ControllerAdvice
public class MerchantExceptionHandler {
    
    @ExceptionHandler(DuplicateKeyException.class)
    public ResponseEntity<String> handleDuplicateKey(DuplicateKeyException e) {
        if (e.getMessage().contains("email")) {
            return ResponseEntity.badRequest().body("邮箱已存在");
        }
        return ResponseEntity.badRequest().body("数据重复");
    }
    
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<String> handleBusinessException(BusinessException e) {
        return ResponseEntity.badRequest().body(e.getMessage());
    }
}
```

## 测试用例示例

### 1. Mapper测试

```java

@SpringBootTest
@Transactional
class MerchantMapperTest {

    @Autowired
    private MerchantMapper merchantMapper;

    @Test
    void testSelectVoById() {
        // Given
        MerchantDO merchantDO = createTestMerchant();
        merchantMapper.insert(merchantDO);

        // When
        MerchantVO vo = merchantMapper.selectVoById(merchantDO.getId());

        // Then
        assertThat(vo).isNotNull();
        assertThat(vo.getId()).isEqualTo(merchantDO.getId());
        assertThat(vo.getEmail()).isEqualTo(merchantDO.getEmail());
    }

    @Test
    void testBatchInsert() {
        // Given
        List<MerchantDO> merchants = createTestMerchants(5);

        // When
        boolean success = merchantMapper.insertBatch(merchants);

        // Then
        assertThat(success).isTrue();
        assertThat(merchantMapper.selectCount(null)).isEqualTo(5);
    }

    @Test
    void testSelectVoPage() {
        // Given
        createTestMerchants(15).forEach(merchantMapper::insert);

        // When
        Page<MerchantVO> page = merchantMapper.selectVoPage(
            new Page<>(1, 10),
            Wrappers.<MerchantDO>lambdaQuery()
                .orderByDesc(MerchantDO::getCreateTime)
        );

        // Then
        assertThat(page.getRecords()).hasSize(10);
        assertThat(page.getTotal()).isEqualTo(15);
    }
}
```

### 2. 转换器测试

```java

@SpringBootTest
class MapstructUtilsTest {

    @Test
    void testConvertSingleObject() {
        // Given
        MerchantDO merchantDO = createTestMerchantDO();

        // When
        MerchantVO vo = MapstructUtils.convert(merchantDO, MerchantVO.class);

        // Then
        assertThat(vo).isNotNull();
        assertThat(vo.getId()).isEqualTo(merchantDO.getId());
        assertThat(vo.getEmail()).isEqualTo(merchantDO.getEmail());
    }

    @Test
    void testConvertList() {
        // Given
        List<MerchantDO> merchants = createTestMerchants(3);

        // When
        List<MerchantVO> vos = MapstructUtils.convert(merchants, MerchantVO.class);

        // Then
        assertThat(vos).hasSize(3);
        assertThat(vos.get(0).getId()).isEqualTo(merchants.get(0).getId());
    }

    @Test
    void testPerformance() {
        // Given
        List<MerchantDO> merchants = createTestMerchants(1000);

        // When
        long start = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            MapstructUtils.convert(merchants, MerchantVO.class);
        }
        long end = System.currentTimeMillis();

        // Then
        System.out.println("转换1000个对象100次耗时: " + (end - start) + "ms");
        assertThat(end - start).isLessThan(1000); // 应该在1秒内完成
    }
}
```

这些示例展示了如何充分利用im-common-mybatis提供的增强功能，实现高效、简洁的数据访问层代码。 