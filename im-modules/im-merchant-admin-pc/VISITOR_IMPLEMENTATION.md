# 游客功能实现说明

基于PHP源码的游客功能，已转换为Java实现，采用DDD（领域驱动设计）架构。

## 文件结构

### 领域层 (Domain Layer)

#### 实体类

- `domain/model/Visitor.java` - 游客实体类
    - 包含游客的所有属性和业务方法
    - 实现了游客状态管理（在线/离线、拉黑/正常、置顶/取消置顶）
    - 提供了创建游客、更新访问信息等方法

#### 值对象

- `domain/valueobject/DeviceInfo.java` - 设备信息值对象
- `domain/valueobject/BrowserInfo.java` - 浏览器信息值对象
- `domain/valueobject/UtmInfo.java` - UTM信息值对象

#### 仓储接口

- `domain/repository/VisitorRepository.java` - 游客仓储接口
    - 定义了所有游客相关的数据访问方法
    - 支持按各种条件查询游客
    - 提供统计和分页功能

#### 领域服务

- `domain/service/VisitorService.java` - 游客领域服务
    - 实现了游客连接处理逻辑（基于PHP的notice方法）
    - 处理游客状态变更
    - 提供游客查找和创建功能

### 应用层 (Application Layer)

#### DTO类

- `application/dto/VisitorConnectionRequest.java` - 游客连接请求DTO
- `application/dto/VisitorResponse.java` - 游客响应DTO

#### 应用服务

- `application/service/VisitorApplicationService.java` - 游客应用服务
    - 协调领域服务和外部系统
    - 处理DTO转换
    - 提供业务用例实现

### 接口层 (Interface Layer)

#### 控制器

- `ctrl/VisitorCtrl.java` - 游客控制器
    - 提供RESTful API接口
    - 处理HTTP请求和响应
    - 实现IP地址获取逻辑

#### 请求类

- `request/VisitorBlockRequest.java` - 游客拉黑请求
- `request/VisitorUpdateRequest.java` - 游客更新请求

## 核心功能

### 1. 游客连接处理

基于PHP的`notice`方法实现，主要逻辑：

- 检查游客是否已存在（根据visitorId和businessId）
- 老用户：更新访问信息和状态
- 新用户：创建游客记录
- 支持Cookie和浏览器指纹识别

### 2. 游客状态管理

- 在线/离线状态切换
- 拉黑/取消拉黑功能
- 置顶/取消置顶功能
- 访问次数统计

### 3. 游客信息管理

- 基本信息更新（姓名、电话、邮箱）
- 设备信息记录
- 浏览器信息记录
- UTM参数跟踪

### 4. 查询和统计

- 在线游客查询
- 被拉黑游客查询
- 置顶游客查询
- 分页查询
- 时间范围查询
- 关键词搜索
- 各种统计功能

## API接口

### 游客连接

```
POST /visitor/connect
```

### 游客离线

```
POST /visitor/offline
```

### 更新消息时间

```
POST /visitor/update-message-time
```

### 拉黑/取消拉黑

```
POST /visitor/block
POST /visitor/unblock
```

### 置顶/取消置顶

```
POST /visitor/top
POST /visitor/untop
```

### 更新游客信息

```
POST /visitor/update-info
```

### 查询接口

```
GET /visitor/online-count
GET /visitor/today-new-count
GET /visitor/online-list
GET /visitor/blocked-list
GET /visitor/top-list
GET /visitor/search
GET /visitor/list
```

## 数据库设计

基于提供的建表SQL，visitor表包含以下主要字段：

- 基本信息：visitor_id, business_id, visitor_name, real_name, phone, email
- 状态信息：status, is_blocked, is_top, login_count
- 时间信息：last_visit_time, last_message_time, created_time, updated_time
- 技术信息：ip_address, user_agent, cookie_id, fingerprint
- 营销信息：utm_source, utm_medium, utm_campaign

## 与PHP实现的对应关系

| PHP功能              | Java实现                                     | 说明       |
|--------------------|--------------------------------------------|----------|
| Event.php notice() | VisitorService.handleVisitorConnection()   | 游客连接处理   |
| 游客状态更新             | Visitor.setOnline()/setOffline()           | 状态管理     |
| 游客信息更新             | VisitorService.updateVisitorInfo()         | 信息更新     |
| 拉黑功能               | VisitorService.blockVisitor()              | 拉黑管理     |
| 置顶功能               | VisitorService.topVisitor()                | 置顶管理     |
| Cookie防重           | VisitorService.findOrCreateByCookieId()    | Cookie识别 |
| 指纹识别               | VisitorService.findOrCreateByFingerprint() | 指纹识别     |

## 技术特点

1. **DDD架构**：清晰的分层结构，职责分离
2. **领域驱动**：业务逻辑集中在领域层
3. **类型安全**：使用强类型，减少运行时错误
4. **可测试性**：依赖注入，便于单元测试
5. **可扩展性**：接口设计，便于扩展实现
6. **RESTful API**：标准的HTTP接口设计

## 使用示例

```java
// 游客连接
VisitorConnectionRequest request = new VisitorConnectionRequest();
request.

setVisitorId("visitor123");
request.

setBusinessId(1L);
request.

setVisitorName("张三");
request.

setFromUrl("https://example.com");

VisitorResponse response = visitorApplicationService.handleVisitorConnection(request, "192.168.1.1");

// 拉黑游客
visitorApplicationService.

blockVisitor("visitor123",1L);

// 查询在线游客
List<VisitorResponse> onlineVisitors = visitorApplicationService.getOnlineVisitors(1L);
``` 