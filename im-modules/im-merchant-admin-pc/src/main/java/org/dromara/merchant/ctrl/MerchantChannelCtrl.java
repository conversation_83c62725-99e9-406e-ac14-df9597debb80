package org.dromara.merchant.ctrl;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImMerchantChannelBo;
import org.dromara.im.domain.vo.ImChannelConfigVo;
import org.dromara.im.domain.vo.ImMerchantChannelVo;
import org.dromara.im.service.IImChannelConfigService;
import org.dromara.im.service.IImMerchantChannelService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 商户接入渠道管理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/merchantChannel")
public class MerchantChannelCtrl extends BaseController {
    private final IImMerchantChannelService imMerchantChannelService;

    private final IImChannelConfigService imChannelConfigService;

    /**
     * 查询商户接入渠道列表
     */
    @GetMapping("/paging")
    public R<?> paging(ImMerchantChannelBo bo, PageQuery pageQuery) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        bo.setDefaultChannel("0");
        return R.ok(imMerchantChannelService.queryPageList(bo, pageQuery));
    }

    /**
     * 查询商户接入渠道列表
     */
    @GetMapping("/list")
    public R<?> list(ImMerchantChannelBo bo, PageQuery pageQuery) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return R.ok(imMerchantChannelService.queryPageList(bo, pageQuery));
    }



    /**
     * 获取商户接入渠道详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ImMerchantChannelVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long id) {
        return R.ok(imMerchantChannelService.queryById(id));
    }

    /**
     * 新增商户接入渠道
     */
    @Log(title = "商户接入渠道", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<?> add(@Validated(AddGroup.class) @RequestBody ImMerchantChannelBo bo) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        executeChannelConfig(bo);
        bo.setAccessStatus("1");
        Long merchantChannelId = imMerchantChannelService.insertByBo(bo);
        return R.ok(merchantChannelId);
    }

    private void executeChannelConfig(ImMerchantChannelBo bo) {
        ImChannelConfigVo imChannelConfigVo = imChannelConfigService.queryById(bo.getChannelConfigId());
        Optional.ofNullable(imChannelConfigVo).ifPresentOrElse(imChannelConfig -> {
            bo.setChannelType(imChannelConfig.getChannelType());
            bo.setChannelCode(imChannelConfig.getChannelCode());
            bo.setChannelName(imChannelConfig.getChannelName());
        }, () -> {
            throw new RuntimeException("渠道配置不存在");
        });
    }

    /**
     * 修改商户接入渠道
     */
    @Log(title = "商户接入渠道", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImMerchantChannelBo bo) {
        executeChannelConfig(bo);
        return toAjax(imMerchantChannelService.updateByBo(bo));
    }

    /**
     * 商户更新渠道IP&Lang
     */
    @Log(title = "商户更新渠道IP&Lang", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/updateIpAndLang")
    public R<Void> updateIpAndLang(@RequestBody ImMerchantChannelBo bo) {
        return toAjax(imMerchantChannelService.updateByBo(bo));
    }


    /**
     * 删除商户接入渠道
     *
     * @param ids 主键串
     */
    @Log(title = "商户接入渠道", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(imMerchantChannelService.deleteWithValidByIds(List.of(ids), true));
    }
}
