package org.dromara.merchant.domain.valueobject;

import lombok.Getter;

@Getter
public class ChatPackage {

    /**
     * 套餐等级;0->体验套餐;1->基础套餐;2->标准套餐;3->高级套餐;4->企业套餐
     */

    private final String level;

    /**
     * 有效期
     */
    private final Long expiredTime;

    /**
     * 描述
     */
    private final String description;

    /**
     * 价格
     */
    private final String price;

    /**
     * 对话数量
     */
    private final Integer chatNumber;

    /**
     * 渠道数量
     */
    private final Integer channelNumber;

    /**
     * 客服数量
     */
    private final Integer customerNumber;


    public ChatPackage(String level, Long expiredTime, String description, String price, Integer chatNumber, Integer channelNumber, Integer customerNumber) {
        this.level = level;
        this.expiredTime = expiredTime;
        this.description = description;
        this.price = price;
        this.chatNumber = chatNumber;
        this.channelNumber = channelNumber;
        this.customerNumber = customerNumber;
    }
}
