package org.dromara.merchant.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * 添加管理员命令
 *
 * <AUTHOR>
 */
@Data
public class AddAdministratorCommand {

    private Long adminId;
    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 管理员姓名
     */
    @NotBlank(message = "昵称姓名不能为空")
    private String nickname;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Pattern(regexp = "^.{6,18}$", message = "密码长度必须在6-18位之间")
    private String password;

    /**
     * 角色类型;1->主管理员;2->子管理员;3->客服
     */
    private String roleType;

    /**
     * 所属分组ID
     */
    @NotNull(message = "分组不能为空")
    private Long memberGroupId;

    /**
     * 所属渠道ID（逗号拼接的字符串）
     */
//    @NotNull(message = "渠道不能为空")
    private List<Long> merchantChannelIdList;
}
