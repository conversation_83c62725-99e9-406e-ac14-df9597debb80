package org.dromara.merchant.ctrl;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImMemberGroupBo;
import org.dromara.im.domain.vo.ImMemberGroupVo;
import org.dromara.im.service.IImMemberGroupService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 成员分组
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/memberGroup")
public class MemberGroupCtrl extends BaseController {

    private final IImMemberGroupService imMemberGroupService;

    /**
     * 查询成员分组列表
     */
    @GetMapping("/list")
    public R<?> list(ImMemberGroupBo bo, PageQuery pageQuery) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return R.ok(imMemberGroupService.queryPageList(bo, pageQuery));
    }

    /**
     * 获取成员分组详细信息
     *
     * @param memberGroupId 主键
     */
    @GetMapping("/{memberGroupId}")
    public R<ImMemberGroupVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long memberGroupId) {
        return R.ok(imMemberGroupService.queryById(memberGroupId));
    }

    /**
     * 新增成员分组
     */
    @Log(title = "成员分组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImMemberGroupBo bo) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return toAjax(imMemberGroupService.insertByBo(bo));
    }

    /**
     * 修改成员分组
     */
    @Log(title = "成员分组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImMemberGroupBo bo) {
        return toAjax(imMemberGroupService.updateByBo(bo));
    }

    /**
     * 删除成员分组
     *
     * @param memberGroupIds 主键串
     */
    @Log(title = "成员分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{memberGroupIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] memberGroupIds) {
        return toAjax(imMemberGroupService.deleteWithValidByIds(List.of(memberGroupIds), true));
    }
}
