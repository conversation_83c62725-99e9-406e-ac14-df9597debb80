package org.dromara.merchant.ctrl;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImCustomLabelBo;
import org.dromara.im.domain.vo.ImCustomLabelVo;
import org.dromara.im.service.IImCustomLabelService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户标签
 *
 * <AUTHOR> Li
 * @date 2025-07-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/customLabel")
public class CustomLabelCtrl extends BaseController {

    private final IImCustomLabelService imCustomLabelService;

    /**
     * 查询用户标签列表
     */
    @GetMapping("/list")
    public R<?> list(ImCustomLabelBo bo, PageQuery pageQuery) {
        return R.ok(imCustomLabelService.queryPageList(bo, pageQuery));
    }

    /**
     * 获取用户标签详细信息
     *
     * @param customLabelId 主键
     */
    @GetMapping("/{customLabelId}")
    public R<ImCustomLabelVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long customLabelId) {
        return R.ok(imCustomLabelService.queryById(customLabelId));
    }

    /**
     * 新增用户标签
     */
    @Log(title = "用户标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImCustomLabelBo bo) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return toAjax(imCustomLabelService.insertByBo(bo));
    }

    /**
     * 修改用户标签
     */
    @Log(title = "用户标签", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImCustomLabelBo bo) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return toAjax(imCustomLabelService.updateByBo(bo));
    }

    /**
     * 删除用户标签
     *
     * @param customLabelIds 主键串
     */
    @Log(title = "用户标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{customLabelIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] customLabelIds) {
        return toAjax(imCustomLabelService.deleteWithValidByIds(List.of(customLabelIds), true));
    }
}
