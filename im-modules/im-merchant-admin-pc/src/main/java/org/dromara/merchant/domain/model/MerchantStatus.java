package org.dromara.merchant.domain.model;

import lombok.Getter;

/**
 * 商户状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum MerchantStatus {

    /**
     * 待审核 - 商户刚注册，等待审核
     */
    PENDING("待审核"),

    /**
     * 活跃 - 商户已激活，正常使用
     */
    ACTIVE("活跃"),

    /**
     * 暂停 - 商户被暂停，无法使用服务
     */
    SUSPENDED("暂停"),

    /**
     * 已注销 - 商户已注销
     */
    DEACTIVATED("已注销");

    private final String description;

    MerchantStatus(String description) {
        this.description = description;
    }

}
