package org.dromara.merchant.dto;

import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;

import java.util.Collections;
import java.util.Set;

/**
 * 商户登录用户信息
 *
 * <AUTHOR>
 */
public class MerchantLoginUser extends LoginUser {

    private static final String MERCHANT_USER_TYPE = "merchant_user";

    public MerchantLoginUser() {
        super();
    }

    /**
     * 基于商户和管理员信息创建登录用户
     */
    public static MerchantLoginUser create(ImMerchantAdministratorVo admin) {
        MerchantLoginUser loginUser = new MerchantLoginUser();

        // 设置基础信息
        loginUser.setUserId(admin.getAdminId());
        loginUser.setUsername(admin.getNickname());
        loginUser.setNickname(admin.getNickname());
        loginUser.setUserType(MERCHANT_USER_TYPE);

        // 设置商户相关信息
        loginUser.setDeptId(admin.getMerchantId());
        loginUser.setDeptCategory("merchant");

        // 设置时间信息
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(System.currentTimeMillis() + 365 * 24 * 60 * 60 * 1000L); // 365天

        // 设置权限信息（根据角色类型）
        if (admin.getRoleType() != null) {
            loginUser.setMenuPermission(Set.of(admin.getRoleType()));
            loginUser.setRolePermission(Collections.singleton(admin.getRoleType()));
        } else {
            // 默认权限（兼容性处理）
            loginUser.setMenuPermission(Collections.singleton("merchant:*"));
            loginUser.setRolePermission(Collections.singleton("merchant_admin"));
        }

        return loginUser;
    }

    /**
     * 获取登录ID（商户用户类型:管理员ID）
     */
    @Override
    public String getLoginId() {
        return MERCHANT_USER_TYPE + ":" + getUserId();
    }
}
