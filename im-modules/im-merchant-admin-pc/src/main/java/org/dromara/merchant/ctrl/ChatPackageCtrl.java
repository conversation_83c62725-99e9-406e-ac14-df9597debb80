package org.dromara.merchant.ctrl;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImChatPackageBo;
import org.dromara.im.service.IImChatPackageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 付费信息管理
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/chatPackage")
public class ChatPackageCtrl extends BaseController {

    private final IImChatPackageService imChatPackageService;

    /**
     * 查询套餐列表
     */
    @GetMapping("/list")
    public R<?> list(ImChatPackageBo bo) {
        bo.setStatus("1");
        return R.ok(imChatPackageService.queryList(bo));
    }


//    /**
//     * 获取套餐详细信息
//     *
//     * @param id 主键
//     */
//    @GetMapping("/{id}")
//    public R<ImChatPackageVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long id) {
//        return R.ok(imChatPackageService.queryById(id));
//    }

//    /**
//     * 新增套餐
//     */
//    @Log(title = "套餐", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImChatPackageBo bo) {
//        return toAjax(imChatPackageService.insertByBo(bo));
//    }
//
//    /**
//     * 修改套餐
//     */
//    @Log(title = "套餐", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImChatPackageBo bo) {
//        return toAjax(imChatPackageService.updateByBo(bo));
//    }
//
//    /**
//     * 删除套餐
//     *
//     * @param ids 主键串
//     */
//    @Log(title = "套餐", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] ids) {
//        return toAjax(imChatPackageService.deleteWithValidByIds(List.of(ids), true));
//    }
}
