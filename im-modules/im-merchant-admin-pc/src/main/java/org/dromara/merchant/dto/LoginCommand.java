package org.dromara.merchant.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 登录命令
 *
 * <AUTHOR>
 */
@Data
public class LoginCommand {

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    @NotBlank(message = "密码不能为空")
    private String password;

    public LoginCommand() {
    }

    public LoginCommand(String email, String password) {
        this.email = email;
        this.password = password;
    }

}
