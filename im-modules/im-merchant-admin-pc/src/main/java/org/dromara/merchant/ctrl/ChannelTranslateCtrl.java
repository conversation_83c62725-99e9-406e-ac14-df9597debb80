package org.dromara.merchant.ctrl;

import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImChannelTranslateBo;
import org.dromara.im.service.IImChannelTranslateService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 渠道翻译设置
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/channelTranslate")
public class ChannelTranslateCtrl extends BaseController {

    private final IImChannelTranslateService imChannelTranslateService;

    /**
     * 查询渠道翻译设置列表
     */
    @GetMapping("/list")
    public R<?> list(ImChannelTranslateBo bo) {
        return R.ok(imChannelTranslateService.queryList(bo));
    }

    /**
     * 新增渠道翻译设置
     */
    @Log(title = "渠道翻译设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImChannelTranslateBo bo) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return toAjax(imChannelTranslateService.insertByBo(bo));
    }

    /**
     * 修改渠道翻译设置
     */
    @Log(title = "渠道翻译设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImChannelTranslateBo bo) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return toAjax(imChannelTranslateService.updateByBo(bo));
    }

    /**
     * 删除渠道翻译设置
     *
     * @param channelTranslateIds 主键串
     */
    @Log(title = "渠道翻译设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{channelTranslateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] channelTranslateIds) {
        return toAjax(imChannelTranslateService.deleteWithValidByIds(List.of(channelTranslateIds), true));
    }


}
