package org.dromara.merchant.ctrl;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImQuickRepliesBo;
import org.dromara.im.domain.bo.ImQuickRepliesGroupBo;
import org.dromara.im.domain.vo.ImQuickRepliesGroupVo;
import org.dromara.im.domain.vo.ImQuickRepliesVo;
import org.dromara.im.service.IImQuickRepliesGroupService;
import org.dromara.im.service.IImQuickRepliesService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 回复列表
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/quickReplies")
public class QuickRepliesCtrl extends BaseController {

    private final IImQuickRepliesService imQuickRepliesService;

    private final IImQuickRepliesGroupService imQuickRepliesGroupService;

    /**
     * 查询团队快捷回复列表
     */
    @GetMapping("/list")
    public R<?> list(ImQuickRepliesBo bo, PageQuery pageQuery) {
        if (StringUtils.equals(bo.getType(), "0")) {
            bo.setAdminId(MerchantLoginHelper.getAdminId());
        } else {
            bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        }
        TableDataInfo<ImQuickRepliesVo> data = imQuickRepliesService.queryPageList(bo, pageQuery);
        // 处理分组名称
        List<ImQuickRepliesVo> rows = data.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            List<Long> list = rows.stream().map(ImQuickRepliesVo::getQuickRepliesGroupId)
                .distinct()
                .toList();
            List<ImQuickRepliesGroupVo> imQuickRepliesGroupVos = imQuickRepliesGroupService.queryByIds(list);
            Map<Long, ImQuickRepliesGroupVo> collect = imQuickRepliesGroupVos.stream().collect(Collectors.toMap(ImQuickRepliesGroupVo::getQuickRepliesGroupId, Function.identity()));
            for (ImQuickRepliesVo row : rows) {
                ImQuickRepliesGroupVo imQuickRepliesGroupVo = collect.get(row.getQuickRepliesGroupId());
                if (Objects.nonNull(imQuickRepliesGroupVo)) {
                    row.setQuickRepliesGroupName(imQuickRepliesGroupVo.getQuickRepliesGroupName());
                }
            }
        }
        return R.ok(data);
    }

    /**
     * 获取团队快捷回复详细信息
     *
     * @param quickRepliesId 主键
     */
    @GetMapping("/{quickRepliesId}")
    public R<ImQuickRepliesVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long quickRepliesId) {
        return R.ok(imQuickRepliesService.queryById(quickRepliesId));
    }

    /**
     * 新增团队快捷回复
     */
    @Log(title = "团队快捷回复", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImQuickRepliesBo bo) {
        if (StringUtils.equals(bo.getType(), "0")) {
            bo.setAdminId(MerchantLoginHelper.getAdminId());
        } else {
            bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        }
        return toAjax(imQuickRepliesService.insertByBo(bo));
    }

    /**
     * 修改团队快捷回复
     */
    @Log(title = "团队快捷回复", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImQuickRepliesBo bo) {
        return toAjax(imQuickRepliesService.updateByBo(bo));
    }

    /**
     * 删除团队快捷回复
     *
     * @param quickRepliesIds 主键串
     */
    @Log(title = "团队快捷回复", businessType = BusinessType.DELETE)
    @DeleteMapping("/{quickRepliesIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] quickRepliesIds) {
        return toAjax(imQuickRepliesService.deleteWithValidByIds(List.of(quickRepliesIds), true));
    }

    @GetMapping("/teamQuickReply")
    public R<?> teamQuickReply() {
        ImQuickRepliesGroupBo bo = new ImQuickRepliesGroupBo();
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        List<ImQuickRepliesGroupVo> data = queryImQuickRepliesGroupVos(bo);
        return R.ok(data);
    }

    @GetMapping("/personalQuickReply")
    public R<?> personalQuickReply() {
        ImQuickRepliesGroupBo bo = new ImQuickRepliesGroupBo();
        bo.setAdminId(MerchantLoginHelper.getAdminId());
        List<ImQuickRepliesGroupVo> data = queryImQuickRepliesGroupVos(bo);
        return R.ok(data);
    }

    private List<ImQuickRepliesGroupVo> queryImQuickRepliesGroupVos(ImQuickRepliesGroupBo bo) {
        List<ImQuickRepliesGroupVo> data = imQuickRepliesGroupService.queryList(bo);
        List<Long> quickRepliesGroupIds = data.stream()
            .map(ImQuickRepliesGroupVo::getQuickRepliesGroupId)
            .distinct()
            .toList();

        if (CollectionUtils.isEmpty(quickRepliesGroupIds)) {
            return data;
        }
        List<ImQuickRepliesVo> imQuickRepliesVos = imQuickRepliesService.queryList(quickRepliesGroupIds);
        Map<Long, List<ImQuickRepliesVo>> collect = imQuickRepliesVos.stream()
            .collect(Collectors.groupingBy(ImQuickRepliesVo::getQuickRepliesGroupId));
        for (ImQuickRepliesGroupVo datum : data) {
            datum.setRepliesVoList(collect.get(datum.getQuickRepliesGroupId()));
        }
        return data;
    }
}
