package org.dromara.merchant.domain.model;

import lombok.Data;
import org.dromara.im.constans.MerchantRoleType;
import org.dromara.im.utils.Password;
import org.dromara.merchant.domain.valueobject.ChatPackage;
import org.dromara.merchant.domain.valueobject.MerchantEmail;

import java.time.LocalDateTime;

/**
 * 商户管理员实体（简化版）
 *
 * <AUTHOR>
 */
@Data
public class MerchantAdministrator {

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 管理员邮箱（唯一标识）
     */
    private MerchantEmail email;

    /**
     * 头像
     */
    private String avatar;


    /**
     * 管理员姓名
     */
    private String nickname;

    /**
     * 密码
     */
    private Password password;

    /**
     * 管理员状态
     */
    private AdminStatus status;

    /**
     * 所属商户ID
     */
    private Long merchantId;

    /**
     * 角色类型
     */
    private MerchantRoleType roleType;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 服务套餐
     */
    private ChatPackage chatPackage;

    /**
     * 所属分组ID
     */
    private Long memberGroupId;

    /**
     * 所属渠道ID（逗号拼接的字符串）
     */
    private String channelId;

    private String channelName;

    /**
     * 私有构造函数
     */
    private MerchantAdministrator() {
    }


}
