package org.dromara.merchant.ctrl;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.constans.ConversationConstants;
import org.dromara.im.domain.bo.*;
import org.dromara.im.domain.dto.MessagePromptDto;
import org.dromara.im.domain.vo.*;
import org.dromara.im.service.*;
import org.dromara.merchant.dto.AddAdministratorCommand;
import org.dromara.merchant.dto.AuthenticationResult;
import org.dromara.merchant.dto.LoginCommand;
import org.dromara.merchant.dto.SimpleRegisterCommand;
import org.dromara.merchant.application.service.MerchantApplicationService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.dromara.im.domain.dto.AutoReplyDTO;
import org.dromara.merchant.request.BindCustomerServiceRequest;
import org.dromara.merchant.request.ModifyInfoRequest;
import org.dromara.merchant.request.ModifyPasswordRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商户管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/merchants")
public class MerchantAdminCtrl extends BaseController {

    @Autowired
    private MerchantApplicationService merchantApplicationService;

    @Autowired
    private IImMerchantAdministratorService imMerchantAdministratorService;

    @Autowired
    private IImMerchantChannelRelationService imMerchantChannelRelationService;

    @Autowired
    private IImMerchantChannelService iImMerchantChannelService;

    @Autowired
    private IImAdminAutoReplyService iImAdminAutoReplyService;

    @Autowired
    private IImVisitorService imVisitorService;

    @Autowired
    private IImVisitorLabelService imVisitorLabelService;

    @Autowired
    private IImCustomLabelService customLabelService;

    @Autowired
    private IImConversationService iImConversationService;


    /**
     * 商户注册
     */
    @PostMapping("/register")
    public R<AuthenticationResult> register(@Valid @RequestBody SimpleRegisterCommand command) {
        try {
            AuthenticationResult result = merchantApplicationService.register(command);
            return R.ok(result);
        } catch (IllegalArgumentException e) {
            log.error(e.getMessage(), e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public R<AuthenticationResult> login(@Valid @RequestBody LoginCommand command) {
        try {
            AuthenticationResult result = merchantApplicationService.login(command);
            return R.ok(result);
        } catch (IllegalArgumentException e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public R<Void> logout() {
        try {
            MerchantLoginHelper.logout();
            return R.ok();
        } catch (Exception e) {
            return R.fail("登出失败：" + e.getMessage());
        }
    }

    /**
     * 添加管理员（子管理员/客服）
     */
    @PostMapping("/administrators")
    public R<Void> addAdministrator(@Valid @RequestBody AddAdministratorCommand command) {
        try {
            merchantApplicationService.addAdministrator(command);
            return R.ok();
        } catch (IllegalArgumentException e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 用户详情
     */
    @GetMapping("/detail")
    public R<ImMerchantAdministratorVo> detail() {
        try {
            Long adminId = MerchantLoginHelper.getAdminId();
            ImMerchantAdministratorVo detail = merchantApplicationService.detailAdmin(adminId);
            if (detail.isMainAdmin()) {
                ImMerchantVo imMerchantVo = merchantApplicationService.queryMerchantInfo(detail.getMerchantId());
                detail.setChannelNumber(imMerchantVo.getChannelNumber());
                detail.setChatNumber(imMerchantVo.getChatNumber());
                detail.setSeatsCount(imMerchantVo.getSeatsCount());
                detail.setTranslateNumber(imMerchantVo.getTranslateNumber());
                detail.setUseTranslateNumber(imMerchantVo.getUseTranslateNumber());
                detail.setExpiredTime(imMerchantVo.getExpiredTime());
                detail.setPackageType(imMerchantVo.getPackageType());


                ImMerchantChannelBo bo = new ImMerchantChannelBo();
                bo.setMerchantId(detail.getMerchantId());
                bo.setDefaultChannel("0");
                List<ImMerchantChannelVo> imMerchantChannelVos = iImMerchantChannelService.queryList(bo);
                detail.setConnectedChannelNumber((long) imMerchantChannelVos.size());
            }
            return R.ok(detail);
        } catch (Exception e) {
            return R.fail("登出失败：" + e.getMessage());
        }
    }


    /**
     * 成员列表
     */
    @GetMapping("/list")
    public R<?> list(ImMerchantAdministratorBo bo, PageQuery pageQuery) {
        Long merchantId = MerchantLoginHelper.getMerchantId();
        bo.setMerchantId(merchantId);
        return R.ok(imMerchantAdministratorService.queryPageList(bo, pageQuery));
    }


    /**
     * 删除成员
     *
     * @param ids 主键串
     */
    @Log(title = "成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(imMerchantAdministratorService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 编辑成员
     */
    @Log(title = "成员", businessType = BusinessType.UPDATE)
    @PostMapping("/updateAdministrator")
    public R<Void> updateAdministrator(@RequestBody AddAdministratorCommand command) {
        return R.ok();
    }


    /**
     * 更换管理员
     */
    @PostMapping(value = "/modifyTeam/{adminId}")
    public R<?> modifyTeam(@PathVariable("adminId") Long adminId) {
        Long currentAdminId = MerchantLoginHelper.getAdminId();
        merchantApplicationService.modifyTeam(adminId, currentAdminId);
        return R.ok();
    }

    /**
     * 退出团队
     * 只有普通用户（非主管理员）才可以退出团队
     */
    @GetMapping("/quitTeam")
    public R<?> quitTeam() {
        try {
            Long adminId = MerchantLoginHelper.getAdminId();
            merchantApplicationService.quitTeam(adminId);
            // 退出团队成功后，注销当前登录状态
            MerchantLoginHelper.logout();
            return R.ok();
        } catch (IllegalArgumentException e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 修改个人信息
     */
    @PostMapping(value = "/modifyInfo")
    public R<?> modifyInfo(@RequestBody ModifyInfoRequest request) {
        if (StringUtils.isEmpty(request.getNickname())
            && StringUtils.isEmpty(request.getAvatar())
            && StringUtils.isEmpty(request.getEmail())
        ) {
            return R.fail("昵称和头像不能同时为空");
        }
        merchantApplicationService.modifyInfo(request, MerchantLoginHelper.getAdminId());
        return R.ok();
    }

    /**
     * 修改密码
     */
    @PostMapping(value = "/modifyPassword")
    public R<?> modifyPassword(@RequestBody ModifyPasswordRequest request) {
        merchantApplicationService.modifyPassword(request);
        return R.ok();
    }


    /**
     * 查询消息提示设置
     */
    @GetMapping(value = "/queryMessagePrompt")
    public R<?> queryMessagePrompt() {
        // 这里应该返回当前用户的消息提示设置
        // 暂时返回默认设置
        Long adminId = MerchantLoginHelper.getAdminId();
        ImMerchantAdministratorVo detail = merchantApplicationService.detailAdmin(adminId);
        detail.buildMessagePrompt();
        return R.ok(detail.getMessagePromptDto());
    }

    /**
     * 保存消息提示设置
     */
    @PostMapping(value = "/saveMessagePrompt")
    public R<?> saveMessagePrompt(@RequestBody MessagePromptDto promptDto) {
        merchantApplicationService.saveMessagePrompt(promptDto);
        return R.ok();
    }

    /**
     * 查询自动回复设置
     */
    @GetMapping(value = "/queryAutoReply/{merchantChannelId}")
    public R<?> queryAutoReply(@PathVariable Long merchantChannelId) {
        // 这里应该返回当前用户的自动回复设置
        ImAdminAutoReplyBo bo = new ImAdminAutoReplyBo();
        bo.setAdminId(MerchantLoginHelper.getAdminId());
        bo.setMerchantChannelId(merchantChannelId);
        ImAdminAutoReplyVo imAdminAutoReplyVo = iImAdminAutoReplyService.queryOne(bo);
        if (Objects.nonNull(imAdminAutoReplyVo)) {
            AutoReplyDTO autoReplyDTO = JsonUtils.parseObject(imAdminAutoReplyVo.getAutoReplyContext(), AutoReplyDTO.class);
            assert autoReplyDTO != null;
            autoReplyDTO.setAdminAutoReplyId(imAdminAutoReplyVo.getAdminAutoReplyId());
            return R.ok(autoReplyDTO);
        } else {
            return R.ok(AutoReplyDTO.getDefault());
        }
    }

    /**
     * 保存自动回复设置
     */
    @PostMapping(value = "/saveAutoReply")
    public R<?> saveAutoReply(@RequestBody AutoReplyDTO request) {

        ImAdminAutoReplyBo bo = new ImAdminAutoReplyBo();
        bo.setAdminId(MerchantLoginHelper.getAdminId());
        bo.setMerchantChannelId(request.getMerchantChannelId());
        ImAdminAutoReplyVo imAdminAutoReplyVo = iImAdminAutoReplyService.queryOne(bo);
        if (Objects.nonNull(imAdminAutoReplyVo)) {
            ImAdminAutoReplyBo bo1 = new ImAdminAutoReplyBo();
            bo1.setAdminAutoReplyId(imAdminAutoReplyVo.getAdminAutoReplyId());
            bo1.setAutoReplyContext(JsonUtils.toJsonString(request));
            iImAdminAutoReplyService.updateByBo(bo1);
        } else {
            ImAdminAutoReplyBo replyBo = new ImAdminAutoReplyBo();
            replyBo.setAdminId(MerchantLoginHelper.getAdminId());
            replyBo.setMerchantChannelId(request.getMerchantChannelId());
            replyBo.setAutoReplyContext(JsonUtils.toJsonString(request));
            iImAdminAutoReplyService.insertByBo(replyBo);
        }
        return R.ok();
    }

    /**
     * 查询我的渠道
     */
    @GetMapping(value = "/myMerchantChannel")
    public R<?> myMerchantChannel() {
        ImMerchantChannelRelationBo bo = new ImMerchantChannelRelationBo();
        bo.setAdminId(MerchantLoginHelper.getAdminId());
        List<ImMerchantChannelRelationVo> imMerchantChannelRelationVos = imMerchantChannelRelationService.queryList(bo);
        List<Long> channelIdList = imMerchantChannelRelationVos.stream()
            .map(ImMerchantChannelRelationVo::getMerchantChannelId)
            .toList();
        if (CollectionUtils.isEmpty(channelIdList)) {
            return R.ok(new ArrayList<>());
        }
        List<ImMerchantChannelVo> imMerchantChannelVos = iImMerchantChannelService.queryByIds(channelIdList);
        return R.ok(imMerchantChannelVos);
    }


    /**
     *
     */
    @PostMapping(value = "/bindCustomerService")
    public R<?> bindCustomerService(@RequestBody BindCustomerServiceRequest request) {
        ImMerchantChannelRelationBo bo = new ImMerchantChannelRelationBo();
        bo.setMerchantChannelId(request.getMerchantChannelId());
        bo.setAdminId(request.getAdminId());
        imMerchantChannelRelationService.insertByBo(bo);
        return R.ok();
    }

    /**
     *
     */
    @PostMapping(value = "/queryBindCustomerService")
    public R<?> queryBindCustomerService(@RequestBody BindCustomerServiceRequest request) {
        ImMerchantChannelRelationBo bo = new ImMerchantChannelRelationBo();
        bo.setMerchantChannelId(request.getMerchantChannelId());
        List<ImMerchantChannelRelationVo> imMerchantChannelRelationVos = imMerchantChannelRelationService.queryList(bo);
        if (CollectionUtils.isEmpty(imMerchantChannelRelationVos)) {
            return R.ok(new ArrayList<>());
        }
        List<Long> adminIdList = imMerchantChannelRelationVos.stream()
            .map(ImMerchantChannelRelationVo::getAdminId)
            .distinct()
            .toList();

        List<ImMerchantAdministratorVo> imMerchantAdministratorVos = imMerchantAdministratorService.queryByIds(adminIdList);
        return R.ok(imMerchantAdministratorVos);
    }

    /**
     * 设置管理员
     */
    @PostMapping(value = "/setAdmin")
    public R<?> setAdmin(@RequestBody ImMerchantAdministratorBo request) {
        if (Objects.equals(request.getAdminId(), MerchantLoginHelper.getAdminId())) {
            return R.fail("不能设置自己为管理员");
        }
        merchantApplicationService.setAdmin(request.getAdminId());
        return R.ok();
    }


    @GetMapping("/visitorList")
    public R<?> visitorList(ImVisitorBo bo, PageQuery pageQuery) {
        // 查询访客标签表 根据标签id+商户id
        if (CollectionUtils.isNotEmpty(bo.getCustomLabelIdList())) {
            ImVisitorLabelBo bo1 = new ImVisitorLabelBo();
            bo1.setCustomLabelIdList(bo.getCustomLabelIdList());
            bo1.setMerchantId(MerchantLoginHelper.getMerchantId());
            List<ImVisitorLabelVo> imVisitorLabelVos = imVisitorLabelService.queryList(bo1);
            List<String> visitorIdList = imVisitorLabelVos.stream()
                .map(ImVisitorLabelVo::getVisitorId)
                .toList();
            bo.setVisitorIdList(visitorIdList);
        }
        TableDataInfo<ImVisitorVo> dataInfo = imVisitorService.queryPageList(bo, pageQuery);
        List<ImVisitorVo> rows = dataInfo.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            Map<Long, ImMerchantChannelVo> longImMerchantChannelVoMap = queryMerchantChannelName(rows);
            List<String> visitorIdList = rows.stream()
                .map(ImVisitorVo::getVisitorId)
                .distinct()
                .toList();
            Map<String, List<ImVisitorLabelVo>> visitorLabel = queryVisitorLabel(visitorIdList);
            for (ImVisitorVo row : rows) {
                List<ImVisitorLabelVo> orDefault = visitorLabel.getOrDefault(row.getVisitorId(), new ArrayList<>());
                List<String> list = orDefault.stream()
                    .map(ImVisitorLabelVo::getCustomLabelName)
                    .toList();
                row.setVisitorLabelList(list);
                ImMerchantChannelVo imMerchantChannelVo = longImMerchantChannelVoMap.get(row.getMerchantChannelId());
                if (imMerchantChannelVo != null) {
                    row.setMerchantChannelName(imMerchantChannelVo.getChannelName());
                }
                row.setLocationInfo("中国-香港");
            }

        }
        return R.ok(dataInfo);
    }


    @GetMapping("/queryVisitorLabel")
    public R<?> queryVisitorLabel(String visitorId) {
        ImVisitorLabelBo bo = new ImVisitorLabelBo();
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        bo.setVisitorId(visitorId);
        List<ImVisitorLabelVo> imVisitorLabelVos = imVisitorLabelService.queryList(bo);
        if (CollectionUtils.isNotEmpty(imVisitorLabelVos)) {
            List<Long> customLableIdList = imVisitorLabelVos.stream()
                .map(ImVisitorLabelVo::getCustomLabelId)
                .toList();
            List<ImCustomLabelVo> imCustomLabelVos = customLabelService.queryByIds(customLableIdList);
            return R.ok(imCustomLabelVos);
        }
        return R.ok();
    }


    /**
     * 评论列表
     */
    @GetMapping("/commentList")
    public R<?> commentList(ImConversationBo bo, PageQuery pageQuery) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        bo.setStatus(ConversationConstants.closed);
        TableDataInfo<ImConversationVo> dataInfo = iImConversationService.queryPageList(bo, pageQuery);
        return R.ok(dataInfo);
    }

    private Map<String, List<ImVisitorLabelVo>> queryVisitorLabel(List<String> visitorIdList) {
        List<ImVisitorLabelVo> imVisitorLabelVos = imVisitorLabelService.queryVisitorLabels(visitorIdList, MerchantLoginHelper.getMerchantId());
        List<Long> customLabelIdList = imVisitorLabelVos.stream()
            .map(ImVisitorLabelVo::getCustomLabelId)
            .distinct()
            .toList();
        if (CollectionUtils.isEmpty(customLabelIdList)) {
            return new HashMap<>();
        }
        List<ImCustomLabelVo> imCustomLabelVos = customLabelService.queryByIds(customLabelIdList);
        Map<Long, ImCustomLabelVo> customLabelVoMap = imCustomLabelVos.stream()
            .collect(Collectors.toMap(ImCustomLabelVo::getCustomLabelId, Function.identity()));
        for (ImVisitorLabelVo imVisitorLabelVo : imVisitorLabelVos) {
            ImCustomLabelVo imCustomLabelVo = customLabelVoMap.get(imVisitorLabelVo.getCustomLabelId());
            if (imCustomLabelVo != null) {
                imVisitorLabelVo.setCustomLabelName(imCustomLabelVo.getCustomLabelName());
            }
        }
        return imVisitorLabelVos.stream().collect(Collectors.groupingBy(ImVisitorLabelVo::getVisitorId));

    }

    private Map<Long, ImMerchantChannelVo> queryMerchantChannelName(List<ImVisitorVo> rows) {
        List<Long> merchantChannelIdList = rows.stream()
            .map(ImVisitorVo::getMerchantChannelId)
            .distinct()
            .toList();
        if (CollectionUtils.isEmpty(merchantChannelIdList)) {
            return new HashMap<>();
        }
        List<ImMerchantChannelVo> imMerchantChannelVos = iImMerchantChannelService.queryByIds(merchantChannelIdList);
        return imMerchantChannelVos.stream()
            .collect(Collectors.toMap(ImMerchantChannelVo::getMerchantChannelId, Function.identity()));
    }


}
