package org.dromara.merchant.ctrl;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImQuickRepliesGroupBo;
import org.dromara.im.domain.vo.ImQuickRepliesGroupVo;
import org.dromara.im.service.IImQuickRepliesGroupService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团队快捷回复分组
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/quickRepliesGroup")
public class QuickRepliesGroupCtrl extends BaseController {

    private final IImQuickRepliesGroupService imQuickRepliesGroupService;

    /**
     * 查询团队快捷回复分组列表
     */
    @GetMapping("/list")
    public R<?> list(ImQuickRepliesGroupBo bo, PageQuery pageQuery) {
        if (StringUtils.equals(bo.getType(), "0")) {
            bo.setAdminId(MerchantLoginHelper.getAdminId());
        } else {
            bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        }

        return R.ok(imQuickRepliesGroupService.queryPageList(bo, pageQuery));
    }


    /**
     * 获取团队快捷回复分组详细信息
     *
     * @param quickRepliesGroupId 主键
     */
    @GetMapping("/{quickRepliesGroupId}")
    public R<ImQuickRepliesGroupVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long quickRepliesGroupId) {
        return R.ok(imQuickRepliesGroupService.queryById(quickRepliesGroupId));
    }

    /**
     * 新增团队快捷回复分组
     */
    @Log(title = "团队快捷回复分组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImQuickRepliesGroupBo bo) {
        if (StringUtils.equals(bo.getType(), "0")) {
            bo.setAdminId(MerchantLoginHelper.getAdminId());
        } else {
            bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        }

        return toAjax(imQuickRepliesGroupService.insertByBo(bo));
    }

    /**
     * 修改团队快捷回复分组
     */
    @Log(title = "团队快捷回复分组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ImQuickRepliesGroupBo bo) {
        return toAjax(imQuickRepliesGroupService.updateByBo(bo));
    }

    /**
     * 删除团队快捷回复分组
     *
     * @param quickRepliesGroupIds 主键串
     */
    @Log(title = "团队快捷回复分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{quickRepliesGroupIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] quickRepliesGroupIds) {
        return toAjax(imQuickRepliesGroupService.deleteWithValidByIds(List.of(quickRepliesGroupIds), true));
    }
}
