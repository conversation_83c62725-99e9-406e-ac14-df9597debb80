package org.dromara.merchant.ctrl;

import lombok.AllArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.im.domain.bo.ImTranslationPackageBo;
import org.dromara.im.service.IImTranslationPackageService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 翻译套餐管理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/api/translatePackage")
@AllArgsConstructor
public class TranslatePackageCtrl {

    private final IImTranslationPackageService imTranslationPackageService;

    /**
     * 查询翻译套餐列表
     */
    @GetMapping("/list")
    public R<?> list(ImTranslationPackageBo bo) {
        bo.setStatus("1");
        return R.ok(imTranslationPackageService.queryList(bo));
    }
}
