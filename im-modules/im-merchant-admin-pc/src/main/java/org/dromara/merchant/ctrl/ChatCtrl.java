package org.dromara.merchant.ctrl;

import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.constans.ConversationConstants;
import org.dromara.im.domain.bo.ImAdminVisitorRelationBo;
import org.dromara.im.domain.bo.ImConversationBo;
import org.dromara.im.domain.bo.ImVisitorBo;
import org.dromara.im.domain.bo.ImVisitorLabelBo;
import org.dromara.im.domain.vo.*;
import org.dromara.im.service.*;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.dromara.merchant.dto.VisitorListDto;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 聊天控制器
 * 演示如何使用WebSocketUtils.publishMessage进行1对1消息发送
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/")
@RequiredArgsConstructor
public class ChatCtrl extends BaseController {


    private final IImConversationService conversationService;

    private final IImVisitorService visitorService;

    private final IImAdminVisitorRelationService imAdminVisitorRelationService;

    private final IImMessageService messageService;

    private final IImVisitorLabelService imVisitorLabelService;

    private final IImQueueService queueService;


    /**
     * 获取置顶聊天列表
     *
     * @return
     */
    @GetMapping("/topChatList")
    public R<List<?>> topChatList(ImAdminVisitorRelationBo bo) {

        Long adminId = MerchantLoginHelper.getAdminId();
        Long merchantId = MerchantLoginHelper.getMerchantId();
        if (StringUtils.isNotEmpty(bo.getVisitorName())) {
            ImVisitorBo bo1 = new ImVisitorBo();
            bo1.setVisitorName(bo.getVisitorName());
            bo1.setMerchantId(merchantId);
            List<ImVisitorVo> imVisitorVos = visitorService.queryList(bo1);
            if (CollectionUtils.isNotEmpty(imVisitorVos)) {
                List<String> visitorIds = imVisitorVos.stream()
                    .map(ImVisitorVo::getVisitorId)
                    .toList();
                bo.setVisitorIdList(visitorIds);
            }
        }
        // 获取当前登录的客服信息

        bo.setAdminId(adminId);
        bo.setMerchantId(merchantId);
        bo.setIsTop("1");
        // 查询客服的访客关系列表（分页）
        List<ImAdminVisitorRelationVo> rows = imAdminVisitorRelationService.queryList(bo);

        // 转换为VisitorListDto并补充详细信息
        List<VisitorListDto> visitorListDtos = new ArrayList<>();

        buildVisitorListDto(rows, merchantId, adminId, visitorListDtos);

        // 按未读数量降序排序（未读数量多的排在前面）
        sort(visitorListDtos);

        return R.ok(visitorListDtos);
    }

    /**
     * 获取聊天列表
     *
     * @return
     */
    @GetMapping("/chatList")
    public R<TableDataInfo<VisitorListDto>> chatList(ImAdminVisitorRelationBo bo, PageQuery pageQuery) {
        // 获取当前登录的客服信息
        Long adminId = MerchantLoginHelper.getAdminId();
        Long merchantId = MerchantLoginHelper.getMerchantId();

        if (adminId == null || merchantId == null) {
            return R.fail("用户未登录或登录信息无效");
        }

        // 设置查询条件：只查询当前客服的访客关系
        bo.setAdminId(adminId);
        bo.setMerchantId(merchantId);
        bo.setIsTop("0");
        if (StringUtils.equals(bo.getFromPage(), "current")) {
            bo.setEndService("0");
        } else if (StringUtils.equals(bo.getFromPage(), "history")) {
            bo.setEndService("1");
        }

        if (bo.getUnreadCount() != null && bo.getUnreadCount() > 0) {
            ImConversationBo bo1 = new ImConversationBo();
            bo1.setAdminId(adminId);
            bo1.setStatus(ConversationConstants.active);
            bo1.setUnreadCount(1L);
            bo1.setMerchantId(merchantId);
            List<ImConversationVo> imConversationVos = conversationService.queryList(bo1);
            if (CollectionUtils.isNotEmpty(imConversationVos)) {
                List<String> visitorIds = imConversationVos.stream()
                    .map(ImConversationVo::getVisitorId)
                    .toList();
                bo.setVisitorIdList(visitorIds);
            } else {
                bo.setVisitorIdList(List.of("0"));
            }
        }

        if (StringUtils.isNotEmpty(bo.getVisitorName())) {
            ImVisitorBo bo1 = new ImVisitorBo();
            bo1.setVisitorName(bo.getVisitorName());
            bo1.setMerchantId(merchantId);
            List<ImVisitorVo> imVisitorVos = visitorService.queryList(bo1);
            if (CollectionUtils.isNotEmpty(imVisitorVos)) {
                List<String> visitorIds = imVisitorVos.stream()
                    .map(ImVisitorVo::getVisitorId)
                    .toList();
                List<String> visitorIdList = bo.getVisitorIdList();
                if (CollectionUtils.isEmpty(visitorIdList)) {
                    visitorIdList = new ArrayList<>();
                }
                visitorIdList.addAll(visitorIds);
                List<String> list = visitorIdList.stream()
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .toList();
                bo.setVisitorIdList(list);
            }else if (CollectionUtils.isEmpty(bo.getVisitorIdList())) {
                bo.setVisitorIdList(List.of("0"));
            }
        }


        // 查询客服的访客关系列表（分页）
        TableDataInfo<ImAdminVisitorRelationVo> dataInfo = imAdminVisitorRelationService.queryPageList(bo, pageQuery);

        // 转换为VisitorListDto并补充详细信息
        List<VisitorListDto> visitorListDtos = new ArrayList<>();

        List<ImAdminVisitorRelationVo> rows = dataInfo.getRows();
        buildVisitorListDto(rows, merchantId, adminId, visitorListDtos);

        // 按未读数量降序排序（未读数量多的排在前面）
        sort(visitorListDtos);

        // 创建新的分页结果
        TableDataInfo<VisitorListDto> result = new TableDataInfo<>();
        result.setCode(dataInfo.getCode());
        result.setMsg(dataInfo.getMsg());
        result.setTotal(dataInfo.getTotal());
        result.setRows(visitorListDtos);

        log.info("查询客服访客列表成功 - adminId: {}, 总数: {}", adminId, result.getTotal());
        return R.ok(result);

    }

    private static void sort(List<VisitorListDto> visitorListDtos) {
        visitorListDtos.sort((a, b) -> {
            long unreadA = a.getUnreadCount() != null ? a.getUnreadCount() : 0L;
            long unreadB = b.getUnreadCount() != null ? b.getUnreadCount() : 0L;
            return Long.compare(unreadB, unreadA); // 降序排序
        });
    }

    private void buildVisitorListDto(List<ImAdminVisitorRelationVo> rows, Long merchantId, Long adminId, List<VisitorListDto> visitorListDtos) {
        if (CollectionUtils.isNotEmpty(rows)) {
            // 收集所有访客ID
            List<String> visitorIds = rows.stream()
                .map(ImAdminVisitorRelationVo::getVisitorId)
                .distinct()
                .collect(Collectors.toList());

            // 批量查询访客信息
            Map<String, ImVisitorVo> visitorInfoMap = batchQueryVisitorInfo(visitorIds, merchantId);

            // 批量查询最新会话信息
            Map<String, ImConversationVo> latestConversationMap = batchQueryLatestConversations(visitorIds, adminId, merchantId);


            List<ImVisitorLabelVo> imVisitorLabelVos = imVisitorLabelService.queryVisitorLabels(visitorIds, merchantId);
            Map<String, List<ImVisitorLabelVo>> visitorLabelMap = imVisitorLabelVos.stream()
                .collect(Collectors.groupingBy(ImVisitorLabelVo::getVisitorId));

            // 组装数据为VisitorListDto
            for (ImAdminVisitorRelationVo relation : rows) {
                VisitorListDto dto = convertToVisitorListDto(relation, visitorInfoMap, latestConversationMap);
                dto.setAdminId(adminId);
                dto.setVisitorLabelVos(visitorLabelMap.getOrDefault(relation.getVisitorId(), new ArrayList<>()));
                visitorListDtos.add(dto);
            }

            log.info("批量关联查询完成 - 访客数量: {}, 访客信息: {}, 会话信息: {}",
                visitorIds.size(), visitorInfoMap.size(), latestConversationMap.size());
        }
    }

    /**
     * 获取聊天记录
     *
     * @return
     */
    @GetMapping("/chatRecord")
    public R<TableDataInfo<ImMessageVo>> chatRecord(ImConversationBo bo, PageQuery pageQuery) {
        TableDataInfo<ImMessageVo> dataInfo = messageService.queryPageList2(bo.getVisitorId(), MerchantLoginHelper.getAdminId(), pageQuery);
        return R.ok(dataInfo);
    }


    /**
     * 已读接口
     */
    @GetMapping("/read")
    @Transactional(rollbackFor = Exception.class)
    public R<String> read(String visitorId) {
        try {
            // 获取当前登录的客服信息
            Long adminId = MerchantLoginHelper.getAdminId();

            if (adminId == null) {
                return R.fail("用户未登录");
            }

            if (visitorId == null || visitorId.trim().isEmpty()) {
                return R.fail("访客ID不能为空");
            }

            // 在事务中执行已读操作
            Boolean success = executeMarkAsReadTransaction(visitorId, adminId);

            if (success) {
                log.info("标记消息已读成功 - adminId: {}, visitorId: {}", adminId, visitorId);
                return R.ok("消息已标记为已读");
            } else {
                log.warn("标记消息已读失败 - adminId: {}, visitorId: {}", adminId, visitorId);
                return R.fail("标记消息已读失败");
            }

        } catch (Exception e) {
            log.error("标记消息已读失败 - visitorId: {}", visitorId, e);
            // 事务会自动回滚
            return R.fail("标记消息已读失败: " + e.getMessage());
        }
    }

    /**
     * 置顶游客
     */
    @GetMapping("/topVisitor")
    public R<String> topVisitor(String visitorId) {
        Long adminId = MerchantLoginHelper.getAdminId();
        Boolean success = imAdminVisitorRelationService.toggleTopStatus(adminId, visitorId);
        return success ? R.ok("操作成功") : R.fail("操作失败");
    }


    /**
     * 拉黑
     */
    @GetMapping("/blockVisitor")
    public R<String> blockVisitor(String visitorId) {
        Long adminId = MerchantLoginHelper.getAdminId();
        Boolean success = imAdminVisitorRelationService.blockVisitor(adminId, visitorId);
        return success ? R.ok("操作成功") : R.fail("操作失败");
    }

    /**
     * 结束服务
     */
    @GetMapping("/endService")
    public R<String> endService(String visitorId) {
        Long adminId = MerchantLoginHelper.getAdminId();
        Long merchantId = MerchantLoginHelper.getMerchantId();

        Boolean success = conversationService.closeConversationByVisitorIdAndAdminId(visitorId, adminId);
        imAdminVisitorRelationService.updateEndService(merchantId, visitorId, adminId);


        if (success) {
            // 结束服务后，尝试处理排队中的访客
            try {
                queueService.processQueueOnServiceEnd(merchantId, adminId);
            } catch (Exception e) {
                log.warn("处理排队失败 - adminId: {}, merchantId: {}", adminId, merchantId, e);
            }
        }

        return success ? R.ok("操作成功") : R.fail("操作失败");
    }

    /**
     * 转接
     */
    @GetMapping("/transfer")
    public R<String> transfer(String visitorId, String toAdminId) {
        Long fromAdminId = MerchantLoginHelper.getAdminId();
        Long targetAdminId = Long.valueOf(toAdminId);
        Long merchantId = MerchantLoginHelper.getMerchantId();

        // 先查询当前会话
        ImConversationVo conversation = conversationService.queryLatestByVisitorAndAdmin(visitorId, fromAdminId, merchantId);
        if (conversation == null) {
            return R.fail("未找到当前会话");
        }

        // 转接会话
        Boolean success = conversationService.transferConversation(conversation.getConversationId(), fromAdminId, targetAdminId, "手动转接");

        if (success) {
            // 创建新的客服访客关系
            imAdminVisitorRelationService.createOrGetAdminVisitorRelation(targetAdminId, visitorId, merchantId, null);
        }

        return success ? R.ok("转接成功") : R.fail("转接失败");
    }

    /**
     * 批量保存访客标签
     */
    @PostMapping("/batchSaveVisitorLabels")
    public R<?> batchSaveVisitorLabels(@RequestBody List<ImVisitorLabelBo> bos) {
        try {
            if (bos == null || bos.isEmpty()) {
                return R.fail("标签数据不能为空");
            }

            // 获取当前登录的客服信息
            Long adminId = MerchantLoginHelper.getAdminId();
            Long merchantId = MerchantLoginHelper.getMerchantId();
            String visitorId = bos.stream()
                .map(ImVisitorLabelBo::getVisitorId)
                .findFirst()
                .get();

            if (adminId == null || merchantId == null) {
                return R.fail("用户未登录或登录信息无效");
            }

            // 为每个标签设置创建者信息
            for (ImVisitorLabelBo bo : bos) {
                bo.setCreateBy(adminId);
                bo.setMerchantId(merchantId);
            }

            // 批量保存标签
            Boolean success = imVisitorLabelService.insertBatch(bos);

            if (success) {
                List<ImVisitorLabelVo> imVisitorLabelVos = imVisitorLabelService.queryVisitorLabels(List.of(visitorId), merchantId);
                log.info("批量保存访客标签成功 - adminId: {}, 数量: {}", adminId, bos.size());
                return R.ok(imVisitorLabelVos);
            } else {
                return R.fail("保存失败");
            }

        } catch (Exception e) {
            log.error("批量保存访客标签失败", e);
            return R.fail("保存失败: " + e.getMessage());
        }
    }


    /**
     * 删除访客标签
     *
     * @param visitorLabelIds 主键串
     */
    @DeleteMapping("/delVisitorLabel/{visitorLabelIds}")
    public R<?> remove(@NotEmpty(message = "主键不能为空")
                       @PathVariable Long[] visitorLabelIds) {
        ImVisitorLabelVo imVisitorLabelVo = imVisitorLabelService.queryById(visitorLabelIds[0]);
        String visitorId = imVisitorLabelVo.getVisitorId();
        Long merchantId = MerchantLoginHelper.getMerchantId();
        Boolean result = imVisitorLabelService.deleteWithValidByIds(List.of(visitorLabelIds), true);
        if (result) {
            List<ImVisitorLabelVo> imVisitorLabelVos = imVisitorLabelService.queryVisitorLabels(List.of(visitorId), merchantId);
            return R.ok(imVisitorLabelVos);
        }
        return toAjax(result);
    }

    /**
     * 对话备注
     */
    @GetMapping("/chatRemark")
    public R<String> chatRemark(String visitorId, String remark) {
        Long adminId = MerchantLoginHelper.getAdminId();

        // 更新客服访客关系中的备注信息
        Boolean success = imAdminVisitorRelationService.updateRemark(adminId, visitorId, remark);

        return success ? R.ok() : R.fail("备注保存失败");
    }


    /**
     * 执行标记已读的事务操作
     * 确保消息表和关系表的更新在同一个事务中
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean executeMarkAsReadTransaction(String visitorId, Long adminId) {
        try {
            // 1. 先查询实际的未读消息数量（用于验证）
            Long unreadCount = messageService.getUnreadCountFromVisitorToAdmin(visitorId, adminId);
            log.info("查询到未读消息数量: {} - adminId: {}, visitorId: {}", unreadCount, adminId, visitorId);

            // 2. 标记消息为已读
            Boolean messageUpdateSuccess = messageService.markMessagesAsReadBetweenVisitorAndAdmin(visitorId, adminId);
            if (!messageUpdateSuccess) {
                log.error("消息表更新失败 - adminId: {}, visitorId: {}", adminId, visitorId);
                throw new RuntimeException("消息表更新失败");
            }

            // 3. 更新关系表的未读计数
            conversationService.clearUnreadCount(adminId, visitorId);

            // 4. 验证操作结果
            Long remainingUnreadCount = messageService.getUnreadCountFromVisitorToAdmin(visitorId, adminId);
            if (remainingUnreadCount > 0) {
                log.warn("标记已读后仍有未读消息 - adminId: {}, visitorId: {}, 剩余未读: {}",
                    adminId, visitorId, remainingUnreadCount);
                // 这种情况可能是并发导致的，不抛异常但记录警告
            }

            log.info("标记已读事务执行成功 - adminId: {}, visitorId: {}, 原未读数: {}, 剩余未读数: {}",
                adminId, visitorId, unreadCount, remainingUnreadCount);

            return true;

        } catch (Exception e) {
            log.error("标记已读事务执行失败 - adminId: {}, visitorId: {}", adminId, visitorId, e);
            // 抛出异常触发事务回滚
            throw new RuntimeException("标记已读事务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量查询访客信息
     */
    private Map<String, ImVisitorVo> batchQueryVisitorInfo(List<String> visitorIds, Long merchantId) {
        try {
            if (CollectionUtils.isEmpty(visitorIds)) {
                return new HashMap<>();
            }
            // 批量查询访客信息
            List<ImVisitorVo> visitorList = visitorService.queryByVisitorIdsAndBusinessId(visitorIds, merchantId);

            // 转换为Map，以visitorId为key
            return visitorList.stream()
                .collect(Collectors.toMap(ImVisitorVo::getVisitorId, visitor -> visitor, (v1, v2) -> v1));

        } catch (Exception e) {
            log.error("批量查询访客信息失败 - visitorIds: {}, merchantId: {}", visitorIds, merchantId, e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询最新会话信息
     */
    private Map<String, ImConversationVo> batchQueryLatestConversations(List<String> visitorIds, Long adminId, Long merchantId) {
        try {
            if (visitorIds.isEmpty()) {
                return new HashMap<>();
            }

            // 批量查询最新会话信息
            List<ImConversationVo> conversationList = conversationService
                .queryLatestByVisitorIdsAndAdmin(visitorIds, adminId, merchantId);

            // 转换为Map，以visitorId为key
            return conversationList.stream()
                .collect(Collectors.toMap(ImConversationVo::getVisitorId, conversation -> conversation, (v1, v2) -> v1));

        } catch (Exception e) {
            log.error("批量查询最新会话信息失败 - visitorIds: {}, adminId: {}, merchantId: {}",
                visitorIds, adminId, merchantId, e);
            return new HashMap<>();
        }
    }

    /**
     * 转换为VisitorListDto
     */
    private VisitorListDto convertToVisitorListDto(ImAdminVisitorRelationVo relation,
                                                   Map<String, ImVisitorVo> visitorInfoMap,
                                                   Map<String, ImConversationVo> conversationMap) {
        VisitorListDto dto = new VisitorListDto();
        // 设置关系基本信息
        dto.setAdminVisitorRelationId(relation.getAdminVisitorRelationId());
        dto.setVisitorId(relation.getVisitorId());
        dto.setMerchantId(relation.getMerchantId());
        dto.setMerchantChannelId(relation.getMerchantChannelId());
        dto.setMerchantChannelName(relation.getMerchantChannelName());
        dto.setIsTop(relation.getIsTop());
        dto.setIsBlocked(relation.getIsBlocked());
        dto.setChatRemark(relation.getChatRemark());
        // 设置访客详细信息
        ImVisitorVo visitorInfo = visitorInfoMap.get(relation.getVisitorId());
        if (visitorInfo != null) {
            dto.setVisitorName(visitorInfo.getVisitorName());
            dto.setVisitorAvatar(visitorInfo.getAvatarUrl());
            dto.setFromUrl(visitorInfo.getFromUrl());
            dto.setIpAddress(visitorInfo.getIpAddress());
            dto.setUserAgent(visitorInfo.getUserAgent());
            dto.setOnlineStatus(visitorInfo.getOnlineStatus());
            dto.setLastOnlineTime(visitorInfo.getLastOnlineTime());
        }

        // 设置最新会话信息
        ImConversationVo latestConversation = conversationMap.get(relation.getVisitorId());
        if (latestConversation != null) {
            dto.setLatestConversationId(latestConversation.getConversationId());
            dto.setConversationStatus(latestConversation.getStatus());
            dto.setMessageCount(latestConversation.getMessageCount());
            dto.setUnreadCount(latestConversation.getUnreadCount());
            dto.setLatestMessageContent(latestConversation.getLastMessageContent());
            dto.setRating(latestConversation.getRating());
            dto.setClosedReason(latestConversation.getClosedReason());
            dto.setFeedback(latestConversation.getFeedback());
            dto.setLastMessageTime(latestConversation.getLastMessageTime());
        }

        return dto;
    }

    /**
     * 手动处理排队
     */
    @GetMapping("/processQueue")
    public R<String> processQueue() {
        try {
            Long merchantId = MerchantLoginHelper.getMerchantId();

            if (merchantId == null) {
                return R.fail("用户未登录");
            }

            int processed = queueService.processWaitingQueue(merchantId);

            return R.ok("处理完成，分配访客数量: " + processed);

        } catch (Exception e) {
            log.error("手动处理排队失败", e);
            return R.fail("处理失败: " + e.getMessage());
        }
    }
}
