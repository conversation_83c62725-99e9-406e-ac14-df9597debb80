package org.dromara.merchant;

import javax.net.ssl.*;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URL;
import java.security.cert.Certificate;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;

/**
 * 网站可访问性和SSL证书检查工具
 *
 * <AUTHOR> Assistant
 */
public class WebsiteChecker {

    private static final int DEFAULT_TIMEOUT = 10000; // 10秒超时
    private static final int DEFAULT_HTTPS_PORT = 443;
    private static final int DEFAULT_HTTP_PORT = 80;

    public static void main(String[] args) {
        System.out.println("=== 网站检查工具 ===");
        System.out.println("检查时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();
        Arrays.asList("https://www.baidu.com", "https://www.google.com/").forEach(WebsiteChecker::checkWebsite);

    }

    /**
     * 检查网站的可访问性和SSL证书
     */
    public static void checkWebsite(String urlString) {
        System.out.println("检查网站: " + urlString);

        try {
            URL url = new URL(urlString);
            String protocol = url.getProtocol().toLowerCase();
            String host = url.getHost();
            int port = url.getPort();

            // 设置默认端口
            if (port == -1) {
                port = "https".equals(protocol) ? DEFAULT_HTTPS_PORT : DEFAULT_HTTP_PORT;
            }

            // 1. 检查网络连通性
            boolean isReachable = checkConnectivity(host, port);
            System.out.println("网络连通性: " + (isReachable ? "✓ 可达" : "✗ 不可达"));

            if (!isReachable) {
                System.out.println("状态: 网站无法访问");
                return;
            }

            // 2. 检查HTTP响应
            HttpCheckResult httpResult = checkHttpResponse(urlString);
            System.out.println("HTTP状态码: " + httpResult.statusCode);
            System.out.println("响应时间: " + httpResult.responseTime + "ms");
            System.out.println("HTTP检查: " + (httpResult.success ? "✓ 成功" : "✗ 失败"));

            if (!httpResult.success) {
                System.out.println("错误信息: " + httpResult.errorMessage);
            }

            // 3. 如果是HTTPS，检查SSL证书
            if ("https".equals(protocol)) {
                SSLCheckResult sslResult = checkSSLCertificate(host, port);
                System.out.println("SSL证书检查: " + (sslResult.valid ? "✓ 有效" : "✗ 无效"));

                if (sslResult.valid) {
                    System.out.println("证书主题: " + sslResult.subject);
                    System.out.println("证书颁发者: " + sslResult.issuer);
                    System.out.println("有效期: " + sslResult.validFrom + " 至 " + sslResult.validTo);
                    System.out.println("剩余天数: " + sslResult.daysUntilExpiry + " 天");

                    if (sslResult.daysUntilExpiry <= 30) {
                        System.out.println("⚠️  警告: SSL证书即将在30天内过期!");
                    }
                } else {
                    System.out.println("SSL错误: " + sslResult.errorMessage);
                }
            }

            // 4. 总体状态
            boolean overallSuccess = httpResult.success &&
                ("http".equals(protocol) || ("https".equals(protocol) && checkSSLCertificate(host, port).valid));
            System.out.println("总体状态: " + (overallSuccess ? "✓ 正常" : "✗ 异常"));

        } catch (Exception e) {
            System.out.println("检查失败: " + e.getMessage());
        }
    }

    /**
     * 检查网络连通性
     */
    private static boolean checkConnectivity(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), DEFAULT_TIMEOUT);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 检查HTTP响应
     */
    private static HttpCheckResult checkHttpResponse(String urlString) {
        HttpCheckResult result = new HttpCheckResult();
        long startTime = System.currentTimeMillis();

        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(DEFAULT_TIMEOUT);
            connection.setReadTimeout(DEFAULT_TIMEOUT);
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "WebsiteChecker/1.0");

            result.statusCode = connection.getResponseCode();
            result.responseTime = System.currentTimeMillis() - startTime;
            result.success = (result.statusCode >= 200 && result.statusCode < 400);

            connection.disconnect();

        } catch (Exception e) {
            result.success = false;
            result.errorMessage = e.getMessage();
            result.responseTime = System.currentTimeMillis() - startTime;
        }

        return result;
    }

    /**
     * 检查SSL证书
     */
    private static SSLCheckResult checkSSLCertificate(String host, int port) {
        SSLCheckResult result = new SSLCheckResult();

        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{new X509TrustManager() {
                public void checkClientTrusted(X509Certificate[] chain, String authType) {
                }

                public void checkServerTrusted(X509Certificate[] chain, String authType) {
                }

                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            }}, null);

            SSLSocketFactory factory = sslContext.getSocketFactory();

            try (SSLSocket socket = (SSLSocket) factory.createSocket(host, port)) {
                socket.setSoTimeout(DEFAULT_TIMEOUT);
                socket.startHandshake();

                Certificate[] certificates = socket.getSession().getPeerCertificates();
                if (certificates.length > 0 && certificates[0] instanceof X509Certificate) {
                    X509Certificate cert = (X509Certificate) certificates[0];

                    // 检查证书有效性
                    try {
                        cert.checkValidity();
                        result.valid = true;
                    } catch (CertificateExpiredException | CertificateNotYetValidException e) {
                        result.valid = false;
                        result.errorMessage = "证书已过期或尚未生效: " + e.getMessage();
                    }

                    // 获取证书信息
                    result.subject = cert.getSubjectDN().getName();
                    result.issuer = cert.getIssuerDN().getName();

                    Date notBefore = cert.getNotBefore();
                    Date notAfter = cert.getNotAfter();

                    result.validFrom = LocalDateTime.ofInstant(notBefore.toInstant(), ZoneId.systemDefault())
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    result.validTo = LocalDateTime.ofInstant(notAfter.toInstant(), ZoneId.systemDefault())
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

                    // 计算剩余天数
                    long diffInMillies = notAfter.getTime() - System.currentTimeMillis();
                    result.daysUntilExpiry = diffInMillies / (24 * 60 * 60 * 1000);
                }
            }

        } catch (Exception e) {
            result.valid = false;
            result.errorMessage = e.getMessage();
        }

        return result;
    }

    /**
     * HTTP检查结果
     */
    static class HttpCheckResult {
        boolean success = false;
        int statusCode = 0;
        long responseTime = 0;
        String errorMessage = "";
    }

    /**
     * SSL检查结果
     */
    static class SSLCheckResult {
        boolean valid = false;
        String subject = "";
        String issuer = "";
        String validFrom = "";
        String validTo = "";
        long daysUntilExpiry = 0;
        String errorMessage = "";
    }
}
