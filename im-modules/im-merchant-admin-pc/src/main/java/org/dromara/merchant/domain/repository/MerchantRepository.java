package org.dromara.merchant.domain.repository;

import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.domain.bo.ImMerchantAdministratorBo;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.merchant.domain.valueobject.MerchantEmail;

import java.util.List;

/**
 * 商户仓储接口（简化版）
 *
 * <AUTHOR>
 */
public interface MerchantRepository {

    /**
     * 保存商户
     */
    ImMerchantAdministratorVo save(ImMerchantBo merchant, ImMerchantAdministratorBo administratorBo);

    /**
     * 根据ID查找商户
     */
    ImMerchantVo findById(Long merchantId);

    /**
     * 根据邮箱查找商户
     */
    ImMerchantAdministratorVo findByEmail(MerchantEmail email);

    /**
     * 根据管理员邮箱查找管理员
     */
    ImMerchantAdministratorVo findAdminByEmail(MerchantEmail email);

    /**
     * 检查商户邮箱是否存在
     */
    boolean existsByEmail(MerchantEmail email);

    /**
     * 检查管理员邮箱是否存在
     */
    boolean existsAdminByEmail(MerchantEmail email);

    ImMerchantAdministratorVo detailAdmin(Long adminId);

    ImMerchantAdministratorVo selectByMerchantId(Long merchantId);

    List<ImMerchantAdministratorVo> selectList(ImMerchantAdministratorBo bo, Long merchantId, String... roleType);

    void modifyPassword(ImMerchantAdministrator administrator);

    ImMerchantVo selectMerchant(Long merchantId);
}
