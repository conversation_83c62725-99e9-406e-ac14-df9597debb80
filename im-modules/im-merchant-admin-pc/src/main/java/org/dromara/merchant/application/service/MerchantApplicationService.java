package org.dromara.merchant.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.domain.ImMerchantChannel;
import org.dromara.im.domain.ImMerchantChannelRelation;
import org.dromara.im.domain.bo.ImMerchantAdministratorBo;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.domain.dto.AssignmentRulesDto;
import org.dromara.im.domain.dto.ChannelDialogRulesDto;
import org.dromara.im.domain.dto.MessagePromptDto;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.im.constans.MerchantRoleType;
import org.dromara.im.mapper.ImMerchantAdministratorMapper;
import org.dromara.im.mapper.ImMerchantChannelMapper;
import org.dromara.im.mapper.ImMerchantChannelRelationMapper;
import org.dromara.im.utils.Password;
import org.dromara.merchant.domain.repository.MerchantRepository;
import org.dromara.merchant.domain.valueobject.MerchantEmail;
import org.dromara.merchant.dto.*;
import org.dromara.merchant.request.ModifyInfoRequest;
import org.dromara.merchant.request.ModifyPasswordRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 商户应用服务（简化版）
 *
 * <AUTHOR>
 */
@Service
public class MerchantApplicationService {

    @Autowired
    private MerchantRepository merchantRepository;

    @Autowired
    private ImMerchantAdministratorMapper administratorMapper;

    @Autowired
    private ImMerchantChannelRelationMapper merchantChannelRelationMapper;

    @Autowired
    private ImMerchantChannelMapper merchantChannelMapper;

    /**
     * 简化注册方法
     */
    @Transactional
    public AuthenticationResult register(SimpleRegisterCommand command) {
        // 验证邮箱是否已存在
        MerchantEmail email = new MerchantEmail(command.getEmail());
        if (merchantRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("邮箱已存在");
        }

        // 创建商户
        ImMerchantBo merchant = ImMerchantBo.initMerchant(command.getCompanyName());

        // 创建管理员（使用邮箱用户名作为管理员姓名）
        String adminName = command.getEmail().substring(0, command.getEmail().indexOf("@"));
        ImMerchantAdministratorBo admin = ImMerchantAdministratorBo.create(email.getValue(), adminName,
            new Password(command.getPassword()), null, MerchantRoleType.MERCHANT_ADMIN.getRoleId(), null);

        // 消息提醒
        admin.setMessagePrompt(JsonUtils.toJsonString(MessagePromptDto.getDefault()));
        // 保存商户
        ImMerchantAdministratorVo vo = merchantRepository.save(merchant, admin);
        // 创建默认渠道
        ImMerchantChannel defaultMerchantChannel = createDefaultMerchantChannel(merchant);
        // 绑定超级管理员的默认渠道
        saveMerchantChannelRelation(vo.getAdminId(),defaultMerchantChannel.getMerchantChannelId());
        // 生成令牌并返回
        String token = generateToken(vo);
        return new AuthenticationResult(token, vo);
    }

    private void saveMerchantChannelRelation(Long adminId, Long merchantChannelId) {
        ImMerchantChannelRelation e = new ImMerchantChannelRelation();
        e.setAdminId(adminId);
        e.setMerchantChannelId(merchantChannelId);
        merchantChannelRelationMapper.insert(e);
    }

    private ImMerchantChannel createDefaultMerchantChannel(ImMerchantBo merchant) {
        ImMerchantChannel entity = new ImMerchantChannel();
        entity.setMerchantId(merchant.getMerchantId());
        entity.setMerchantChannelName("默认配置");
        entity.setDefaultChannel("1");
        entity.setAssignmentRules(JsonUtils.toJsonString(AssignmentRulesDto.getDefault()));
        entity.setDialogRules(JsonUtils.toJsonString(ChannelDialogRulesDto.getDefault()));
        merchantChannelMapper.insert(entity);
        return entity;
    }

    /**
     * 登录方法
     */
    public AuthenticationResult login(LoginCommand command) {
        // 根据邮箱查找管理员
        MerchantEmail email = new MerchantEmail(command.getEmail());
        ImMerchantAdministratorVo admin = merchantRepository.findAdminByEmail(email);

        if (admin == null) {
            throw new IllegalArgumentException("邮箱或密码错误");
        }

        // 验证密码
        if (!Password.matches(command.getPassword(), admin.getPassword())) {
            throw new IllegalArgumentException("邮箱或密码错误");
        }

        // 查找商户
        ImMerchantAdministratorVo administratorVo = merchantRepository.findByEmail(email);

        // 生成令牌并返回
        String token = generateToken(administratorVo);
        return new AuthenticationResult(token, administratorVo);
    }

    /**
     * 添加管理员
     */
    @Transactional
    public void addAdministrator(AddAdministratorCommand command) {
        // 验证管理员邮箱是否已存在
        MerchantEmail email = new MerchantEmail(command.getEmail());
        if (merchantRepository.existsAdminByEmail(email)) {
            throw new IllegalArgumentException("邮箱已存在");
        }

        // 获取当前登录的商户信息
        MerchantLoginUser currentUser = MerchantLoginHelper.getLoginUser();
        if (currentUser == null) {
            throw new IllegalArgumentException("用户未登录");
        }

        // 检查权限：只有主管理员才能添加其他管理员
        if (!currentUser.getRolePermission().contains(MerchantRoleType.MERCHANT_ADMIN.getRoleId())) {
            throw new IllegalArgumentException("权限不足，只有主管理员才能添加其他管理员");
        }

        // 通过商户ID查找商户
        Long merchantId = currentUser.getDeptId();
        ImMerchantVo merchant = merchantRepository.findById(merchantId);

        if (merchant == null) {
            throw new IllegalArgumentException("商户不存在");
        }

        // 校验坐席数量
        Long seatsCount = merchant.getSeatsCount();
        Long l = administratorMapper.selectCount(new LambdaQueryWrapper<>(ImMerchantAdministrator.class)
                .eq(ImMerchantAdministrator::getMerchantId, merchantId)
                .eq(ImMerchantAdministrator::getRoleType, MerchantRoleType.MERCHANT_SERVICE.getRoleId())
        );

        if (l >= seatsCount) {
            throw new IllegalArgumentException("坐席数量已满");
        }

        MerchantRoleType roleType = MerchantRoleType.getByCode(command.getRoleType());
        ImMerchantAdministratorBo newAdmin = ImMerchantAdministratorBo.create(
                email.getValue(), command.getNickname(), new Password(command.getPassword()),
                merchantId, roleType.getRoleId(), command.getMemberGroupId());

        // 保存
        ImMerchantBo imMerchantBo = new ImMerchantBo();
        imMerchantBo.setMerchantId(merchant.getMerchantId());
        ImMerchantAdministratorVo administratorVo = merchantRepository.save(imMerchantBo, newAdmin);
        List<ImMerchantChannelRelation> entityList = new ArrayList<>();
        for (Long merchantChannelId : command.getMerchantChannelIdList()) {
            ImMerchantChannelRelation e = new ImMerchantChannelRelation();
            e.setAdminId(administratorVo.getAdminId());
            e.setMerchantChannelId(merchantChannelId);
            entityList.add(e);
        }
        merchantChannelRelationMapper.insertBatch(entityList);

    }

    /**
     * 生成SaToken令牌
     */
    private String generateToken(ImMerchantAdministratorVo administratorVo) {
        // 使用商户登录助手进行登录
        return MerchantLoginHelper.login(administratorVo);
    }

    public ImMerchantAdministratorVo detailAdmin(Long adminId) {
        return merchantRepository.detailAdmin(adminId);
    }

    public List<ImMerchantAdministratorVo> myTeam(ImMerchantAdministratorBo bo) {
        ImMerchantAdministratorVo administratorVo = administratorMapper.selectVoById(bo.getAdminId());
        return merchantRepository.selectList(bo, administratorVo.getMerchantId(), "2", "3");

    }

    public void modifyInfo(ModifyInfoRequest request, Long adminId) {
        administratorMapper.update(new LambdaUpdateWrapper<>(ImMerchantAdministrator.class)
            .set(StringUtils.isNotEmpty(request.getAvatar()), ImMerchantAdministrator::getAvatar, request.getAvatar())
            .set(StringUtils.isNotEmpty(request.getNickname()), ImMerchantAdministrator::getNickname, request.getNickname())
            .set(StringUtils.isNotEmpty(request.getEmail()), ImMerchantAdministrator::getEmail, request.getEmail())
            .eq(ImMerchantAdministrator::getAdminId, adminId)
        );
    }

    public void modifyPassword(ModifyPasswordRequest request) {
        if (request.getOldPassword().equals(request.getNewPassword())) {
            throw new IllegalArgumentException("新密码不能与旧密码相同");
        }
        ImMerchantAdministratorVo administrator = merchantRepository.selectByMerchantId(MerchantLoginHelper.getAdminId());
        if (!Password.matches(administrator.getPassword(), request.getOldPassword())) {
            throw new IllegalArgumentException("旧密码错误");
        }
        ImMerchantAdministrator convert = MapstructUtils.convert(administrator, ImMerchantAdministrator.class);
        administrator.setPassword(new Password(request.getNewPassword()).getValue());
        merchantRepository.modifyPassword(convert);
    }

    @Transactional
    public void modifyTeam(Long adminId, Long currentAdminId) {
        administratorMapper.update(new LambdaUpdateWrapper<>(ImMerchantAdministrator.class)
            .set(ImMerchantAdministrator::getRoleType, MerchantRoleType.MERCHANT_SERVICE.getRoleId())
            .eq(ImMerchantAdministrator::getAdminId, currentAdminId)
        );
        administratorMapper.update(new LambdaUpdateWrapper<>(ImMerchantAdministrator.class)
            .set(ImMerchantAdministrator::getRoleType, MerchantRoleType.MERCHANT_ADMIN.getRoleId())
            .eq(ImMerchantAdministrator::getAdminId, adminId)
        );
    }

    /**
     * 退出团队
     * 只有普通用户（非主管理员）才可以退出团队
     *
     * @param adminId 管理员ID
     * @throws IllegalArgumentException 如果是主管理员尝试退出团队
     */
    @Transactional
    public void quitTeam(Long adminId) {
        // 获取当前用户的详细信息
        ImMerchantAdministratorVo currentAdmin = merchantRepository.detailAdmin(adminId);
        if (currentAdmin == null) {
            throw new IllegalArgumentException("用户不存在");
        }

        // 检查是否为主管理员
        if (currentAdmin.isMainAdmin()) {
            throw new IllegalArgumentException("主管理员无法退出团队，请先转让管理员权限");
        }

        // 只有普通用户（客服）才能退出团队
        if (!currentAdmin.isService()) {
            throw new IllegalArgumentException("当前角色无法退出团队");
        }

        // 执行退出团队操作：清空商户ID和分组ID
        administratorMapper.deleteById(adminId);
    }

    public ImMerchantVo queryMerchantInfo(Long merchantId) {
        return merchantRepository.selectMerchant(merchantId);
    }

    public void saveMessagePrompt(MessagePromptDto promptDto) {
        administratorMapper.update(new LambdaUpdateWrapper<>(ImMerchantAdministrator.class)
            .set(ImMerchantAdministrator::getMessagePrompt, JsonUtils.toJsonString(promptDto))
            .eq(ImMerchantAdministrator::getAdminId, MerchantLoginHelper.getAdminId())
        );
    }

    public void setAdmin(Long adminId) {
        administratorMapper.update(new LambdaUpdateWrapper<>(ImMerchantAdministrator.class)
            .set(ImMerchantAdministrator::getRoleType, MerchantRoleType.MERCHANT_ADMIN.getRoleId())
            .eq(ImMerchantAdministrator::getAdminId, adminId)
        );
    }
}
