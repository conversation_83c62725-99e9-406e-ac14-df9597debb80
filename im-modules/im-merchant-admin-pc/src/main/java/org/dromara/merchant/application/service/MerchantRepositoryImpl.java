package org.dromara.merchant.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.domain.bo.ImMerchantAdministratorBo;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.im.mapper.ImChatPackageMapper;
import org.dromara.im.mapper.ImMerchantAdministratorMapper;
import org.dromara.im.mapper.ImMerchantMapper;
import org.dromara.merchant.domain.repository.MerchantRepository;
import org.dromara.merchant.domain.valueobject.MerchantEmail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 商户仓储实现类（简化版）
 *
 * <AUTHOR>
 */
@Repository
public class MerchantRepositoryImpl implements MerchantRepository {
    @Autowired
    private ImMerchantMapper merchantMapper;
    @Autowired
    private ImMerchantAdministratorMapper merchantAdministratorMapper;
    @Autowired
    private ImChatPackageMapper chatPackageMapper;

    @Override
    public ImMerchantAdministratorVo save(ImMerchantBo merchant, ImMerchantAdministratorBo administratorBo) {
        ImMerchant merchantDO = MapstructUtils.convert(merchant, ImMerchant.class);

        if (merchant.getMerchantId() == null) {
            // 新增商户
            merchantMapper.insert(merchantDO);
            merchant.setMerchantId(merchantDO.getMerchantId());
        }
        ImMerchantAdministrator adminDO = MapstructUtils.convert(administratorBo, ImMerchantAdministrator.class);
        adminDO.setMerchantId(merchant.getMerchantId());
        merchantAdministratorMapper.insert(adminDO);
        administratorBo.setAdminId(adminDO.getAdminId());
        ImMerchantAdministratorVo imMerchantAdministratorVo = merchantAdministratorMapper.selectVoById(adminDO.getAdminId());
        return imMerchantAdministratorVo;
    }

    @Override
    public ImMerchantVo findById(Long merchantId) {
        if (merchantId == null) {
            return null;
        }

        ImMerchant merchantDO = merchantMapper.selectById(merchantId);
        if (merchantDO == null) {
            return null;
        }
        return MapstructUtils.convert(merchantDO, ImMerchantVo.class);
    }

    @Override
    public ImMerchantAdministratorVo findByEmail(MerchantEmail email) {
        LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImMerchantAdministrator::getEmail, email.getValue());
        ImMerchantAdministrator administrator = merchantAdministratorMapper.selectOne(wrapper);
        if (administrator == null) {
            return null;
        }
        return MapstructUtils.convert(administrator, ImMerchantAdministratorVo.class);
    }

    @Override
    public ImMerchantAdministratorVo findAdminByEmail(MerchantEmail email) {
        LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImMerchantAdministrator::getEmail, email.getValue());

        ImMerchantAdministrator adminDO = merchantAdministratorMapper.selectOne(wrapper);
        if (adminDO == null) {
            return null;
        }
        return MapstructUtils.convert(adminDO, ImMerchantAdministratorVo.class);
    }

    @Override
    public boolean existsByEmail(MerchantEmail email) {
        LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImMerchantAdministrator::getEmail, email.getValue());
        return merchantAdministratorMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean existsAdminByEmail(MerchantEmail email) {
        LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImMerchantAdministrator::getEmail, email.getValue());
        return merchantAdministratorMapper.selectCount(wrapper) > 0;
    }

    @Override
    public ImMerchantAdministratorVo detailAdmin(Long adminId) {
        return merchantAdministratorMapper.selectVoById(adminId);
    }

    @Override
    public ImMerchantAdministratorVo selectByMerchantId(Long merchantId) {
        return merchantAdministratorMapper.selectVoById(merchantId);
    }

    @Override
    public List<ImMerchantAdministratorVo> selectList(ImMerchantAdministratorBo bo, Long merchantId, String... roleType) {
        LambdaQueryWrapper<ImMerchantAdministrator> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImMerchantAdministrator::getMerchantId, merchantId);
        queryWrapper.like(StringUtils.isNotEmpty(bo.getEmail()), ImMerchantAdministrator::getEmail, bo.getEmail());
        queryWrapper.eq(Objects.nonNull(bo.getMemberGroupId()), ImMerchantAdministrator::getMemberGroupId, bo.getMemberGroupId());
        queryWrapper.eq(StringUtils.isNotEmpty(bo.getNickname()), ImMerchantAdministrator::getNickname, bo.getNickname());
        queryWrapper.in(ImMerchantAdministrator::getRoleType, Arrays.asList(roleType));
        List<ImMerchantAdministrator> imMerchantAdministrators = merchantAdministratorMapper.selectList(queryWrapper);
        return imMerchantAdministrators.stream()
            .map(var -> MapstructUtils.convert(var, ImMerchantAdministratorVo.class)).toList();
    }

    @Override
    public void modifyPassword(ImMerchantAdministrator administrator) {
        merchantAdministratorMapper.update(new LambdaUpdateWrapper<>(ImMerchantAdministrator.class)
            .set(ImMerchantAdministrator::getPassword, administrator.getPassword())
            .eq(ImMerchantAdministrator::getAdminId, administrator.getAdminId())
        );
    }

    @Override
    public ImMerchantVo selectMerchant(Long merchantId) {

        ImMerchantVo imMerchantVo = merchantMapper.selectVoById(merchantId);
        return imMerchantVo;
    }
}
