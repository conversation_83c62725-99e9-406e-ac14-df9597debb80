package org.dromara.merchant.application.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.merchant.dto.MerchantLoginUser;

/**
 * 商户登录助手类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MerchantLoginHelper {

    /**
     * 商户登录
     */
    public static String login(ImMerchantAdministratorVo merchant) {
        return login(merchant, new SaLoginParameter());
    }

    /**
     * 商户登录
     */
    public static String login(ImMerchantAdministratorVo merchant, SaLoginParameter parameter) {
        // 创建商户登录用户信息
        MerchantLoginUser loginUser = MerchantLoginUser.create(merchant);

        // 使用SaToken进行登录
        parameter = ObjectUtil.defaultIfNull(parameter, new SaLoginParameter());
        LoginHelper.login(loginUser, parameter);

        // 返回生成的token
        return StpUtil.getTokenValue();
    }

    /**
     * 获取当前登录的商户用户信息
     */
    public static MerchantLoginUser getLoginUser() {
        return LoginHelper.getLoginUser();
    }

    /**
     * 获取当前登录的商户ID
     */
    public static Long getMerchantId() {
        MerchantLoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getDeptId() : null;
    }

    /**
     * 获取当前登录的管理员ID
     */
    public static Long getAdminId() {
        MerchantLoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getUserId() : null;
    }

    /**
     * 检查当前用户是否已登录
     */
    public static boolean isLogin() {
        return LoginHelper.isLogin();
    }

    /**
     * 登出
     */
    public static void logout() {
        StpUtil.logout();
    }
}
