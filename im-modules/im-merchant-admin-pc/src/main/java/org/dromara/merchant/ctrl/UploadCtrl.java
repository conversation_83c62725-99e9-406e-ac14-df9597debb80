package org.dromara.merchant.ctrl;

import cn.ucloud.ufile.util.MimeTypeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.domain.R;
import org.dromara.im.domain.dto.FileUploadDto;
import org.dromara.im.utils.S3UploadUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
@Slf4j
public class UploadCtrl {

    @Value("${nginxFileForward}")
    private String nginxForward;

    @Value("${ufFolderMame}")
    private String ufFolderMame;

    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<?> upload(@RequestPart("file") MultipartFile file) {
        try {
            FileUploadDto fileUploadDto = uploadFile(file, null);
            return R.ok(fileUploadDto);
        } catch (Exception e) {
            log.error("upload", e);
        }
        return R.fail();
    }


    public FileUploadDto uploadFile(MultipartFile file, String customFolder) throws Exception {
        FileUploadDto fileUploadDto = new FileUploadDto();
        //充填参数
        this.fillParams(fileUploadDto, file, customFolder);
        String bucketName = "pkfile";
        String mimeType = MimeTypeUtil.getMimeType(file.getName());
        String keyName = ufFolderMame + "/" + fileUploadDto.getFileKey();
        S3UploadUtils.putStream(file.getInputStream(), file.getSize(), mimeType, keyName, bucketName);
        return fileUploadDto;
    }

    /**
     * 充填参数
     *
     * @param fileUploadDto
     * @param file
     */
    private void fillParams(FileUploadDto fileUploadDto, MultipartFile file, String customFolder) {
        String fileKey = S3UploadUtils.extractFilename(file);
        if (StringUtils.isNotBlank(customFolder)) {
            fileKey = customFolder + S3UploadUtils.SLASH + fileKey;
        }
        String filePath = S3UploadUtils.SLASH + nginxForward + S3UploadUtils.SLASH + ufFolderMame + S3UploadUtils.SLASH + fileKey;
        fileUploadDto.setFileSize(String.valueOf(file.getSize()));
        fileUploadDto.setFileKey(fileKey);
        fileUploadDto.setFilePath(filePath);
        fileUploadDto.setFileName(file.getOriginalFilename());
        fileUploadDto.setTarget(S3UploadUtils.dateTime());
    }

}
