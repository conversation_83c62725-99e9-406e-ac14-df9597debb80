package org.dromara.merchant.dto;

import lombok.Data;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;

import java.time.LocalDateTime;

/**
 * 认证结果（简化版）
 *
 * <AUTHOR>
 */
@Data
public class AuthenticationResult {

    // Getters and Setters
    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 管理员邮箱
     */
    private String adminEmail;

    /**
     * 管理员姓名
     */
    private String adminName;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    public AuthenticationResult() {
        this.loginTime = LocalDateTime.now();
    }

    public AuthenticationResult(String accessToken, ImMerchantAdministratorVo admin) {
        this();
        this.accessToken = accessToken;
        this.merchantId = admin.getMerchantId();
        this.adminId = admin.getAdminId();
        this.adminEmail = admin.getEmail();
        this.adminName = admin.getNickname();
    }

}
