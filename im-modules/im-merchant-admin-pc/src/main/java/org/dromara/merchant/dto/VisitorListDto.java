package org.dromara.merchant.dto;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import org.dromara.im.domain.vo.ImVisitorLabelVo;

import java.util.Date;
import java.util.List;

/**
 * 客服访客列表DTO
 * 用于展示客服的访客列表信息
 */
@Data
public class VisitorListDto {

    /**
     * 关系ID
     */
    private Long adminVisitorRelationId;

    /**
     * 客服id
     */
    private Long adminId;

    /**
     * 访客ID
     */
    private String visitorId;

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 访客头像
     */
    private String visitorAvatar;

    /**
     * 客服id
     */
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * 商户渠道名称
     */
    private String merchantChannelName;

    /**
     * 是否置顶 (0=否, 1=是)
     */
    private String isTop;

    /**
     * 是否拉黑 (0=否, 1=是)
     */
    private String isBlocked;

    /**
     * 消息总数
     */
    private Long messageCount;

    /**
     * 未读消息数
     */
    private Long unreadCount = 0L;

    /**
     * 最后消息时间
     */
    private Date lastMessageTime;

    /**
     * 最新会话ID
     */
    private Long latestConversationId;

    /**
     * 最新消息内容
     */
    private String latestMessageContent;

    /**
     * 会话状态
     */
    private String conversationStatus;

    /**
     * 在线状态 0->离线 1->在线
     */
    private String onlineStatus;

    /**
     * 最后在线时间
     */
    private Long lastOnlineTime;

    /**
     * 访客来源URL
     */
    private String fromUrl;

    /**
     * 访客IP地址
     */
    private String ipAddress;

    /**
     * IP地址区域
     */
    private String ipAddressArea = "中国-香港";
    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 访客标签（JSON格式）
     */
    private String visitorTags;

    /**
     * 备注信息
     */
    private String chatRemark;

    /**
     * 服务评分
     */
    private Double serviceRating;


    private String language = "en-US";

    /**
     * 满意度评分 1-5
     */
    @ExcelProperty(value = "满意度评分 1-5")
    private Long rating;

    /**
     * 结束原因
     */
    @ExcelProperty(value = "结束原因")
    private String closedReason;

    /**
     * 游客反馈
     */
    @ExcelProperty(value = "游客反馈")
    private String feedback;


    private List<ImVisitorLabelVo> visitorLabelVos;

    /**
     * 获取显示名称（优先使用访客姓名，否则使用访客ID）
     */
    public String getDisplayName() {
        if (visitorName != null && !visitorName.trim().isEmpty()) {
            return visitorName;
        }
        return visitorId != null ? visitorId : "未知访客";
    }
}
