package org.dromara.merchant.ctrl;

import org.dromara.common.core.domain.R;
import org.dromara.im.domain.bo.ImChannelConfigBo;
import org.dromara.im.service.IImChannelConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渠道配置
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/api/channelConfig")
public class ChannelConfigCtrl {


    @Autowired
    private IImChannelConfigService iImChannelConfigService;


    /**
     * 查询渠道配置列表
     */
    @GetMapping("/list")
    public R<?> list() {
        ImChannelConfigBo bo = new ImChannelConfigBo();
        return R.ok(iImChannelConfigService.queryList(bo));
    }

}
