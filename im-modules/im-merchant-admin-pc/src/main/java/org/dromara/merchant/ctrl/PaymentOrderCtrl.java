package org.dromara.merchant.ctrl;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.bo.ImPaymentOrderBo;
import org.dromara.im.domain.dto.PayRequest;
import org.dromara.im.service.IImPaymentOrderService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 订单列表
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/paymentOrder")
public class PaymentOrderCtrl extends BaseController {

    private final IImPaymentOrderService imPaymentOrderService;

    /**
     * 查询支付订单列表
     */
    @GetMapping("/list")
    public R<?> list(ImPaymentOrderBo bo, PageQuery pageQuery) {
        bo.setMerchantId(MerchantLoginHelper.getMerchantId());
        return R.ok(imPaymentOrderService.queryPageList(bo, pageQuery));
    }

    /**
     * 创建订单
     */
    @PostMapping("/create")
    public R<?> create(@RequestBody PayRequest bo) {
        imPaymentOrderService.create(bo, MerchantLoginHelper.getMerchantId());
        return R.ok();
    }

}
