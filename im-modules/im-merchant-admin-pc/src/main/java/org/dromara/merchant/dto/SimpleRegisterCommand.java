package org.dromara.merchant.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 简化注册命令
 *
 * <AUTHOR>
 */
@Data
public class SimpleRegisterCommand {

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    @NotBlank(message = "企业名称不能为空")
    @Size(max = 100, message = "企业名称不能超过100个字符")
    private String companyName;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 18, message = "密码长度必须在6-18位之间")
    private String password;

}
