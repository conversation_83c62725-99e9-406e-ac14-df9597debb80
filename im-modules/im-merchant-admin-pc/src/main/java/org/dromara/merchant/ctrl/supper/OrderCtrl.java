package org.dromara.merchant.ctrl.supper;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.im.domain.bo.ImPaymentOrderBo;
import org.dromara.im.service.IImPaymentOrderService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/order")
public class OrderCtrl {

    private final IImPaymentOrderService paymentOrderService;


    /**
     * 查询支付订单列表
     */
    @GetMapping("/list")
    public R<?> list(ImPaymentOrderBo bo, PageQuery pageQuery) {
        return R.ok(paymentOrderService.queryPageList(bo, pageQuery));
    }

    @PostMapping("/paySuccess")
    public R<?> paySuccess(@RequestBody ImPaymentOrderBo order) {
        paymentOrderService.paySuccess(order);
        return R.ok();
    }

    @PostMapping("/payFailed")
    public R<?> payFailed(@RequestBody ImPaymentOrderBo order) {
        paymentOrderService.payFailed(order);
        return R.ok();
    }
}
