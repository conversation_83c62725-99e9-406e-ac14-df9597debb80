package org.dromara.merchant.domain.valueobject;

import lombok.Data;

/**
 * 浏览器信息值对象
 *
 * <AUTHOR>
 */
@Data
public class BrowserInfo {

    /**
     * 浏览器名称
     */
    private String browserName;

    /**
     * 浏览器版本
     */
    private String browserVersion;

    /**
     * 浏览器引擎
     */
    private String engine;

    /**
     * 渲染引擎
     */
    private String renderingEngine;

    /**
     * 是否支持JavaScript
     */
    private Boolean supportsJavaScript;

    /**
     * 是否支持Cookie
     */
    private Boolean supportsCookies;

    /**
     * 是否支持LocalStorage
     */
    private Boolean supportsLocalStorage;

    /**
     * 是否支持SessionStorage
     */
    private Boolean supportsSessionStorage;

    /**
     * 是否支持WebSocket
     */
    private Boolean supportsWebSocket;

    /**
     * 是否支持WebRTC
     */
    private Boolean supportsWebRTC;

    /**
     * 是否支持Canvas
     */
    private Boolean supportsCanvas;

    /**
     * 是否支持WebGL
     */
    private Boolean supportsWebGL;

    /**
     * 是否支持Touch
     */
    private Boolean supportsTouch;

    /**
     * 是否支持Geolocation
     */
    private Boolean supportsGeolocation;

    /**
     * 是否支持Notifications
     */
    private Boolean supportsNotifications;

    /**
     * 是否支持Push
     */
    private Boolean supportsPush;

    /**
     * 是否支持ServiceWorker
     */
    private Boolean supportsServiceWorker;

    /**
     * 是否支持PWA
     */
    private Boolean supportsPWA;

    /**
     * 是否支持ES6
     */
    private Boolean supportsES6;

    /**
     * 是否支持ES7
     */
    private Boolean supportsES7;

    /**
     * 是否支持ES8
     */
    private Boolean supportsES8;

    /**
     * 是否支持ES9
     */
    private Boolean supportsES9;

    /**
     * 是否支持ES10
     */
    private Boolean supportsES10;

    /**
     * 是否支持ES11
     */
    private Boolean supportsES11;

    /**
     * 是否支持ES12
     */
    private Boolean supportsES12;
}
