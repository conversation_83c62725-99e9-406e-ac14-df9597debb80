package org.dromara.merchant.ctrl;

import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.im.domain.dto.ConversationQueuesDto;
import org.dromara.im.domain.vo.ImMerchantChannelVo;
import org.dromara.im.service.IImMerchantChannelService;
import org.dromara.im.service.IImMerchantService;
import org.dromara.merchant.application.service.MerchantLoginHelper;
import org.dromara.merchant.request.AssignmentRules;
import org.dromara.merchant.request.DialogRules;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 对话设置
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/api/dialogSettings")
public class DialogCtrl extends BaseController {

    @Autowired
    private IImMerchantChannelService merchantChannelService;

    @Autowired
    private IImMerchantService iImMerchantService;

    /**
     * 对话规则查询
     */
    @GetMapping("/queryDialogRules/{merchantChannelId}")
    public R<?> queryDialogRules(@PathVariable Long merchantChannelId) {
        Long merchantId = MerchantLoginHelper.getMerchantId();
        ImMerchantChannelVo merchantChannelVo = merchantChannelService.queryDialogRules(merchantId, merchantChannelId);
        merchantChannelVo.buildChannelDialogRulesDto();
        return R.ok(merchantChannelVo.getChannelDialogRulesDto());
    }


    /**
     * 保存对话规则
     */
    @PostMapping("/dialogRules")
    public R<?> dialogRules(@RequestBody DialogRules rules) {
        merchantChannelService.dialogRules(rules.getMerchantChannelId(), rules.getChannelDialogRulesDto());
        return R.ok();
    }


    /**
     * 分配规则查询
     */
    @GetMapping("/queryAssignmentRules/{merchantChannelId}")
    public R<?> queryAssignmentRules(@PathVariable Long merchantChannelId) {
        Long merchantId = MerchantLoginHelper.getMerchantId();
        ImMerchantChannelVo merchantChannelVo = merchantChannelService.queryAssignmentRules(merchantId, merchantChannelId);
        merchantChannelVo.buildAssignmentRulesDto();
        return R.ok(merchantChannelVo.getAssignmentRulesDto());
    }

    /**
     * 保存分配规则
     */
    @PostMapping("/assignmentRules")
    public R<?> assignmentRules(@RequestBody AssignmentRules rules) {
        merchantChannelService.assignmentRules(rules.getMerchantChannelId(), rules.getAssignmentRulesDto());
        return R.ok();
    }

    /**
     * 查询对话排队
     */
    @GetMapping("/queryConversationQueues")
    public R<ConversationQueuesDto> queryConversationQueues() {
        Long merchantId = MerchantLoginHelper.getMerchantId();
        ConversationQueuesDto conversationQueues = iImMerchantService.queryConversationQueues(merchantId);
        return R.ok(conversationQueues);
    }


    /**
     * 保存对话排队
     */
    @PostMapping("/conversationQueues")
    public R<?> conversationQueues(@RequestBody ConversationQueuesDto conversationQueuesDto) {
        Long merchantId = MerchantLoginHelper.getMerchantId();
        iImMerchantService.conversationQueues(merchantId, conversationQueuesDto);
        return R.ok();
    }

}
