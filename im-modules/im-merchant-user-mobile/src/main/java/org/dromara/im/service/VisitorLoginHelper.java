package org.dromara.im.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.VisitorLoginUser;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.im.domain.vo.ImVisitorVo;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class VisitorLoginHelper {

    /**
     * 商户登录
     */
    public static String login(ImVisitorVo visitorVo) {
        return login(visitorVo, new SaLoginParameter());
    }

    /**
     * 商户登录
     */
    public static String login(ImVisitorVo vo, SaLoginParameter parameter) {
        // 创建商户登录用户信息
        VisitorLoginUser loginUser = create(vo);
        // 使用SaToken进行登录
        parameter = ObjectUtil.defaultIfNull(parameter, new SaLoginParameter());
        LoginHelper.login(loginUser, parameter);
        // 返回生成的token
        return StpUtil.getTokenValue();
    }

    public static VisitorLoginUser create(ImVisitorVo visitorVo) {
        VisitorLoginUser loginUser = new VisitorLoginUser();
        // 设置基础信息
        loginUser.setUserId(visitorVo.getId());
        loginUser.setVisitorId(visitorVo.getVisitorId());
        loginUser.setVisitorName(visitorVo.getVisitorName());
        // 设置商户相关信息
        loginUser.setMerchantId(visitorVo.getMerchantId());
        loginUser.setMerchantChannelId(visitorVo.getMerchantChannelId());

        // 设置时间信息
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(System.currentTimeMillis() + 365 * 24 * 60 * 60 * 1000L); // 365天
        loginUser.setUserType("visitor");
        return loginUser;
    }

    /**
     * 获取当前登录的商户用户信息
     */
    public static VisitorLoginUser getLoginUser() {
        return LoginHelper.getLoginUser();
    }


    /**
     * 获取当前登录的管理员ID
     */
    public static String getVisitorId() {
        VisitorLoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getVisitorId() : null;
    }


}
