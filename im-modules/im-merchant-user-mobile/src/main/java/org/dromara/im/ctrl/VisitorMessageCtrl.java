package org.dromara.im.ctrl;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMessageVo;
import org.dromara.im.dto.VisitorConnectionDto;
import org.dromara.im.service.*;
import org.dromara.im.utils.ClientIpUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 游客消息推送控制器
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/api/visitor/")
public class VisitorMessageCtrl {

    private final IImVisitorService visitorService;

    private final IImConversationService conversationService;

    private final IImAdminVisitorRelationService imAdminVisitorRelationService;

    private final IImMessageService messageService;

    /**
     * 游客连接处理（包含消息推送）
     * 参考PHP源码中的游客连接处理逻辑
     */
    @PostMapping("/connect")
    public R<?> handleVisitorConnection(HttpServletRequest request, @RequestBody VisitorConnectionDto params) {
        try {
            String visitorId = params.getVisitorId();
            Long merchantId = params.getMerchantId() == null ? 1 : params.getMerchantId();
            String visitorName = params.getVisitorName();
            String clientIp = ClientIpUtils.getClientIp(request);
            String fromUrl = params.getFromUrl();
            String userAgent = params.getUserAgent();
            String avatarUrl = params.getAvatarUrl();
            Long channel = params.getMerchantChannel();
            String language = params.getLanguage();
            String fingerprint = params.getFingerprint();

            log.info("处理游客连接: visitorId={}, businessId={}, visitorName={}",
                visitorId, merchantId, visitorName);

            // 处理游客连接
            var visitor = visitorService.handleVisitorConnection(visitorId, merchantId, visitorName, clientIp, fromUrl,
                userAgent, avatarUrl, channel, language, fingerprint);

            // 推送游客连接通知
            String login = VisitorLoginHelper.login(visitor);
            HashMap<Object, Object> data = new HashMap<>();
            data.put("token", login);
            data.put("visitorId", visitor.getVisitorId());
            data.put("fingerprint", visitor.getFingerprint());
            data.put("merchantId", visitor.getMerchantChannelId());
            data.put("visitorName", visitor.getVisitorName());
            data.put("visitorAvatar", visitor.getAvatarUrl());
            return R.ok(data);
        } catch (Exception e) {
            log.error("处理游客连接失败", e);
            return R.fail("connection exception");
        }
    }


    /**
     * 获取聊天记录
     */
    @GetMapping("/chatRecord")
    public R<TableDataInfo<ImMessageVo>> chatRecord(String visitorId, PageQuery pageQuery) {
        TableDataInfo<ImMessageVo> dataInfo = messageService.queryVisitorPageList(visitorId, pageQuery);
        return R.ok(dataInfo);
    }


    /**
     * 服务评价
     */
    @GetMapping("/evaluateService")
    public R<String> evaluate(Long adminId, Long rating, String feedback) {
        String visitorId = VisitorLoginHelper.getVisitorId();
        Long merchantId = VisitorLoginHelper.getLoginUser().getMerchantId();

        // 1. 查询游客跟客服的会话关系
        ImConversationVo conversation = conversationService.queryLatestByVisitorAndAdmin(visitorId, adminId, merchantId);
        if (conversation == null) {
            return R.ok();
        }
        // 2. 更新会话评价
        Boolean success = conversationService.updateConversationRating(conversation, rating, feedback);
        // 3. 更新imAdminVisitorRelationService里面的endService
        imAdminVisitorRelationService.updateEndService(merchantId, visitorId, adminId);
        // 4. 发送评价消息给客服
        return success ? R.ok() : R.fail();
    }


}
