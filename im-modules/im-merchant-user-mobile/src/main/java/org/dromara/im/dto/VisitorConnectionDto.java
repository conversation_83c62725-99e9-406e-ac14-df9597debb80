package org.dromara.im.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 游客连接请求DTO
 * 用于封装游客连接时的所有参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorConnectionDto implements Serializable {

    /**
     * 游客ID（必填）
     */
    @NotBlank(message = "游客ID不能为空")
    private String visitorId;

    /**
     * 商户ID（必填）
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 游客名称
     */
    private String visitorName;

    /**
     * 来源URL
     */
    private String fromUrl;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 渠道
     */
    private Long channel;

    /**
     * 语言
     */
    private String language;

    /**
     * 浏览器指纹
     */
    private String fingerprint;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * Cookie ID
     */
    private String cookieId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 浏览器信息
     */
    private String browserInfo;

    /**
     * 位置信息
     */
    private String locationInfo;

    /**
     * 时区
     */
    private String timezone;

    /**
     * UTM来源
     */
    private String utmSource;

    /**
     * UTM媒介
     */
    private String utmMedium;

    /**
     * UTM广告系列
     */
    private String utmCampaign;

    /**
     * UTM内容
     */
    private String utmContent;

    /**
     * UTM关键词
     */
    private String utmTerm;

    /**
     * 自定义字段（JSON格式）
     */
    private String customFields;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商品信息（JSON格式）
     */
    private String productInfo;

    /**
     * 客户端IP地址（由服务端自动填充）
     */
    private String clientIp;

    /**
     * 验证必要字段
     */
    public boolean isValid() {
        return this.visitorId != null && !this.visitorId.trim().isEmpty()
            && this.merchantId != null && this.merchantId > 0;
    }
}
