<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>im-server</artifactId>
        <groupId>org.dromara</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>im-common-bom</module>
        <module>im-common-core</module>
        <module>im-common-doc</module>
        <module>im-common-excel</module>
        <module>im-common-idempotent</module>
        <module>im-common-job</module>
        <module>im-common-log</module>
        <module>im-common-mail</module>
        <module>im-common-mybatis</module>
        <module>im-common-oss</module>
        <module>im-common-ratelimiter</module>
        <module>im-common-redis</module>
        <module>im-common-satoken</module>
        <module>im-common-security</module>
        <module>im-common-sms</module>
        <module>im-common-web</module>
        <module>im-common-translation</module>
        <module>im-common-sensitive</module>
        <module>im-common-json</module>
        <module>im-common-encrypt</module>
        <module>im-common-websocket</module>
        <module>im-common-sse</module>
        <module>im-common-merchant</module>
        <module>im-common-social</module>
    </modules>

    <artifactId>im-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
