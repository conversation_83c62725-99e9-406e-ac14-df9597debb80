# 游客访问功能实现说明

## 概述

基于PHP源码的游客访问逻辑，使用MVC三层架构实现的Java版本游客访问功能。

## 文件结构

```
im-common-merchant/
├── src/main/java/org/dromara/im/
│   ├── domain/
│   │   ├── ImVisitor.java              # 游客实体类
│   │   ├── bo/
│   │   │   └── ImVisitorBo.java        # 游客业务对象
│   │   └── vo/
│   │       └── ImVisitorVo.java        # 游客视图对象
│   ├── mapper/
│   │   └── ImVisitorMapper.java        # 数据访问层
│   ├── service/
│   │   ├── IImVisitorService.java      # 服务接口
│   │   └── impl/
│   │       └── ImVisitorServiceImpl.java # 服务实现
│   └── controller/
│       └── ImVisitorController.java     # 控制器
└── VISITOR_IMPLEMENTATION.md           # 实现说明文档
```

## 核心功能

### 1. 游客连接处理

- **功能**: 处理游客连接，区分新老用户
- **实现**: `handleVisitorConnection()` 方法
- **逻辑**:
    - 根据visitorId和businessId查找现有游客
    - 老用户：更新状态为在线，增加访问次数
    - 新用户：创建新游客记录

### 2. 游客离线处理

- **功能**: 设置游客离线状态
- **实现**: `handleVisitorOffline()` 方法

### 3. 游客管理功能

- **拉黑/取消拉黑**: `blockVisitor()` / `unblockVisitor()`
- **置顶/取消置顶**: `topVisitor()` / `untopVisitor()`
- **更新消息时间**: `updateVisitorMessageTime()`

### 4. 游客查找与创建

- **Cookie ID查找**: `findOrCreateByCookieId()` - 基于Cookie标识
- **指纹查找**: `findOrCreateByFingerprint()` - 基于浏览器指纹

### 5. 统计功能

- **在线游客数量**: `getOnlineVisitorCount()`
- **新增游客统计**: 今日/本周/本月新增数量
- **游客列表**: 在线/被拉黑/置顶游客列表

## API接口

### 基础CRUD接口

- `GET /im/visitor/list` - 分页查询游客列表
- `GET /im/visitor/{id}` - 获取游客详情
- `POST /im/visitor` - 新增游客
- `PUT /im/visitor` - 修改游客
- `DELETE /im/visitor/{ids}` - 删除游客

### 业务功能接口

- `POST /im/visitor/connect` - 游客连接处理
- `POST /im/visitor/offline` - 游客离线处理
- `POST /im/visitor/message` - 更新消息时间
- `POST /im/visitor/block` - 拉黑游客
- `POST /im/visitor/unblock` - 取消拉黑
- `POST /im/visitor/top` - 置顶游客
- `POST /im/visitor/untop` - 取消置顶

### 查找接口

- `POST /im/visitor/findOrCreateByCookie` - Cookie查找
- `POST /im/visitor/findOrCreateByFingerprint` - 指纹查找

### 统计接口

- `GET /im/visitor/onlineCount` - 在线数量
- `GET /im/visitor/todayNewCount` - 今日新增
- `GET /im/visitor/weekNewCount` - 本周新增
- `GET /im/visitor/monthNewCount` - 本月新增

### 列表接口

- `GET /im/visitor/onlineList` - 在线游客列表
- `GET /im/visitor/blockedList` - 被拉黑游客列表
- `GET /im/visitor/topList` - 置顶游客列表
- `GET /im/visitor/search` - 搜索游客
- `GET /im/visitor/timeRange` - 时间范围查询

## 数据库设计

### 游客表 (visitor)

```sql
CREATE TABLE `visitor`
(
    `id`                bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `visitor_id`        varchar(64) NOT NULL COMMENT '游客唯一标识',
    `business_id`       bigint      NOT NULL COMMENT '商户ID',
    `visitor_name`      varchar(100) DEFAULT NULL COMMENT '游客显示名称',
    `real_name`         varchar(100) DEFAULT NULL COMMENT '游客真实姓名',
    `phone`             varchar(20)  DEFAULT NULL COMMENT '联系电话',
    `email`             varchar(100) DEFAULT NULL COMMENT '邮箱地址',
    `avatar_url`        varchar(500) DEFAULT NULL COMMENT '头像URL',
    `channel`           varchar(50)  DEFAULT NULL COMMENT '访问渠道标识',
    `ip_address`        varchar(45)  DEFAULT NULL COMMENT '访客IP地址(支持IPv6)',
    `location_info`     varchar(200) DEFAULT NULL COMMENT 'IP地理位置信息',
    `from_url`          varchar(500) DEFAULT NULL COMMENT '来源页面URL',
    `user_agent`        varchar(500) DEFAULT NULL COMMENT '浏览器信息',
    `device_info`       text COMMENT '设备信息JSON格式',
    `browser_info`      text COMMENT '浏览器详细信息JSON格式',
    `language`          varchar(20)  DEFAULT 'zh-CN' COMMENT '客户端语言',
    `timezone`          varchar(50)  DEFAULT NULL COMMENT '时区',
    `status`            tinyint      DEFAULT '0' COMMENT '状态: 0=离线, 1=在线',
    `is_blocked`        tinyint      DEFAULT '0' COMMENT '是否被拉黑: 0=正常, 1=已拉黑',
    `is_top`            tinyint      DEFAULT '0' COMMENT '是否置顶: 0=否, 1=是',
    `login_count`       int          DEFAULT '0' COMMENT '访问次数',
    `last_visit_time`   datetime     DEFAULT NULL COMMENT '最后访问时间',
    `last_message_time` datetime     DEFAULT NULL COMMENT '最后消息时间',
    `session_id`        varchar(100) DEFAULT NULL COMMENT '会话ID',
    `cookie_id`         varchar(100) DEFAULT NULL COMMENT 'Cookie标识',
    `fingerprint`       varchar(200) DEFAULT NULL COMMENT '浏览器指纹',
    `utm_source`        varchar(100) DEFAULT NULL COMMENT 'UTM来源',
    `utm_medium`        varchar(100) DEFAULT NULL COMMENT 'UTM媒介',
    `utm_campaign`      varchar(100) DEFAULT NULL COMMENT 'UTM广告系列',
    `utm_content`       varchar(100) DEFAULT NULL COMMENT 'UTM内容',
    `utm_term`          varchar(100) DEFAULT NULL COMMENT 'UTM关键词',
    `custom_fields`     text COMMENT '自定义字段JSON格式',
    `remark`            text COMMENT '备注信息',
    `created_time`      datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '首次访问时间',
    `updated_time`      datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted`        tinyint      DEFAULT '0' COMMENT '逻辑删除: 0=正常, 1=已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_visitor_business` (`visitor_id`,`business_id`),
    KEY                 `idx_business_status` (`business_id`,`status`),
    KEY                 `idx_cookie_business` (`cookie_id`,`business_id`),
    KEY                 `idx_fingerprint_business` (`fingerprint`,`business_id`),
    KEY                 `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游客访问表';
```

## 核心逻辑

### 游客ID生成

基于PHP的生成逻辑：

```java
private String generateVisitorId() {
    // 基于PHP的生成逻辑：bin2hex(pack('N', time())).strtolower($common->rand(8))
    long timestamp = System.currentTimeMillis() / 1000;
    String timestampHex = Long.toHexString(timestamp);
    String randomStr = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    return timestampHex + randomStr.toLowerCase();
}
```

### 新老用户判断

1. **Cookie方式**: 通过cookie_id查找现有游客
2. **指纹方式**: 通过fingerprint查找现有游客
3. **直接ID**: 通过visitor_id直接查找

### 状态管理

- **在线状态**: status字段，1=在线，0=离线
- **拉黑状态**: is_blocked字段，1=已拉黑，0=正常
- **置顶状态**: is_top字段，1=置顶，0=正常

## 与PHP逻辑对比

### 相同点

1. **游客ID生成**: 保持相同的生成逻辑
2. **新老用户判断**: 基于唯一标识判断
3. **状态管理**: 在线/离线状态管理
4. **访问统计**: 访问次数统计

### 改进点

1. **架构优化**: 使用MVC三层架构，职责分离
2. **类型安全**: 使用强类型Java语言
3. **接口设计**: RESTful API设计
4. **扩展性**: 支持更多查询和统计功能

## 使用示例

### 游客连接

```java
// 游客连接处理
ImVisitorVo visitor = visitorService.handleVisitorConnection(
        "visitor123",           // 游客ID
        1L,                     // 商户ID
        "张三",                  // 游客名称
        "*************",       // IP地址
        "https://example.com",  // 来源URL
        "Mozilla/5.0...",      // 用户代理
        "/avatar.jpg",          // 头像
        "web",                  // 渠道
        "zh-CN"                // 语言
    );
```

### 游客离线

```java
// 游客离线处理
visitorService.handleVisitorOffline("visitor123",1L);
```

### 拉黑游客

```java
// 拉黑游客
visitorService.blockVisitor("visitor123",1L);
```

## 注意事项

1. **并发处理**: 游客连接可能存在并发情况，需要适当的锁机制
2. **数据一致性**: 确保游客状态的一致性
3. **性能优化**: 大量游客时的查询性能优化
4. **安全考虑**: 游客信息的隐私保护
5. **扩展性**: 支持更多游客属性和行为分析

## 后续优化

1. **缓存机制**: 添加Redis缓存提升性能
2. **异步处理**: 非关键操作使用异步处理
3. **监控告警**: 添加游客行为监控
4. **数据分析**: 游客行为数据分析功能
5. **多租户**: 支持多租户隔离 