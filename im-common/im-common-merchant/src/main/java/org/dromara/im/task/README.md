# 📋 对话规则定时任务说明

## 🎯 功能概述

`DialogRulesTask` 是一个定时任务类，负责处理客服系统中的各种自动化规则，包括：

- ✅ **自动结束对话** - 对话无消息超时自动结束
- ✅ **顾客离线检测** - 顾客离线超时自动结束对话
- ✅ **对话自动转接** - 首条消息超时自动转接
- ✅ **客服离线转接** - 客服离线自动转接给其他客服
- ✅ **顾客消息间隔** - 顾客长时间未发送消息自动转接

## 🔧 配置说明

### 启用/禁用定时任务
```yaml
# application.yml
im:
  dialog:
    rules:
      enabled: true  # true-启用 false-禁用，默认启用
```

### 定时任务执行频率
- **自动结束对话检查**：每分钟执行一次
- **顾客离线检查**：每30秒执行一次
- **对话自动转接检查**：每30秒执行一次
- **客服离线转接检查**：每分钟执行一次
- **顾客消息间隔检查**：每分钟执行一次

## 📊 规则配置

规则配置存储在 `im_merchant_channel` 表的 `conversation_rules` 字段中，JSON格式：

```json
{
  "autoEndDialog": "1",
  "autoEndDialogTime": 30,
  "customerOfflineAutoEnd": "1", 
  "customerOfflineTimeout": 300,
  "dialogAutoTransfer": "1",
  "firstMessageTimeout": 60,
  "maxTransferTimes": 3,
  "serviceOfflineAutoTransfer": "1",
  "customerMessageInterval": 1800
}
```

### 字段说明
| 字段 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| autoEndDialog | String | 自动结束对话开关 0-关闭 1-开启 | "0" |
| autoEndDialogTime | Integer | 自动结束对话时间（分钟） | 30 |
| customerOfflineAutoEnd | String | 顾客离线自动结束开关 | "0" |
| customerOfflineTimeout | Integer | 顾客离线超时时间（秒） | 300 |
| dialogAutoTransfer | String | 对话自动转接开关 | "0" |
| firstMessageTimeout | Integer | 首条消息超时时间（秒） | 60 |
| maxTransferTimes | Integer | 最大转接次数 | 3 |
| serviceOfflineAutoTransfer | String | 客服离线自动转接开关 | "0" |
| customerMessageInterval | Integer | 顾客消息间隔时间（秒） | 1800 |

## 🔄 工作流程

### 1. 自动结束对话
```
定时检查所有active状态会话
├─ 获取最后一条消息时间
├─ 计算距离现在的分钟数
├─ 超过配置时间 → 结束对话
└─ 发送结束通知给访客和客服
```

### 2. 顾客离线检测
```
定时检查所有active状态会话
├─ 从Redis获取顾客最后活跃时间
├─ 计算离线时长
├─ 超过配置时间 → 结束对话
└─ 发送离线通知
```

### 3. 对话自动转接
```
定时检查所有waiting状态会话
├─ 计算会话创建时长
├─ 超过首条消息超时时间 → 尝试转接
├─ 检查转接次数是否超限
├─ 查找可用客服 → 转接成功
└─ 无可用客服 → 结束对话
```

### 4. 客服离线转接
```
定时检查所有active状态会话
├─ 检查客服WebSocket连接状态
├─ 客服离线 → 尝试转接
├─ 查找其他可用客服
├─ 转接成功 → 通知相关方
└─ 转接失败 → 结束对话
```

## 📝 Redis数据结构

### 顾客活跃状态
```
Key: customer:last_active:{visitorId}
Value: 时间戳（毫秒）
TTL: 24小时
```

### 顾客最后消息时间
```
Key: customer:last_message:{visitorId}
Value: 时间戳（毫秒）
TTL: 24小时
```

### 转接次数记录
```
Key: conversation:transfer:count:{conversationId}
Value: 转接次数
TTL: 1天
```

### 规则缓存
```
Key: channel:dialog:rules:{channelId}
Value: JSON格式的规则配置
TTL: 5分钟
```

## 🚨 注意事项

### 1. 性能考虑
- 定时任务会查询所有活跃会话，数据量大时需要注意性能
- 建议在业务低峰期执行频率较高的检查
- 可以考虑分页处理大量会话

### 2. 并发安全
- 多个定时任务可能同时操作同一个会话
- 使用数据库事务确保状态更新的一致性
- 避免重复转接或结束同一个会话

### 3. 异常处理
- 定时任务中的异常不会影响其他任务执行
- 记录详细的错误日志便于排查问题
- 对于关键操作失败，可以考虑重试机制

### 4. 监控告警
- 监控定时任务的执行情况
- 统计自动结束和转接的数量
- 异常情况及时告警

## 🧪 测试建议

### 1. 功能测试
```bash
# 1. 创建测试会话
# 2. 修改规则配置为较短时间（如30秒）
# 3. 观察定时任务是否正确执行
# 4. 验证通知消息是否正确发送
```

### 2. 性能测试
```bash
# 1. 创建大量测试会话（如1000个）
# 2. 观察定时任务执行时间
# 3. 监控数据库和Redis性能
# 4. 验证是否有性能瓶颈
```

### 3. 边界测试
```bash
# 1. 测试规则配置为0或负数的情况
# 2. 测试Redis数据不存在的情况
# 3. 测试数据库连接异常的情况
# 4. 测试WebSocket连接异常的情况
```

## 📈 扩展建议

### 1. 规则引擎
- 可以考虑引入规则引擎（如Drools）
- 支持更复杂的业务规则配置
- 动态修改规则无需重启服务

### 2. 事件驱动
- 将定时检查改为事件驱动模式
- 减少不必要的数据库查询
- 提高系统响应速度

### 3. 分布式锁
- 在集群环境中使用分布式锁
- 避免多个节点重复执行相同任务
- 确保数据一致性

### 4. 指标统计
- 统计各种规则的触发次数
- 分析客服工作效率
- 优化规则配置参数
