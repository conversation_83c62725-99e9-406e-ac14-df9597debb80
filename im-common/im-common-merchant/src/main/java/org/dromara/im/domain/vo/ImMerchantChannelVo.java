package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.im.domain.ImMerchantChannel;
import org.dromara.im.domain.dto.AssignmentRulesDto;
import org.dromara.im.domain.dto.ChannelDialogRulesDto;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 商户接入渠道（聚合）视图对象 im_merchant_channel
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@AutoMapper(target = ImMerchantChannel.class)
public class ImMerchantChannelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    private Long merchantChannelId;

    /**
     * 所属商户 ID（聚合根）
     */
    private Long merchantId;

    /**
     * 商户渠道名称
     */
    private String merchantChannelName;

    /**
     * 渠道类型，例如 网页+APP、Telegram Bot
     */
    private String channelType;

    /**
     * 是否默认渠道;0->否;1->是
     */
    private String defaultChannel;

    /**
     * 渠道编码，唯一标识，如 sa0KZ3
     */
    @ExcelProperty(value = "渠道编码，唯一标识，如 sa0KZ3")
    private String channelCode;

    /**
     * 渠道名称，如 bem渠道、tgben
     */
    @ExcelProperty(value = "渠道名称，如 bem渠道、tgben")
    private String channelName;

    /**
     * 接入状态;0->待接入;1->已接入
     */
    @ExcelProperty(value = "接入状态;0->待接入;1->已接入")
    private String accessStatus;


    /**
     * 渠道语言
     */
    @ExcelProperty(value = "渠道语言")
    private String channelLanguage;

    /**
     * "是否根据IP自动识别语言0->关闭;1->开启"
     */
    @ExcelProperty(value = "是否根据IP自动识别语言")
    private String ipLanguage;

    /**
     * 对话规则
     */
    @ExcelProperty(value = "对话规则")
    private String dialogRules;

    /**
     * 分配规则
     */
    @ExcelProperty(value = "分配规则")
    private String assignmentRules;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 对话规则
     */
    @TableField(exist = false)
    private ChannelDialogRulesDto channelDialogRulesDto;

    /**
     * 分配规则
     */
    @TableField(exist = false)
    private AssignmentRulesDto assignmentRulesDto;


    public void buildChannelDialogRulesDto() {
        if (StringUtils.isNotEmpty(dialogRules)) {
            channelDialogRulesDto = JsonUtils.parseObject(dialogRules, ChannelDialogRulesDto.class);
        }else {
            channelDialogRulesDto = ChannelDialogRulesDto.getDefault();
        }
    }

    public void buildAssignmentRulesDto() {
        if (StringUtils.isNotEmpty(assignmentRules)) {
            assignmentRulesDto = JsonUtils.parseObject(assignmentRules, AssignmentRulesDto.class);
        }else {
            assignmentRulesDto = AssignmentRulesDto.getDefault();
        }
    }

    /**
     * 渠道昵称
     */
    private String merchantChannelNickname;

    /**
     * 渠道图标
     */
    private String merchantChannelIcon;


}
