package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImChannelConfig;

import java.util.Date;

/**
 * 渠道配置业务对象 im_channel_config
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImChannelConfig.class, reverseConvertGenerate = false)
public class ImChannelConfigBo extends BaseEntity {

    /**
     * 渠道配置ID
     */
    private Long channelConfigId;

    /**
     * 渠道类型，例如 网页+APP、Telegram Bot
     */
    private String channelType;

    /**
     * 渠道编码，唯一标识，如 sa0KZ3
     */
    private String channelCode;

    /**
     * 渠道名称，如 bem渠道、tgben
     */
    private String channelName;

    /**
     * 接入状态;0->待接入;1->已接入
     */
    @NotBlank(message = "接入状态;0->待接入;1->已接入不能为空", groups = {AddGroup.class, EditGroup.class})
    private String accessStatus;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;


}
