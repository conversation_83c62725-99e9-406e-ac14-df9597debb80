package org.dromara.im.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 分配规则配置DTO
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
public class AssignmentRulesDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分配方式 1=接待量平均分配 2=按顺序依次分配 3=按优先顺序分配 4=按随机分配
     */
    private String assignmentType;

    /**
     * 回头客分配开关 0=关闭 1=开启
     */
    private String returningCustomerAssignment;

    /**
     * 回头客分配策略 0=给客服留言 1=分配给其他客服
     */
    private String returningCustomerStrategy;

    /**
     * 获取默认配置
     */
    public static AssignmentRulesDto getDefault() {
        AssignmentRulesDto dto = new AssignmentRulesDto();
        dto.setAssignmentType("1"); // 默认接待量平均分配
        dto.setReturningCustomerAssignment("0");
        dto.setReturningCustomerStrategy("0");
        return dto;
    }

}
