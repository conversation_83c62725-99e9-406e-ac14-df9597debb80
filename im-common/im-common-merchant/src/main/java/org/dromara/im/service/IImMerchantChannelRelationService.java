package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImMerchantChannelRelationBo;
import org.dromara.im.domain.vo.ImMerchantChannelRelationVo;

import java.util.Collection;
import java.util.List;

/**
 * 渠道用户关系Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
public interface IImMerchantChannelRelationService {

    /**
     * 查询渠道用户关系
     *
     * @param merchantChannelRelationId 主键
     * @return 渠道用户关系
     */
    ImMerchantChannelRelationVo queryById(Long merchantChannelRelationId);

    /**
     * 分页查询渠道用户关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道用户关系分页列表
     */
    TableDataInfo<ImMerchantChannelRelationVo> queryPageList(ImMerchantChannelRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的渠道用户关系列表
     *
     * @param bo 查询条件
     * @return 渠道用户关系列表
     */
    List<ImMerchantChannelRelationVo> queryList(ImMerchantChannelRelationBo bo);

    /**
     * 新增渠道用户关系
     *
     * @param bo 渠道用户关系
     * @return 是否新增成功
     */
    Boolean insertByBo(ImMerchantChannelRelationBo bo);

    /**
     * 修改渠道用户关系
     *
     * @param bo 渠道用户关系
     * @return 是否修改成功
     */
    Boolean updateByBo(ImMerchantChannelRelationBo bo);

    /**
     * 校验并批量删除渠道用户关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
