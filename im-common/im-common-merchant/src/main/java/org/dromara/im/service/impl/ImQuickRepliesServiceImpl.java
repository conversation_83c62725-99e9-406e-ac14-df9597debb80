package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImQuickReplies;
import org.dromara.im.domain.bo.ImQuickRepliesBo;
import org.dromara.im.domain.vo.ImQuickRepliesVo;
import org.dromara.im.mapper.ImQuickRepliesMapper;
import org.dromara.im.service.IImQuickRepliesService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 团队快捷回复Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImQuickRepliesServiceImpl implements IImQuickRepliesService {

    private final ImQuickRepliesMapper baseMapper;

    /**
     * 查询团队快捷回复
     *
     * @param quickRepliesId 主键
     * @return 团队快捷回复
     */
    @Override
    public ImQuickRepliesVo queryById(Long quickRepliesId) {
        return baseMapper.selectVoById(quickRepliesId);
    }

    /**
     * 分页查询团队快捷回复列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 团队快捷回复分页列表
     */
    @Override
    public TableDataInfo<ImQuickRepliesVo> queryPageList(ImQuickRepliesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImQuickReplies> lqw = buildQueryWrapper(bo);
        Page<ImQuickRepliesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的团队快捷回复列表
     *
     * @param bo 查询条件
     * @return 团队快捷回复列表
     */
    @Override
    public List<ImQuickRepliesVo> queryList(ImQuickRepliesBo bo) {
        LambdaQueryWrapper<ImQuickReplies> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<ImQuickRepliesVo> queryList(List<Long> quickRepliesGroupIds) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<>(ImQuickReplies.class)
            .in(ImQuickReplies::getQuickRepliesGroupId, quickRepliesGroupIds)
        );
    }

    private LambdaQueryWrapper<ImQuickReplies> buildQueryWrapper(ImQuickRepliesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImQuickReplies> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImQuickReplies::getQuickRepliesId);
        lqw.eq(Objects.nonNull(bo.getMerchantId()), ImQuickReplies::getMerchantId, bo.getMerchantId());
        lqw.eq(Objects.nonNull(bo.getAdminId()), ImQuickReplies::getAdminId, bo.getAdminId());
        lqw.eq(bo.getMerchantChannelId() != null, ImQuickReplies::getMerchantChannelId, bo.getMerchantChannelId());
        lqw.eq(bo.getQuickRepliesGroupId() != null, ImQuickReplies::getQuickRepliesGroupId, bo.getQuickRepliesGroupId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ImQuickReplies::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ImQuickReplies::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增团队快捷回复
     *
     * @param bo 团队快捷回复
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImQuickRepliesBo bo) {
        ImQuickReplies add = MapstructUtils.convert(bo, ImQuickReplies.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setQuickRepliesId(add.getQuickRepliesId());
        }
        return flag;
    }

    /**
     * 修改团队快捷回复
     *
     * @param bo 团队快捷回复
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImQuickRepliesBo bo) {
        ImQuickReplies update = MapstructUtils.convert(bo, ImQuickReplies.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImQuickReplies entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除团队快捷回复信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
