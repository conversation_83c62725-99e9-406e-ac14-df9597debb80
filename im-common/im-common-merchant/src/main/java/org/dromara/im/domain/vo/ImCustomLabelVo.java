package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImCustomLabel;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 用户标签视图对象 im_custom_label
 *
 * <AUTHOR> Li
 * @date 2025-07-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImCustomLabel.class)
public class ImCustomLabelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户标签id
     */
    @ExcelProperty(value = "用户标签id")
    private Long customLabelId;

    /**
     * 用户标签名称
     */
    @ExcelProperty(value = "用户标签名称")
    private String customLabelName;

    /**
     * 商户渠道id
     */
    @ExcelProperty(value = "商户渠道id")
    private Long merchantChannelId;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 客户数
     */
    private Long customNumber = 0L;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商户id
     */
    private Long merchantId;


}
