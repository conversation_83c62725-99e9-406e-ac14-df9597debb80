package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.im.constans.ConversationConstants;
import org.dromara.im.domain.ImConversation;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.mapper.ImConversationMapper;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImQueueService;
import org.dromara.im.utils.message.QueueMessageUtil;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 排队管理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImQueueServiceImpl implements IImQueueService {

    private final ImConversationMapper conversationMapper;
    private final IImConversationService conversationService;

    /**
     * 处理排队中的访客，尝试分配客服
     */
    @Override
    public int processWaitingQueue(Long merchantId) {
        try {
            log.info("开始处理排队队列 - merchantId: {}", merchantId);

            // 批量处理模式：每次最多处理50个排队访客
            int assignedCount = 0;
            int maxProcessCount = 50; // 每次最多处理50个

            for (int i = 0; i < maxProcessCount; i++) {
                // 获取下一个排队的访客
                ImConversationVo waitingConversation = getNextWaitingConversation(merchantId);
                if (waitingConversation == null) {
                    log.debug("没有更多排队中的访客 - merchantId: {}, 已处理: {}", merchantId, assignedCount);
                    break;
                }

                // 尝试分配可用客服
                ImMerchantAdministratorVo availableService = conversationService.autoAssignService(
                    waitingConversation.getConversationId(), merchantId, waitingConversation.getMerchantChannelId());

                if (availableService != null) {
                    // 分配客服（assignConversationToService方法内部会更新状态为active）
                    boolean assigned = assignConversationToService(waitingConversation, availableService.getAdminId());
                    if (assigned) {
                        // 同步更新内存对象状态
                        waitingConversation.setStatus("active");
                        waitingConversation.setAdminId(availableService.getAdminId());

                        assignedCount++;

                        // 通知访客分配成功
                        QueueMessageUtil.sendQueueSuccessToVisitor(waitingConversation);

                        log.info("排队访客分配成功 - conversationId: {}, adminId: {}, 第{}个",
                            waitingConversation.getConversationId(), availableService.getAdminId(), assignedCount);
                    } else {
                        // 分配失败，停止处理
                        log.warn("分配客服失败，停止处理 - conversationId: {}, adminId: {}",
                            waitingConversation.getConversationId(), availableService.getAdminId());
                        break;
                    }
                } else {
                    // 没有可用客服，停止处理
                    log.debug("暂无可用客服，停止处理 - merchantId: {}, 已处理: {}", merchantId, assignedCount);
                    break;
                }
            }

            log.info("排队处理完成 - merchantId: {}, 分配数量: {}/{}", merchantId, assignedCount, maxProcessCount);
            return assignedCount;

        } catch (Exception e) {
            log.error("处理排队队列失败 - merchantId: {}", merchantId, e);
            return 0;
        }
    }

    /**
     * 当客服结束服务时，处理排队
     */
    @Override
    public void processQueueOnServiceEnd(Long merchantId, Long adminId) {
        try {
            log.info("客服结束服务，处理排队 - merchantId: {}, adminId: {}", merchantId, adminId);

            // 检查该客服是否还能接受新访客
            // 这里可以根据客服的最大接待数量等规则判断

            // 获取下一个排队的访客
            ImConversationVo waitingConversation = getNextWaitingConversation(merchantId);
            if (waitingConversation == null) {
                log.info("没有排队中的访客 - merchantId: {}", merchantId);
                return;
            }

            // 分配给该客服（assignConversationToService方法内部会更新状态为active）
            boolean assigned = assignConversationToService(waitingConversation, adminId);
            if (assigned) {
                // 同步更新内存对象状态
                waitingConversation.setStatus("active");
                waitingConversation.setAdminId(adminId);

                // 通知访客分配成功
                QueueMessageUtil.sendQueueSuccessToVisitor(waitingConversation);

                log.info("客服结束服务分配新访客成功 - conversationId: {}, adminId: {}, 状态已更新为active",
                    waitingConversation.getConversationId(), adminId);
            }

        } catch (Exception e) {
            log.error("客服结束服务处理排队失败 - merchantId: {}, adminId: {}", merchantId, adminId, e);
        }
    }

    /**
     * 获取排队中的下一个访客
     */
    @Override
    public ImConversationVo getNextWaitingConversation(Long merchantId) {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getMerchantId, merchantId)
                .eq(ImConversation::getStatus, ConversationConstants.waiting)
                .orderByAsc(ImConversation::getCreateTime) // 按创建时间排序，先进先出
                .last("LIMIT 1");

            return conversationMapper.selectVoOne(wrapper);

        } catch (Exception e) {
            log.error("获取排队访客失败 - merchantId: {}", merchantId, e);
            return null;
        }
    }

    /**
     * 分配访客给指定客服
     */
    @Override
    public boolean assignConversationToService(ImConversationVo conversation, Long adminId) {
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate(ImConversation.class)
                .set(ImConversation::getAdminId, adminId)
                .set(ImConversation::getStatus, ConversationConstants.active)
                .set(ImConversation::getUpdateTime, new Date())
                .eq(ImConversation::getConversationId, conversation.getConversationId())
                .eq(ImConversation::getStatus, ConversationConstants.waiting) // 确保只更新等待中的会话
                ;

            int updated = conversationMapper.update(null, updateWrapper);
            boolean success = updated > 0;

            if (success) {
                log.info("分配访客给客服成功 - conversationId: {}, adminId: {}",
                    conversation.getConversationId(), adminId);
            } else {
                log.warn("分配访客给客服失败，可能会话状态已变更 - conversationId: {}, adminId: {}",
                    conversation.getConversationId(), adminId);
            }

            return success;

        } catch (Exception e) {
            log.error("分配访客给客服异常 - conversationId: {}, adminId: {}",
                conversation.getConversationId(), adminId, e);
            return false;
        }
    }
}
