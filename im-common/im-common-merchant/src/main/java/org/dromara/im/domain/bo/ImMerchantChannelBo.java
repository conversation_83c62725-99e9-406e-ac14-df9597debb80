package org.dromara.im.domain.bo;

import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImMerchantChannel;

/**
 * 商户接入渠道（聚合）业务对象 im_merchant_channel
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImMerchantChannel.class, reverseConvertGenerate = false)
public class ImMerchantChannelBo extends BaseEntity {

    /**
     * 主键 ID
     */
    private Long merchantChannelId;

    /**
     * 所属商户 ID（聚合根）
     */
    private Long merchantId;


    /**
     * 商户渠道名称
     */
    @NotBlank(message = "渠道名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String merchantChannelName;

    /**
     * 渠道配置ID
     */
    @NotNull(message = "渠道配置ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long channelConfigId;

    /**
     * 渠道类型，例如 网页+APP、Telegram Bot
     */
    private String channelType;

    /**
     * 是否默认渠道;0->否;1->是
     */
    private String defaultChannel;

    /**
     * 渠道编码，唯一标识，如 sa0KZ3
     */
    private String channelCode;

    /**
     * 渠道名称，如 bem渠道、tgben
     */
    private String channelName;

    /**
     * 接入状态;0->待接入;1->已接入
     */
    private String accessStatus;

    /**
     * 渠道语言
     */
    @ExcelProperty(value = "渠道语言")
    private String channelLanguage;

    /**
     * "是否根据IP自动识别语言0->关闭;1->开启"
     */
    @ExcelProperty(value = "是否根据IP自动识别语言")
    private String ipLanguage;

    /**
     * 对话规则
     */
    private String dialogRules;

    /**
     * 分配规则
     */
    private String assignmentRules;

    /**
     * 渠道昵称
     */
    private String merchantChannelNickname;

    /**
     * 渠道图标
     */
    private String merchantChannelIcon;
}
