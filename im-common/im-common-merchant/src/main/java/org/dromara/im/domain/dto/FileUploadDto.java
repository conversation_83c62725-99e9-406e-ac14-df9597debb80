package org.dromara.im.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FileUploadDto {

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private String fileSize;

    /**
     * 文件key
     */
    private String fileKey;

    /**
     * 文件key加上桶名称的路径
     */
    private String filePath;

    /**
     * minio文件夹名称
     */
    private String target;

    /**
     * 访问url地址
     */
    private String fileUrl;

    /**
     * 域名
     */
    private String domain;
}
