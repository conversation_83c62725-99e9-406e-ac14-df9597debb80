package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImQuickReplies;

/**
 * 团队快捷回复业务对象 im_quick_replies
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImQuickReplies.class, reverseConvertGenerate = false)
public class ImQuickRepliesBo extends BaseEntity {

    /**
     * 快捷回复分组id
     */
    private Long quickRepliesId;

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道id
     */
    @NotNull(message = "商户渠道id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merchantChannelId;

    /**
     * 商户渠道id
     */
    @NotNull(message = "快捷回复分组不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long quickRepliesGroupId;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 0->个人;1->团队
     */
    private String type;


}
