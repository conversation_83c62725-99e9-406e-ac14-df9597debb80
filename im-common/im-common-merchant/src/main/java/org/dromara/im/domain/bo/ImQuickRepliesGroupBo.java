package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImQuickRepliesGroup;
import org.dromara.im.domain.vo.ImQuickRepliesVo;

import java.util.List;

/**
 * 团队快捷回复分组业务对象 im_quick_replies_group
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImQuickRepliesGroup.class, reverseConvertGenerate = false)
public class ImQuickRepliesGroupBo extends BaseEntity {

    /**
     * 快捷回复分组id
     */
    private Long quickRepliesGroupId;

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 商户ID
     */
    private Long merchantId;


    /**
     * 快捷回复分组名称
     */
    private String quickRepliesGroupName;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 0->个人;1->团队
     */
    private String type;


}
