package org.dromara.im.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImQueueService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 排队处理定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QueueProcessTask {

    private final IImQueueService queueService;
    private final IImConversationService conversationService;

    /**
     * 定时处理排队队列
     * 每30秒执行一次
     */
    @Scheduled(fixedRate = 30000)
    public void processWaitingQueues() {
        try {
            log.info("开始定时处理排队队列");

            // 获取所有有排队访客的商户ID
            List<Long> merchantIds = conversationService.getMerchantsWithWaitingConversations();

            if (merchantIds.isEmpty()) {
                log.info("没有商户有排队访客");
                return;
            }

            int totalProcessed = 0;

            // 逐个处理每个商户的排队
            for (Long merchantId : merchantIds) {
                try {
                    int processed = queueService.processWaitingQueue(merchantId);
                    totalProcessed += processed;

                    if (processed > 0) {
                        log.info("处理商户排队成功 - merchantId: {}, 分配数量: {}", merchantId, processed);
                    }
                } catch (Exception e) {
                    log.error("处理商户排队失败 - merchantId: {}", merchantId, e);
                }
            }

            if (totalProcessed > 0) {
                log.info("定时处理排队完成 - 商户数量: {}, 总分配数量: {}", merchantIds.size(), totalProcessed);
            } else {
                log.info("定时处理排队完成 - 商户数量: {}, 无可分配访客", merchantIds.size());
            }

        } catch (Exception e) {
            log.error("定时处理排队队列失败", e);
        }
    }
}
