package org.dromara.im.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class VisitorListVO {

    /**
     * 访客id
     */
    private String visitorId;
    /**
     * 访客名称
     */
    private String visitorName;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 会话id
     */
    private Long conversationId;

    /**
     *
     */
    private String lastMessageContent;
    private LocalDateTime lastMessageTime;
    private Integer unreadCount;
    private String status;
}

