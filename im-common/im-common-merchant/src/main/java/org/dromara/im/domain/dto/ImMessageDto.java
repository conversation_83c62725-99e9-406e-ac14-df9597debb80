package org.dromara.im.domain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * IM消息统一格式DTO
 * 参考微信消息格式设计，支持各种消息类型
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImMessageDto implements Serializable {

    /**
     * 消息唯一标识
     */
    private String messageId;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 发送者类型: visitor=游客, service=客服, system=系统
     */
    private String senderType;

    /**
     * 发送者ID
     */
    private String senderId;

    /**
     * 发送者姓名
     */
    private String senderName;

    /**
     * 发送者头像
     */
    private String senderAvatar;

    /**
     * 接收者类型: visitor=游客, service=客服
     */
    private String receiverType;

    /**
     * 接收者ID
     */
    private String receiverId;

    /**
     * 接受者名称
     */
    private String receiverName;

    /**
     * 接收者头像
     */
    private String receiverAvatar;

    /**
     * 消息内容（文本消息直接存储文本内容）
     */
    private String content;

    /**
     * 消息扩展内容（JSON格式，用于复杂消息类型）
     */
    private Map<String, Object> contentJson;

    /**
     * 文件URL（图片、文件、语音等）
     */
    private String fileUrl;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 音视频时长（秒）
     */
    private Long duration;

    /**
     * 消息状态: sent=已发送, delivered=已送达, read=已读, failed=发送失败
     */
    private String status;

    /**
     * 是否撤回: 0=否, 1=是
     */
    private Boolean isRecalled;

    /**
     * 撤回时间
     */
    private LocalDateTime recallTime;

    /**
     * 读取时间
     */
    private LocalDateTime readTime;

    /**
     * 客户端消息ID（用于去重）
     */
    private String clientMsgId;

    /**
     * 回复的消息ID
     */
    private String replyToMsgId;

    /**
     * 转发来源消息ID
     */
    private String forwardFromMsgId;

    /**
     * 额外数据（JSON格式）
     */
    private Map<String, Object> extraData;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 业务ID
     */
    private Long merchantId;

    /**
     * 消息方向（to_service/to_visitor）
     */
    private String direction;

    // ========== 特定消息类型的扩展字段 ==========

    /**
     * 图片消息 - 图片宽度
     */
    private String imageWidth;

    /**
     * 图片消息 - 图片高度
     */
    private String imageHeight;

    /**
     * 位置消息 - 纬度
     */
    private Double latitude;

    /**
     * 位置消息 - 经度
     */
    private Double longitude;

    /**
     * 位置消息 - 地址
     */
    private String address;

    /**
     * 位置消息 - 位置名称
     */
    private String locationName;

    /**
     * 语音消息 - 语音时长（秒）
     */
    private Integer voiceDuration;

    /**
     * 视频消息 - 视频时长（秒）
     */
    private Integer videoDuration;

    /**
     * 视频消息 - 视频封面URL
     */
    private String videoCoverUrl;

    /**
     * 商品消息 - 商品ID
     */
    private String productId;

    /**
     * 商品消息 - 商品标题
     */
    private String productTitle;

    /**
     * 商品消息 - 商品价格
     */
    private String productPrice;

    /**
     * 商品消息 - 商品图片
     */
    private String productImage;

    /**
     * 商品消息 - 商品链接
     */
    private String productUrl;

    /**
     * 链接消息 - 链接标题
     */
    private String linkTitle;

    /**
     * 链接消息 - 链接描述
     */
    private String linkDescription;

    /**
     * 链接消息 - 链接缩略图
     */
    private String linkThumbnail;

    /**
     * 名片消息 - 名片姓名
     */
    private String cardName;

    /**
     * 名片消息 - 名片头像
     */
    private String cardAvatar;

    /**
     * 名片消息 - 名片描述
     */
    private String cardDescription;

    /**
     * 表情消息 - 表情包ID
     */
    private String emojiId;

    /**
     * 表情消息 - 表情包名称
     */
    private String emojiName;

    /**
     * 表情消息 - 表情包URL
     */
    private String emojiUrl;

    // ========== 静态工厂方法 ==========
}
