package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImAdminVisitorRelation;

import java.io.Serial;
import java.io.Serializable;


/**
 * 客服与访客服务关系视图对象 im_admin_visitor_relation
 *
 * <AUTHOR> Li
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImAdminVisitorRelation.class)

public class ImAdminVisitorRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成员分组id
     */
    @ExcelProperty(value = "成员分组id")
    private Long adminVisitorRelationId;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 客服ID
     */
    @ExcelProperty(value = "客服ID")
    private Long adminId;

    /**
     * 访客ID
     */
    @ExcelProperty(value = "访客ID")
    private String visitorId;

    /**
     * 商户渠道ID
     */
    @ExcelProperty(value = "商户渠道ID")
    private Long merchantChannelId;

    /**
     * 商户渠道
     */
    @ExcelProperty(value = "商户渠道")
    private String merchantChannelName;

    /**
     * 是否置顶
     */
    @ExcelProperty(value = "是否置顶")
    private String isTop;

    /**
     * 服务是否结束;0->否;1->已结束
     */
    @ExcelProperty(value = "服务是否结束;0->否;1->已结束")
    private String endService;

    /**
     * 是否拉黑 (0=否, 1=是)
     */
    @ExcelProperty(value = "是否拉黑 (0=否, 1=是)")
    private String isBlocked;

    /**
     * 聊天备注
     */
    @ExcelProperty(value = "聊天备注")
    private String chatRemark;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


}
