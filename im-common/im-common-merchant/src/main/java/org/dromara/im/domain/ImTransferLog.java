package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 会话转接记录对象 im_transfer_log
 *
 * <AUTHOR> Li
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_transfer_log")
public class ImTransferLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 原客服ID
     */
    private Long fromServiceId;

    /**
     * 目标客服ID
     */
    private Long toServiceId;

    /**
     * 转接类型: manual=手动转接, auto=自动转接, timeout=超时转接
     */
    private String transferType;

    /**
     * 转接原因
     */
    private String transferReason;

    /**
     * 操作人ID
     */
    private Long transferBy;

    /**
     * 转接状态: pending=待接收, accepted=已接收, rejected=已拒绝
     */
    private String status;

    /**
     * 处理时间
     */
    private Date processedTime;

    /**
     * 租户编号
     */
    private String tenantId;


}
