package org.dromara.im.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 渠道对话规则配置DTO
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
public class ChannelDialogRulesDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自动结束对话开关 0=关闭 1=开启
     */
    private String autoEndDialog;

    /**
     * 自动结束对话时间（分钟）
     */
    private Integer autoEndDialogTime;

    /**
     * 顾客离线自动结束对话开关 0=关闭 1=开启
     */
    private String customerOfflineAutoEnd;

    /**
     * 顾客离线超时时间（秒）
     */
    private Integer customerOfflineTimeout;

    /**
     * 对话连接自动转接开关 0=关闭 1=开启
     */
    private String dialogAutoTransfer;

    /**
     * 首条消息超时时间（秒）
     */
    private Integer firstMessageTimeout;

    /**
     * 最多转接次数
     */
    private Integer maxTransferTimes;

    /**
     * 客服离线自动转接开关 0=关闭 1=开启
     */
    private String serviceOfflineAutoTransfer;

    /**
     * 顾客发送消息间隔没有在线自动转接（秒）
     */
    private Integer customerMessageInterval;

    /**
     * 消息撤回开关 0=关闭 1=开启
     */
    private String messageRecall;


    public static ChannelDialogRulesDto getDefault() {
        ChannelDialogRulesDto dialogRulesDto = new ChannelDialogRulesDto();
        // 自动开启结束对话
        dialogRulesDto.setAutoEndDialog("1");
        // 自动结束对话时间（分钟）默认60分钟
        dialogRulesDto.setAutoEndDialogTime(60);
        // 顾客离线自动结束对话开关 0=关闭 1=开启
        dialogRulesDto.setCustomerOfflineAutoEnd("1");
        // 顾客离线时间
        dialogRulesDto.setCustomerOfflineTimeout(60);
        // 对话连接自动转接开关 0=关闭 1=开启
        dialogRulesDto.setDialogAutoTransfer("1");
        // 第一条消息超时时间 秒
        dialogRulesDto.setFirstMessageTimeout(60);
        // 最多转接次数
        dialogRulesDto.setMaxTransferTimes(1);
        // 客服离线自动转接开关 0=关闭 1=开启
        dialogRulesDto.setServiceOfflineAutoTransfer("1");
        // 顾客发送消息间隔没有在线自动转接 秒
        dialogRulesDto.setCustomerMessageInterval(90);
        // 消息撤回开关 0=关闭 1=开启
        dialogRulesDto.setMessageRecall("0");
        return dialogRulesDto;
    }

}
