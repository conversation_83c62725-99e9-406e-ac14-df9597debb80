package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImMerchantChannelRelation;

/**
 * 渠道用户关系业务对象 im_merchant_channel_relation
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImMerchantChannelRelation.class, reverseConvertGenerate = false)
public class ImMerchantChannelRelationBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long merchantChannelRelationId;

    /**
     * 用户id
     */
    private Long adminId;

    /**
     * 商户渠道id
     */
    private Long merchantChannelId;

    /**
     * 接入状态;0->待接入;1->已接入
     */
    @NotBlank(message = "接入状态;0->待接入;1->已接入不能为空", groups = {AddGroup.class, EditGroup.class})
    private String accessStatus;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantId;


}
