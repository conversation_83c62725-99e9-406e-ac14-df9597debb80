package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImTranslationPackageBo;
import org.dromara.im.domain.vo.ImTranslationPackageVo;

import java.util.Collection;
import java.util.List;

/**
 * 翻译套餐Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
public interface IImTranslationPackageService {

    /**
     * 查询翻译套餐
     *
     * @param translationPackageId 主键
     * @return 翻译套餐
     */
    ImTranslationPackageVo queryById(Long translationPackageId);

    /**
     * 分页查询翻译套餐列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 翻译套餐分页列表
     */
    TableDataInfo<ImTranslationPackageVo> queryPageList(ImTranslationPackageBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的翻译套餐列表
     *
     * @param bo 查询条件
     * @return 翻译套餐列表
     */
    List<ImTranslationPackageVo> queryList(ImTranslationPackageBo bo);

    /**
     * 新增翻译套餐
     *
     * @param bo 翻译套餐
     * @return 是否新增成功
     */
    Boolean insertByBo(ImTranslationPackageBo bo);

    /**
     * 修改翻译套餐
     *
     * @param bo 翻译套餐
     * @return 是否修改成功
     */
    Boolean updateByBo(ImTranslationPackageBo bo);

    /**
     * 校验并批量删除翻译套餐信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
