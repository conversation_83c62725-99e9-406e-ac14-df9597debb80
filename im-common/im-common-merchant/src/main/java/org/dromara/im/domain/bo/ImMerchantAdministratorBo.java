package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.utils.Password;

import java.util.Date;

/**
 * 商户管理员业务对象 im_merchant_administrator
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImMerchantAdministrator.class, reverseConvertGenerate = false)
public class ImMerchantAdministratorBo extends BaseEntity {

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 管理员编码
     */
    private String adminCode;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 昵称
     */
    @NotBlank(message = "昵称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String nickname;

    /**
     * 角色类型;1->主管理员;2->子管理员;3->客服
     */
    private String roleType;

    /**
     * 状态 1=正常 2=停用
     */
    private String status;

    /**
     * 最后登录时间
     */
    @NotNull(message = "最后登录时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date lastLoginTime;

    /**
     * 成员分组ID
     */
    private Long memberGroupId;

    /**
     * 商户渠道id
     */
    private Long merchantChannelId;
    ;

    /**
     * 头像
     */
    @NotBlank(message = "头像不能为空", groups = {AddGroup.class, EditGroup.class})
    private String avatar;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 消息提示设置
     */
    private String messagePrompt;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantId;

    /**
     * 顺序分配
     */
    private Integer sequenceSort;

    /**
     * 优先分配
     */
    private Integer prioritySort;

    /**
     * 排序规则 2->顺序排序;3->优先排序
     */
    private Integer sortParam;

    /**
     * 排序
     */
    private Integer sort;



    /**
     * 创建新管理员（指定角色）
     */
    public static ImMerchantAdministratorBo create(String email, String nickname,
                                                   Password plainPassword, Long merchantId,
                                                   String roleType, Long memberGroupId) {
        ImMerchantAdministratorBo admin = new ImMerchantAdministratorBo();
        admin.email = email;
        admin.adminCode = "ADMIN_" + System.currentTimeMillis();
        admin.nickname = nickname;
        admin.password = plainPassword.getValue();
        admin.merchantId = merchantId;
        admin.roleType = roleType;
        admin.status = "1";
        admin.memberGroupId = memberGroupId;
        admin.avatar = "/ufImgServer/neweggtest-mengjiala/20250723/fadd94704a3e4288aaf932dfbd15ecff.png";
        return admin;
    }


}
