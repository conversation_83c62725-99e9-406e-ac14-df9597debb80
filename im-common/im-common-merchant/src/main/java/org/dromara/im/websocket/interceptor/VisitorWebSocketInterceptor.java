package org.dromara.im.websocket.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.VisitorLoginUser;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

import static org.dromara.common.websocket.constant.WebSocketConstants.LOGIN_USER_KEY;

/**
 * 访客WebSocket连接拦截器
 * 用于第三方网站访客连接客服系统，无需登录态
 * 支持通过指纹识别游客身份
 * 自动解析客户端真实IP地址
 */
@Slf4j
public class VisitorWebSocketInterceptor implements HandshakeInterceptor {

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {
        try {
            // 检查是否登录 是否有token
            VisitorLoginUser loginUser = LoginHelper.getLoginUser();
            attributes.put(LOGIN_USER_KEY, loginUser);
            return true;
        } catch (Exception e) {
            log.error("访客WebSocket连接验证失败", e);
            return false;
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            log.error("访客WebSocket握手后处理失败", exception);
        }
    }
}
