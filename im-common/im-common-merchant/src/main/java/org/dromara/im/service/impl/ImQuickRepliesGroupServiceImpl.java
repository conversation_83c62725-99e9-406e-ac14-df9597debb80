package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImQuickRepliesGroup;
import org.dromara.im.domain.bo.ImQuickRepliesGroupBo;
import org.dromara.im.domain.vo.ImQuickRepliesGroupVo;
import org.dromara.im.mapper.ImQuickRepliesGroupMapper;
import org.dromara.im.service.IImQuickRepliesGroupService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 团队快捷回复分组Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImQuickRepliesGroupServiceImpl implements IImQuickRepliesGroupService {

    private final ImQuickRepliesGroupMapper baseMapper;

    /**
     * 查询团队快捷回复分组
     *
     * @param quickRepliesGroupId 主键
     * @return 团队快捷回复分组
     */
    @Override
    public ImQuickRepliesGroupVo queryById(Long quickRepliesGroupId) {
        return baseMapper.selectVoById(quickRepliesGroupId);
    }

    @Override
    public List<ImQuickRepliesGroupVo> queryByIds(List<Long> quickRepliesGroupId) {
        return baseMapper.selectVoByIds(quickRepliesGroupId);
    }

    /**
     * 分页查询团队快捷回复分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 团队快捷回复分组分页列表
     */
    @Override
    public TableDataInfo<ImQuickRepliesGroupVo> queryPageList(ImQuickRepliesGroupBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImQuickRepliesGroup> lqw = buildQueryWrapper(bo);
        Page<ImQuickRepliesGroupVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的团队快捷回复分组列表
     *
     * @param bo 查询条件
     * @return 团队快捷回复分组列表
     */
    @Override
    public List<ImQuickRepliesGroupVo> queryList(ImQuickRepliesGroupBo bo) {
        LambdaQueryWrapper<ImQuickRepliesGroup> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImQuickRepliesGroup> buildQueryWrapper(ImQuickRepliesGroupBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImQuickRepliesGroup> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImQuickRepliesGroup::getQuickRepliesGroupId);
        lqw.eq(Objects.nonNull(bo.getMerchantId()), ImQuickRepliesGroup::getMerchantId, bo.getMerchantId());
        lqw.eq(Objects.nonNull(bo.getAdminId()), ImQuickRepliesGroup::getAdminId, bo.getAdminId());
        lqw.eq(StringUtils.isNotBlank(bo.getQuickRepliesGroupName()), ImQuickRepliesGroup::getQuickRepliesGroupName, bo.getQuickRepliesGroupName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ImQuickRepliesGroup::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增团队快捷回复分组
     *
     * @param bo 团队快捷回复分组
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImQuickRepliesGroupBo bo) {
        ImQuickRepliesGroup add = MapstructUtils.convert(bo, ImQuickRepliesGroup.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setQuickRepliesGroupId(add.getQuickRepliesGroupId());
        }
        return flag;
    }

    /**
     * 修改团队快捷回复分组
     *
     * @param bo 团队快捷回复分组
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImQuickRepliesGroupBo bo) {
        ImQuickRepliesGroup update = MapstructUtils.convert(bo, ImQuickRepliesGroup.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImQuickRepliesGroup entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除团队快捷回复分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
