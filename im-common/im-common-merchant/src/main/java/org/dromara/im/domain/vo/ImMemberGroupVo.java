package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImMemberGroup;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 成员分组视图对象 im_member_group
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMemberGroup.class)
public class ImMemberGroupVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成员分组id
     */
    @ExcelProperty(value = "成员分组id")
    private Long memberGroupId;

    private Long merchantId;

    /**
     * 分组名称
     */
    @ExcelProperty(value = "分组名称")
    private String groupName;

    /**
     * webhook;1->tg;2->Lark;3->自定义
     */
    @ExcelProperty(value = "webhook")
    private String webhook;

    /**
     * webhookUrl
     */
    @ExcelProperty(value = "webhookUrl")
    private String webhookUrl;

    private Date createTime;

    private Date updateTime;


}
