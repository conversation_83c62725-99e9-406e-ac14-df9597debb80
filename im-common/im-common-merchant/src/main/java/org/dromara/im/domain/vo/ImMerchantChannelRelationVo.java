package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImMerchantChannelRelation;

import java.io.Serial;
import java.io.Serializable;


/**
 * 渠道用户关系视图对象 im_merchant_channel_relation
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMerchantChannelRelation.class)
public class ImMerchantChannelRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long merchantChannelRelationId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long adminId;

    /**
     * 渠道id
     */
    @ExcelProperty(value = "商户渠道ID")
    private Long merchantChannelId;

    /**
     * 接入状态;0->待接入;1->已接入
     */
    @ExcelProperty(value = "接入状态;0->待接入;1->已接入")
    private String accessStatus;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


}
