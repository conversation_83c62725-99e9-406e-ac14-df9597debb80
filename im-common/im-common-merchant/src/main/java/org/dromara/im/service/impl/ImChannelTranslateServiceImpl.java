package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImChannelTranslate;
import org.dromara.im.domain.bo.ImChannelTranslateBo;
import org.dromara.im.domain.vo.ImChannelTranslateVo;
import org.dromara.im.mapper.ImChannelTranslateMapper;
import org.dromara.im.service.IImChannelTranslateService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 渠道翻译设置Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImChannelTranslateServiceImpl implements IImChannelTranslateService {

    private final ImChannelTranslateMapper baseMapper;

    /**
     * 查询渠道翻译设置
     *
     * @param channelTranslateId 主键
     * @return 渠道翻译设置
     */
    @Override
    public ImChannelTranslateVo queryById(Long channelTranslateId) {
        return baseMapper.selectVoById(channelTranslateId);
    }

    /**
     * 分页查询渠道翻译设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道翻译设置分页列表
     */
    @Override
    public TableDataInfo<ImChannelTranslateVo> queryPageList(ImChannelTranslateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImChannelTranslate> lqw = buildQueryWrapper(bo);
        Page<ImChannelTranslateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的渠道翻译设置列表
     *
     * @param bo 查询条件
     * @return 渠道翻译设置列表
     */
    @Override
    public List<ImChannelTranslateVo> queryList(ImChannelTranslateBo bo) {
        LambdaQueryWrapper<ImChannelTranslate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImChannelTranslate> buildQueryWrapper(ImChannelTranslateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImChannelTranslate> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImChannelTranslate::getChannelTranslateId);
        lqw.eq(bo.getMerchantId() != null, ImChannelTranslate::getMerchantId, bo.getMerchantId());
        lqw.eq(bo.getMerchantChannelId() != null, ImChannelTranslate::getMerchantChannelId, bo.getMerchantChannelId());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenTranslate()), ImChannelTranslate::getOpenTranslate, bo.getOpenTranslate());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerLanguage()), ImChannelTranslate::getCustomerLanguage, bo.getCustomerLanguage());
        lqw.eq(StringUtils.isNotBlank(bo.getUserLanguage()), ImChannelTranslate::getUserLanguage, bo.getUserLanguage());
        return lqw;
    }

    /**
     * 新增渠道翻译设置
     *
     * @param bo 渠道翻译设置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImChannelTranslateBo bo) {
        ImChannelTranslate add = MapstructUtils.convert(bo, ImChannelTranslate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setChannelTranslateId(add.getChannelTranslateId());
        }
        return flag;
    }

    /**
     * 修改渠道翻译设置
     *
     * @param bo 渠道翻译设置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImChannelTranslateBo bo) {
        ImChannelTranslate update = MapstructUtils.convert(bo, ImChannelTranslate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImChannelTranslate entity) {
        //TODO 做一些数据校验,如唯一约束
        // 同一个渠道只能设置一个翻译
        Long l = baseMapper.selectCount(new LambdaQueryWrapper<>(ImChannelTranslate.class)
            .ne(Objects.nonNull(entity.getChannelTranslateId()), ImChannelTranslate::getChannelTranslateId, entity.getChannelTranslateId())
            .eq(ImChannelTranslate::getMerchantChannelId, entity.getMerchantChannelId())
        );
        if (l > 0) {
            throw new RuntimeException("当前渠道已经设置过翻译了");
        }
    }

    /**
     * 校验并批量删除渠道翻译设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public int batchInsertByBo(List<ImChannelTranslateBo> bo, Long merchantId) {
        List<ImChannelTranslateVo> imChannelTranslateVos = baseMapper.selectVoList(new LambdaQueryWrapper<>(new ImChannelTranslate())
            .eq(ImChannelTranslate::getMerchantId, merchantId));
        if (CollectionUtils.isNotEmpty(imChannelTranslateVos)) {
            List<Long> list = bo.stream().map(ImChannelTranslateBo::getChannelTranslateId).toList();
            List<ImChannelTranslateVo> delIds = imChannelTranslateVos.stream()
                .filter(var -> !list.contains(var.getChannelTranslateId()))
                .toList();
            baseMapper.deleteByIds(delIds);
        }
        List<ImChannelTranslateBo> updateList = bo.stream()
            .filter(var -> Objects.nonNull(var.getChannelTranslateId()))
            .toList();

        List<ImChannelTranslateBo> saveList = bo.stream()
            .filter(var -> Objects.isNull(var.getChannelTranslateId()))
            .toList();
        boolean flag = true;
        if (CollectionUtils.isNotEmpty(updateList)) {
            boolean b = baseMapper.updateBatchById(MapstructUtils.convert(updateList, ImChannelTranslate.class));
            flag &= b;
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            saveList.forEach(var -> var.setMerchantId(merchantId));
            boolean b = baseMapper.insertBatch(MapstructUtils.convert(saveList, ImChannelTranslate.class));
            flag &= b;
        }
        return flag ? 1 : 0;
    }
}
