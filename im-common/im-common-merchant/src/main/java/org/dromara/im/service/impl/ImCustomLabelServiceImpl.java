package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImCustomLabel;
import org.dromara.im.domain.bo.ImCustomLabelBo;
import org.dromara.im.domain.vo.ImCustomLabelVo;
import org.dromara.im.mapper.ImCustomLabelMapper;
import org.dromara.im.service.IImCustomLabelService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户标签Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-12
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImCustomLabelServiceImpl implements IImCustomLabelService {

    private final ImCustomLabelMapper baseMapper;

    /**
     * 查询用户标签
     *
     * @param customLabelId 主键
     * @return 用户标签
     */
    @Override
    public ImCustomLabelVo queryById(Long customLabelId) {
        return baseMapper.selectVoById(customLabelId);
    }


    @Override
    public List<ImCustomLabelVo> queryByIds(List<Long> customLabelId) {
        return baseMapper.selectVoByIds(customLabelId);
    }

    /**
     * 分页查询用户标签列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户标签分页列表
     */
    @Override
    public TableDataInfo<ImCustomLabelVo> queryPageList(ImCustomLabelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImCustomLabel> lqw = buildQueryWrapper(bo);
        Page<ImCustomLabelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户标签列表
     *
     * @param bo 查询条件
     * @return 用户标签列表
     */
    @Override
    public List<ImCustomLabelVo> queryList(ImCustomLabelBo bo) {
        LambdaQueryWrapper<ImCustomLabel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImCustomLabel> buildQueryWrapper(ImCustomLabelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImCustomLabel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImCustomLabel::getCustomLabelId);
        lqw.like(StringUtils.isNotBlank(bo.getCustomLabelName()), ImCustomLabel::getCustomLabelName, bo.getCustomLabelName());
        lqw.eq(bo.getMerchantChannelId() != null, ImCustomLabel::getMerchantChannelId, bo.getMerchantChannelId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImCustomLabel::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增用户标签
     *
     * @param bo 用户标签
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImCustomLabelBo bo) {
        ImCustomLabel add = MapstructUtils.convert(bo, ImCustomLabel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCustomLabelId(add.getCustomLabelId());
        }
        return flag;
    }

    /**
     * 修改用户标签
     *
     * @param bo 用户标签
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImCustomLabelBo bo) {
        ImCustomLabel update = MapstructUtils.convert(bo, ImCustomLabel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImCustomLabel entity) {
        //TODO 做一些数据校验,如唯一约束
        Long l = baseMapper.selectCount(new LambdaQueryWrapper<>(ImCustomLabel.class)
            .ne(Objects.nonNull(entity.getCustomLabelId()), ImCustomLabel::getCustomLabelId, entity.getCustomLabelId())
            .eq(ImCustomLabel::getCustomLabelName, entity.getCustomLabelName())
            .eq(ImCustomLabel::getMerchantChannelId, entity.getMerchantChannelId())
            .eq(ImCustomLabel::getMerchantId, entity.getMerchantId())
        );
        if (l > 0) {
            throw new RuntimeException("当前渠道已经存在该标签了");
        }

    }

    /**
     * 校验并批量删除用户标签信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
