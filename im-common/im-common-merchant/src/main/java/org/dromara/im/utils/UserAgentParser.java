package org.dromara.im.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * User-Agent解析工具类
 * 提供准确的浏览器和操作系统识别
 *
 * <AUTHOR>
 */
@Slf4j
public class UserAgentParser {

    /**
     * 操作系统识别规则
     */
    private static final Pattern[] OS_PATTERNS = {
        // iOS (必须在Mac OS之前检查)
        Pattern.compile("(iPhone|iPad|iPod).*?OS ([\\d_]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(iPhone|iPad|iPod)", Pattern.CASE_INSENSITIVE),

        // Android
        Pattern.compile("Android ([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Android", Pattern.CASE_INSENSITIVE),

        // Windows
        Pattern.compile("Windows NT ([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Windows", Pattern.CASE_INSENSITIVE),

        // Mac OS (必须在iOS之后检查)
        Pattern.compile("Mac OS X ([\\d_]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(Mac|Macintosh)", Pattern.CASE_INSENSITIVE),

        // Linux
        Pattern.compile("Linux", Pattern.CASE_INSENSITIVE),

        // 其他
        Pattern.compile("(Unix|FreeBSD|OpenBSD|NetBSD)", Pattern.CASE_INSENSITIVE)
    };

    /**
     * 浏览器识别规则（按优先级排序，更具体的在前面）
     */
    private static final Pattern[] BROWSER_PATTERNS = {
        // Edge (新版)
        Pattern.compile("Edg/([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Edge/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // Chrome (必须在Safari之前检查)
        Pattern.compile("Chrome/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // Safari (必须在Chrome之后检查)
        Pattern.compile("Version/([\\d.]+).*Safari", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Safari/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // Firefox
        Pattern.compile("Firefox/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // Opera
        Pattern.compile("OPR/([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Opera/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // IE
        Pattern.compile("MSIE ([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Trident.*rv:([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // 微信浏览器
        Pattern.compile("MicroMessenger/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // QQ浏览器
        Pattern.compile("QQBrowser/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // UC浏览器
        Pattern.compile("UCBrowser/([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // 百度浏览器
        Pattern.compile("BIDUBrowser[/\\s]([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // 搜狗浏览器
        Pattern.compile("SE ([\\d.]+)", Pattern.CASE_INSENSITIVE),

        // 360浏览器
        Pattern.compile("360[SE]*E", Pattern.CASE_INSENSITIVE),

        // 其他浏览器
        Pattern.compile("([A-Za-z]+)/([\\d.]+)", Pattern.CASE_INSENSITIVE)
    };

    /**
     * 浏览器引擎识别规则
     */
    private static final Pattern[] ENGINE_PATTERNS = {
        Pattern.compile("WebKit/([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Gecko/([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Trident/([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Presto/([\\d.]+)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("Blink", Pattern.CASE_INSENSITIVE)
    };

    /**
     * 解析User-Agent字符串
     *
     * @param userAgent User-Agent字符串
     * @return 解析结果包含浏览器、操作系统、引擎等信息
     */
    public static Map<String, Object> parse(String userAgent) {
        Map<String, Object> result = new HashMap<>();

        if (StrUtil.isBlank(userAgent)) {
            return getDefaultResult();
        }

        try {
            // 解析操作系统
            Map<String, String> osInfo = parseOperatingSystem(userAgent);
            result.put("os", osInfo.get("name"));
            result.put("osVersion", osInfo.get("version"));

            // 解析浏览器
            Map<String, String> browserInfo = parseBrowser(userAgent);
            result.put("browserName", browserInfo.get("name"));
            result.put("browserVersion", browserInfo.get("version"));

            // 解析引擎
            Map<String, String> engineInfo = parseEngine(userAgent);
            result.put("engine", engineInfo.get("name"));
            result.put("engineVersion", engineInfo.get("version"));

            // 判断设备类型
            result.put("deviceType", getDeviceType(userAgent));

            // 判断是否移动设备
            result.put("isMobile", isMobile(userAgent));

            // 原始User-Agent
            result.put("userAgent", userAgent);

            log.info("User-Agent解析结果: {}", result);

        } catch (Exception e) {
            log.error("User-Agent解析失败: {}", userAgent, e);
            return getDefaultResult();
        }

        return result;
    }

    /**
     * 解析操作系统信息
     */
    private static Map<String, String> parseOperatingSystem(String userAgent) {
        Map<String, String> result = new HashMap<>();

        // iOS设备检测（优先级最高）
        if (userAgent.contains("iPhone")) {
            Matcher matcher = Pattern.compile("OS ([\\d_]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "iOS");
                result.put("version", matcher.group(1).replace("_", "."));
            } else {
                result.put("name", "iOS");
                result.put("version", "Unknown");
            }
            return result;
        }

        if (userAgent.contains("iPad")) {
            Matcher matcher = Pattern.compile("OS ([\\d_]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "iPadOS");
                result.put("version", matcher.group(1).replace("_", "."));
            } else {
                result.put("name", "iPadOS");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Android检测
        if (userAgent.contains("Android")) {
            Matcher matcher = Pattern.compile("Android ([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Android");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Android");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Windows检测
        if (userAgent.contains("Windows")) {
            Matcher matcher = Pattern.compile("Windows NT ([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                String version = matcher.group(1);
                result.put("name", "Windows");
                result.put("version", getWindowsVersion(version));
            } else {
                result.put("name", "Windows");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Mac OS检测（在iOS检测之后）
        if (userAgent.contains("Mac OS X") || userAgent.contains("Macintosh")) {
            Matcher matcher = Pattern.compile("Mac OS X ([\\d_]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "macOS");
                result.put("version", matcher.group(1).replace("_", "."));
            } else {
                result.put("name", "macOS");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Linux检测
        if (userAgent.contains("Linux")) {
            result.put("name", "Linux");
            result.put("version", "Unknown");
            return result;
        }

        // 默认值
        result.put("name", "Unknown");
        result.put("version", "Unknown");
        return result;
    }

    /**
     * 解析浏览器信息
     */
    private static Map<String, String> parseBrowser(String userAgent) {
        Map<String, String> result = new HashMap<>();

        // 微信浏览器检测（优先级最高）
        if (userAgent.contains("MicroMessenger")) {
            Matcher matcher = Pattern.compile("MicroMessenger/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "微信浏览器");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "微信浏览器");
                result.put("version", "Unknown");
            }
            return result;
        }

        // QQ浏览器
        if (userAgent.contains("QQBrowser")) {
            Matcher matcher = Pattern.compile("QQBrowser/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "QQ浏览器");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "QQ浏览器");
                result.put("version", "Unknown");
            }
            return result;
        }

        // UC浏览器
        if (userAgent.contains("UCBrowser")) {
            Matcher matcher = Pattern.compile("UCBrowser/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "UC浏览器");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "UC浏览器");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Edge浏览器（新版本）
        if (userAgent.contains("Edg/")) {
            Matcher matcher = Pattern.compile("Edg/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Microsoft Edge");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Microsoft Edge");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Edge浏览器（旧版本）
        if (userAgent.contains("Edge/")) {
            Matcher matcher = Pattern.compile("Edge/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Microsoft Edge");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Microsoft Edge");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Safari检测（必须在Chrome之前，但排除Chrome）
        if (userAgent.contains("Safari") && !userAgent.contains("Chrome") && !userAgent.contains("Chromium")) {
            Matcher matcher = Pattern.compile("Version/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Safari");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Safari");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Chrome检测（在Safari之后）
        if (userAgent.contains("Chrome") && !userAgent.contains("Edg")) {
            Matcher matcher = Pattern.compile("Chrome/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Chrome");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Chrome");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Firefox检测
        if (userAgent.contains("Firefox")) {
            Matcher matcher = Pattern.compile("Firefox/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Firefox");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Firefox");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Opera检测
        if (userAgent.contains("OPR/")) {
            Matcher matcher = Pattern.compile("OPR/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Opera");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Opera");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Internet Explorer检测
        if (userAgent.contains("MSIE")) {
            Matcher matcher = Pattern.compile("MSIE ([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Internet Explorer");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Internet Explorer");
                result.put("version", "Unknown");
            }
            return result;
        }

        // Internet Explorer 11
        if (userAgent.contains("Trident") && userAgent.contains("rv:")) {
            Matcher matcher = Pattern.compile("rv:([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Internet Explorer");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Internet Explorer");
                result.put("version", "11.0");
            }
            return result;
        }

        // 默认值
        result.put("name", "Unknown");
        result.put("version", "Unknown");
        return result;
    }

    /**
     * 解析浏览器引擎信息
     */
    private static Map<String, String> parseEngine(String userAgent) {
        Map<String, String> result = new HashMap<>();

        if (userAgent.contains("WebKit")) {
            Matcher matcher = Pattern.compile("WebKit/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "WebKit");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "WebKit");
                result.put("version", "Unknown");
            }
            return result;
        }

        if (userAgent.contains("Gecko") && !userAgent.contains("WebKit")) {
            Matcher matcher = Pattern.compile("Gecko/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Gecko");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Gecko");
                result.put("version", "Unknown");
            }
            return result;
        }

        if (userAgent.contains("Trident")) {
            Matcher matcher = Pattern.compile("Trident/([\\d.]+)", Pattern.CASE_INSENSITIVE).matcher(userAgent);
            if (matcher.find()) {
                result.put("name", "Trident");
                result.put("version", matcher.group(1));
            } else {
                result.put("name", "Trident");
                result.put("version", "Unknown");
            }
            return result;
        }

        if (userAgent.contains("Blink")) {
            result.put("name", "Blink");
            result.put("version", "Unknown");
            return result;
        }

        result.put("name", "Unknown");
        result.put("version", "Unknown");
        return result;
    }

    /**
     * 获取设备类型
     */
    private static String getDeviceType(String userAgent) {
        if (userAgent.contains("Mobile") || userAgent.contains("Android")) {
            if (userAgent.contains("Tablet") || userAgent.contains("iPad")) {
                return "Tablet";
            }
            return "Mobile";
        }

        if (userAgent.contains("iPad")) {
            return "Tablet";
        }

        return "Desktop";
    }

    /**
     * 判断是否为移动设备
     */
    private static boolean isMobile(String userAgent) {
        String[] mobileKeywords = {
            "Mobile", "Android", "iPhone", "iPad", "iPod",
            "BlackBerry", "Windows Phone", "Opera Mini",
            "IEMobile", "Mobile Safari"
        };

        for (String keyword : mobileKeywords) {
            if (userAgent.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取Windows版本名称
     */
    private static String getWindowsVersion(String version) {
        switch (version) {
            case "10.0":
                return "Windows 10";
            case "6.3":
                return "Windows 8.1";
            case "6.2":
                return "Windows 8";
            case "6.1":
                return "Windows 7";
            case "6.0":
                return "Windows Vista";
            case "5.2":
                return "Windows XP x64";
            case "5.1":
                return "Windows XP";
            default:
                return "Windows " + version;
        }
    }

    /**
     * 获取默认解析结果
     */
    private static Map<String, Object> getDefaultResult() {
        Map<String, Object> result = new HashMap<>();
        result.put("os", "Unknown");
        result.put("osVersion", "Unknown");
        result.put("browserName", "Unknown");
        result.put("browserVersion", "Unknown");
        result.put("engine", "Unknown");
        result.put("engineVersion", "Unknown");
        result.put("deviceType", "Desktop");
        result.put("isMobile", false);
        result.put("userAgent", "");
        return result;
    }

    /**
     * 简化解析，返回基本信息
     */
    public static Map<String, String> parseSimple(String userAgent) {
        Map<String, Object> fullResult = parse(userAgent);
        Map<String, String> result = new HashMap<>();

        result.put("browserName", (String) fullResult.get("browserName"));
        result.put("browserVersion", (String) fullResult.get("browserVersion"));
        result.put("os", (String) fullResult.get("os"));
        result.put("engine", (String) fullResult.get("engine"));

        return result;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试iPhone Safari
        String iphoneUA = "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1";
        System.out.println("iPhone Safari解析结果:");
        System.out.println(parse(iphoneUA));

        // 测试Android Chrome
        String androidUA = "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36";
        System.out.println("\nAndroid Chrome解析结果:");
        System.out.println(parse(androidUA));

        // 测试Windows Chrome
        String windowsUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
        System.out.println("\nWindows Chrome解析结果:");
        System.out.println(parse(windowsUA));

        // 测试Mac Safari
        String macUA = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15";
        System.out.println("\nMac Safari解析结果:");
        System.out.println(parse(macUA));
    }
}
