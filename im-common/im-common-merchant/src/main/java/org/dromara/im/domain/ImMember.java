package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 成员对象 im_member
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_member")
public class ImMember extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成员id
     */
    @TableId(value = "member_id")
    private Long memberId;

    private Long adminId;

    /**
     * 成员分组id
     */
    private Long memberGroupId;

    /**
     * 成员分组名称
     */
    private String memberGroupName;

    /**
     * 客服名称
     */
    private String name;

    /**
     * 渠道id
     */
    private String channelIds;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;


}
