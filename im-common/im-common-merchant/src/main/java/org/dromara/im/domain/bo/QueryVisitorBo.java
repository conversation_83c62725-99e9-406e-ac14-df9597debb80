package org.dromara.im.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class QueryVisitorBo {

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * 访客ID
     */
    private String visitorId;

    /**
     * 访客名称
     */
    private String visitorName;

    /**
     * 标签ID
     */
    private List<Long> customLabelId;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();
}
