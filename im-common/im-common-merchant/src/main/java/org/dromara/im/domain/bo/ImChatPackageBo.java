package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImChatPackage;

import java.math.BigDecimal;

/**
 * 套餐业务对象 im_chat_package
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImChatPackage.class, reverseConvertGenerate = false)
public class ImChatPackageBo extends BaseEntity {

    /**
     * 套餐ID
     */
    private Long chatPackageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 1->标准版;2->专业版;3->集团版
     */
    private String packageType;

    /**
     * 套餐价格(USD)
     */
    @NotNull(message = "套餐价格(USD)不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal price;

    /**
     * 货币单位
     */
    @NotBlank(message = "货币单位不能为空", groups = {AddGroup.class, EditGroup.class})
    private String currency;

    @NotBlank(message = "计费周期数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer billingPeriod;

    /**
     * 计费周期(0-月付 1-季付 2-年付)
     */
    @NotBlank(message = "计费周期不能为空", groups = {AddGroup.class, EditGroup.class})
    private String billingCycle;

    /**
     * 套餐描述
     */
    @NotBlank(message = "套餐描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String description;

    /**
     * 功能配置JSON，采用驼峰命名
     */
    @NotBlank(message = "功能配置JSON，采用驼峰命名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String featureConfig;


    /**
     * 状态(1-启用 0-禁用)
     */
    @NotNull(message = "状态(1-启用 0-禁用)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long status;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sortOrder;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;


}
