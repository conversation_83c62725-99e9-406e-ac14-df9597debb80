package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImChatPackage;

import java.math.BigDecimal;

/**
 * 套餐业务对象 im_chat_package
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImChatPackage.class, reverseConvertGenerate = false)
public class ImChatPackageBo extends BaseEntity {

    /**
     * 套餐ID
     */
    private Long chatPackageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 1->标准版;2->专业版;3->集团版
     */
    private String packageType;

    /**
     * 套餐价格(USD)
     */
    private BigDecimal price;

    /**
     * 货币单位
     */
    private String currency;

    private Integer billingPeriod;

    /**
     * 计费周期(0-月付 1-季付 2-年付)
     */
    private String billingCycle;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 功能配置JSON，采用驼峰命名
     */
    private String featureConfig;


    /**
     * 状态(1-启用 0-禁用)
     */
    private Long status;

    /**
     * 排序
     */
    private Long sortOrder;

    /**
     * 备注
     */
    private String remark;


}
