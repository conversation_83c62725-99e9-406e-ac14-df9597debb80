package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 团队快捷回复对象 im_quick_replies
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_quick_replies")
public class ImQuickReplies extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 快捷回复分组id
     */
    @TableId(value = "quick_replies_id")
    private Long quickRepliesId;

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道id
     */
    private Long merchantChannelId;

    /**
     * 商户渠道id
     */
    private Long quickRepliesGroupId;

    /**
     * 内容
     */
    private String content;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 0->个人;1->团队
     */
    private String type;


}
