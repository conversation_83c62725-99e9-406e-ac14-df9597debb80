package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.im.constans.PackageTypeEnum;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.dto.PackageInfoDTO;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;


/**
 * 商户视图对象 im_merchant
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMerchant.class)
public class ImMerchantVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 商户编码
     */
    @ExcelProperty(value = "商户编码")
    private String merchantCode;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 坐席数量
     */
    private Long seatsCount;

    /**
     * 对话上限
     */
    private Long chatNumber;

    /**
     * 渠道数量
     */
    private Long channelNumber;

    /**
     * 已使用量
     */
    private Long useTranslateNumber;

    /**
     * 总量
     */
    private Long translateNumber;

    /**
     * 过期时间
     */
    private Long expiredTime;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


    private String password;

    /**
     * 套餐类型0->体验版;1->标准版;2->专业版;3->集团版
     */
    private String packageType;

    private String email;


    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 对话排队配置
     */
    private String conversationQueues;


    private PackageInfoDTO freeVersion;


    /**
     * 标准版
     */
    private PackageInfoDTO standardVersion;

    /**
     * 专业版
     */
    private PackageInfoDTO ProfessionalVersion;

    /**
     * 集团版
     */
    private PackageInfoDTO groupVersion;


    public PackageInfoDTO getExtraPackageInfo(String key) {
        if (StringUtils.isNotBlank(extra)) {
            Map<String, Object> extraMap = JsonUtils.parseMap(extra);
            if (MapUtils.isNotEmpty(extraMap) && extraMap.containsKey(key)) {
                Object object = extraMap.get(key);
                return JsonUtils.parseObject(object.toString(), PackageInfoDTO.class);
            }
        }
        return null;
    }

    /**
     * 免费版
     */
    public PackageInfoDTO buildFreeVersion() {
        return getExtraPackageInfo(PackageTypeEnum.freeVersion.name());
    }

    /**
     * 构建标准版套餐
     */
    public PackageInfoDTO buildStandardVersion() {
        return getExtraPackageInfo(PackageTypeEnum.standardVersion.name());
    }

    /**
     * 构建专业版套餐
     */
    public PackageInfoDTO buildProfessionalVersion() {
        return getExtraPackageInfo(PackageTypeEnum.professionalVersion.name());
    }

    /**
     * 构建集团套餐
     */
    public PackageInfoDTO buildGroupVersion() {
        return getExtraPackageInfo(PackageTypeEnum.groupVersion.name());
    }


    public static PackageInfoDTO getCurrentPackage(ImMerchantVo imMerchantVo) {
        return Optional.ofNullable(imMerchantVo.buildGroupVersion())
            .filter(var -> var.getExpiredTime() > System.currentTimeMillis())
            .orElseGet(() -> Optional
                .ofNullable(imMerchantVo.buildProfessionalVersion())
                .filter(var -> var.getExpiredTime() > System.currentTimeMillis())
                .orElseGet(() -> Optional
                    .ofNullable(imMerchantVo.buildStandardVersion())
                    .filter(var -> var.getExpiredTime() > System.currentTimeMillis())
                    .orElseGet(imMerchantVo::buildFreeVersion)));
    }

    public static boolean checkExpired(ImMerchantVo imMerchantVo) {
        PackageInfoDTO packageInfoDTO = Optional.ofNullable(imMerchantVo.buildGroupVersion())
            .filter(var -> var.getExpiredTime() > System.currentTimeMillis())
            .orElseGet(() -> Optional
                .ofNullable(imMerchantVo.buildProfessionalVersion())
                .filter(var -> var.getExpiredTime() > System.currentTimeMillis())
                .orElseGet(() -> Optional
                    .ofNullable(imMerchantVo.buildStandardVersion())
                    .filter(var -> var.getExpiredTime() > System.currentTimeMillis())
                    .orElseGet(imMerchantVo::buildFreeVersion)));
        return packageInfoDTO.getExpiredTime() > System.currentTimeMillis();
    }

}
