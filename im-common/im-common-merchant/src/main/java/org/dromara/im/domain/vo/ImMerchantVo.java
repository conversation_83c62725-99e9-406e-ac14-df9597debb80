package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImMerchant;

import java.io.Serial;
import java.io.Serializable;


/**
 * 商户视图对象 im_merchant
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMerchant.class)
public class ImMerchantVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 商户编码
     */
    @ExcelProperty(value = "商户编码")
    private String merchantCode;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 坐席数量
     */
    private Long seatsCount;

    /**
     * 对话上限
     */
    private Long chatNumber;

    /**
     * 渠道数量
     */
    private Long channelNumber;

    /**
     * 已使用量
     */
    private Long useTranslateNumber;

    /**
     * 总量
     */
    private Long translateNumber;

    /**
     * 过期时间
     */
    private Long expiredTime;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


    private String password;

    /**
     * 套餐类型0->体验版;1->标准版;2->专业版;3->集团版
     */
    private String packageType;

    private String email;


}
