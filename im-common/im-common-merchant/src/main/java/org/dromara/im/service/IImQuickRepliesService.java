package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImQuickRepliesBo;
import org.dromara.im.domain.vo.ImQuickRepliesVo;

import java.util.Collection;
import java.util.List;

/**
 * 团队快捷回复Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
public interface IImQuickRepliesService {

    /**
     * 查询团队快捷回复
     *
     * @param quickRepliesId 主键
     * @return 团队快捷回复
     */
    ImQuickRepliesVo queryById(Long quickRepliesId);

    /**
     * 分页查询团队快捷回复列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 团队快捷回复分页列表
     */
    TableDataInfo<ImQuickRepliesVo> queryPageList(ImQuickRepliesBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的团队快捷回复列表
     *
     * @param bo 查询条件
     * @return 团队快捷回复列表
     */
    List<ImQuickRepliesVo> queryList(ImQuickRepliesBo bo);

    List<ImQuickRepliesVo> queryList(List<Long> quickRepliesGroupIds);

    /**
     * 新增团队快捷回复
     *
     * @param bo 团队快捷回复
     * @return 是否新增成功
     */
    Boolean insertByBo(ImQuickRepliesBo bo);

    /**
     * 修改团队快捷回复
     *
     * @param bo 团队快捷回复
     * @return 是否修改成功
     */
    Boolean updateByBo(ImQuickRepliesBo bo);

    /**
     * 校验并批量删除团队快捷回复信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
