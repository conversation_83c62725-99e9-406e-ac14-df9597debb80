package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImVisitorLabel;

import java.util.List;

/**
 * 访客标签业务对象 im_visitor_label
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImVisitorLabel.class, reverseConvertGenerate = false)
public class ImVisitorLabelBo extends BaseEntity {

    /**
     * 游客标签id
     */
    private Long visitorLabelId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 标签ID
     */
    private Long customLabelId;

    /**
     * 访客ID
     */
    private String visitorId;

    private List<Long> customLabelIdList;


}
