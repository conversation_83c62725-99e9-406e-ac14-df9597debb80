package org.dromara.im.constans;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum PackageTypeEnum {
    /**
     * 体验版
     */
    freeVersion("0", "体验版"),
    /**
     * 标准版
     */
    standardVersion("1", "标准版"),
    /**
     * 专业版
     */
    professionalVersion("2", "专业版"),
    /**
     * 集团版
     */
    groupVersion("3", "集团版");
    private final String packageType;

    private final String desc;

    PackageTypeEnum(String packageType, String desc) {
        this.packageType = packageType;
        this.desc = desc;
    }

    public static String getByPackageType(String packageType) {
        for (PackageTypeEnum value : values()) {
            if (StringUtils.equals(value.getPackageType(), packageType)) {
                return value.name();
            }
        }
        throw new IllegalArgumentException("未知的套餐类型: " + packageType);
    }
}
