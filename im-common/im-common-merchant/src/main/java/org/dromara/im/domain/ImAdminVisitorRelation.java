package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 客服与访客服务关系对象 im_admin_visitor_relation
 *
 * <AUTHOR> Li
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_admin_visitor_relation")
public class ImAdminVisitorRelation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成员分组id
     */
    @TableId(value = "admin_visitor_relation_id")
    private Long adminVisitorRelationId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 客服ID
     */
    private Long adminId;

    /**
     * 访客ID
     */
    private String visitorId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;


    /**
     * 是否置顶
     */
    private String isTop;

    /**
     * 服务是否结束;0->否;1->已结束
     */
    private String endService;

    /**
     * 是否拉黑 (0=否, 1=是)
     */
    private String isBlocked;

    /**
     * 聊天备注
     */
    private String chatRemark;

    /**
     * 租户编号
     */
    private String tenantId;


}
