package org.dromara.im.utils;

import cn.hutool.core.util.IdUtil;
import cn.ucloud.ufile.UfileClient;
import cn.ucloud.ufile.api.object.ObjectConfig;
import cn.ucloud.ufile.auth.UfileObjectLocalAuthorization;
import cn.ucloud.ufile.bean.PutObjectResultBean;
import cn.ucloud.ufile.exception.UfileClientException;
import cn.ucloud.ufile.exception.UfileServerException;
import cn.ucloud.ufile.util.JLog;
import cn.ucloud.ufile.util.StorageType;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Date;


public class S3UploadUtils {

    private static final String TAG = "PutObjectSample";
    private static final ObjectConfig config = new ObjectConfig("sg", "ufileos.com");


    public static final String SLASH = "/";
    public static final String DOT = ".";

    public static final String IMAGE_PNG = "image/png";

    public static final String IMAGE_JPG = "image/jpg";

    public static final String IMAGE_JPEG = "image/jpeg";

    public static final String IMAGE_BMP = "image/bmp";

    public static final String IMAGE_GIF = "image/gif";


    public static void putStream(InputStream stream, long contentLength, String mimeType, String nameAs, String toBucket) {
        try {
            /**
             * 上传回调策略
             * 必须填写回调接口url(目前仅支持http，不支持https)，可选填回调参数，回调参数请自行决定是否需要urlencode
             * 若配置上传回调，则上传接口的回调将会透传回调接口的response，包括httpCode
             */
            PutObjectResultBean response = UfileClient.object(new UfileObjectLocalAuthorization("TOKEN_3d2852e9-874c-44f4-9310-bef968dfc036", "cb675cf5-5e8e-4743-91cb-4f781cbe7016"), config)
                .putObject(stream, contentLength, mimeType)
                .nameAs(nameAs)
                .toBucket(toBucket)
                /**
                 * 配置文件存储类型，分别是标准、低频、冷存，对应有效值：STANDARD | IA | ARCHIVE
                 */
                .withStorageType(StorageType.STANDARD)
                /**
                 * 配置进度监听
                 */
                .setOnProgressListener((bytesWritten, contentLength1) -> JLog.D(TAG, String.format("[progress] = %d%% - [%d/%d]", (int) (bytesWritten * 1.f / contentLength1 * 100), bytesWritten, contentLength1)))
                .execute();
            JLog.D(TAG, String.format("[res] = %s", (response == null ? "null" : response.toString())));
        } catch (UfileClientException | UfileServerException e) {
            e.printStackTrace();
        }
    }


    /**
     * 编码文件名
     */
    public static final String extractFilename(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        fileName = dateTime() + SLASH + IdUtil.fastSimpleUUID() + DOT + extension;
        return fileName;
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }


    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static final String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (!StringUtils.hasText(extension)) {
            extension = getExtension(file.getContentType());
        }
        return extension;
    }


    public static String getExtension(String prefix) {
        return switch (prefix) {
            case IMAGE_PNG -> "png";
            case IMAGE_JPG -> "jpg";
            case IMAGE_JPEG -> "jpeg";
            case IMAGE_BMP -> "bmp";
            case IMAGE_GIF -> "gif";
            default -> "";
        };
    }
}
