package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImChannelConfig;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 渠道配置视图对象 im_channel_config
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImChannelConfig.class)
public class ImChannelConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道配置ID
     */
    @ExcelProperty(value = "渠道配置ID")
    private Long channelConfigId;

    /**
     * 渠道类型，例如 网页+APP、Telegram Bot
     */
    @ExcelProperty(value = "渠道类型，例如 网页+APP、Telegram Bot")
    private String channelType;

    /**
     * 渠道编码，唯一标识，如 sa0KZ3
     */
    @ExcelProperty(value = "渠道编码，唯一标识，如 sa0KZ3")
    private String channelCode;

    /**
     * 渠道名称，如 bem渠道、tgben
     */
    @ExcelProperty(value = "渠道名称，如 bem渠道、tgben")
    private String channelName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updatedAt;


}
