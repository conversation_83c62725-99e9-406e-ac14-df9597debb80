package org.dromara.im.utils;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 客户端IP地址解析工具类
 * 参考Symfony HttpFoundation Request类实现
 * 支持各种代理服务器情况下的真实IP获取
 * 专门用于WebSocket场景
 *
 * <AUTHOR>
 */
@Slf4j
public class ClientIpUtils {

    /**
     * 代理头部常量
     */
    private static final String HEADER_X_FORWARDED_FOR = "X-Forwarded-For";
    private static final String HEADER_X_FORWARDED_HOST = "X-Forwarded-Host";
    private static final String HEADER_X_FORWARDED_PROTO = "X-Forwarded-Proto";
    private static final String HEADER_X_FORWARDED_PORT = "X-Forwarded-Port";
    private static final String HEADER_FORWARDED = "Forwarded";
    private static final String HEADER_X_REAL_IP = "X-Real-IP";
    private static final String HEADER_CLIENT_IP = "Client-IP";
    private static final String HEADER_PROXY_CLIENT_IP = "Proxy-Client-IP";
    private static final String HEADER_WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    private static final String HEADER_HTTP_CLIENT_IP = "HTTP_CLIENT_IP";
    private static final String HEADER_HTTP_X_FORWARDED_FOR = "HTTP_X_FORWARDED_FOR";

    /**
     * 优先级排序的头部列表
     */
    private static final String[] PROXY_HEADERS = {
        HEADER_X_REAL_IP,
        HEADER_X_FORWARDED_FOR,
        HEADER_FORWARDED,
        HEADER_PROXY_CLIENT_IP,
        HEADER_WL_PROXY_CLIENT_IP,
        HEADER_CLIENT_IP,
        HEADER_HTTP_CLIENT_IP,
        HEADER_HTTP_X_FORWARDED_FOR
    };

    /**
     * 本地IP地址模式
     */
    private static final Pattern LOCAL_IP_PATTERN = Pattern.compile(
        "^(127\\..*|10\\..*|172\\.1[6-9]\\.|172\\.2[0-9]\\.|172\\.3[0-1]\\.|192\\.168\\.|169\\.254\\.).*");

    /**
     * IPv4地址模式
     */
    private static final Pattern IPV4_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    /**
     * IPv6地址模式
     */
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$");

    /**
     * 默认可信代理IP列表
     * 可以通过配置文件或环境变量覆盖
     */
    private static final Set<String> DEFAULT_TRUSTED_PROXIES = new HashSet<>(Arrays.asList(
        "127.0.0.1",
        "::1",
        "10.0.0.0/8",
        "**********/12",
        "***********/16"
    ));

    /**
     * 获取客户端真实IP地址 - ServerHttpRequest版本（用于WebSocket）
     *
     * @param request ServerHttpRequest对象
     * @return 客户端IP地址
     */
    public static String getClientIp(ServerHttpRequest request) {
        if (request == null) {
            return "unknown";
        }

        // 获取所有可能的IP地址
        List<String> candidateIps = getClientIps(request);

        // 返回第一个有效的非内网IP，或者第一个有效IP
        return selectBestClientIp(candidateIps);
    }

    /**
     * 获取客户端真实IP地址 - HttpServletRequest版本（用于普通HTTP请求）
     *
     * @param request HttpServletRequest对象
     * @return 客户端IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        // 获取所有可能的IP地址
        List<String> candidateIps = getClientIps(request);

        // 返回第一个有效的非内网IP，或者第一个有效IP
        return selectBestClientIp(candidateIps);
    }

    /**
     * 获取所有可能的客户端IP地址 - ServerHttpRequest版本
     *
     * @param request ServerHttpRequest对象
     * @return IP地址列表，按信任度排序
     */
    public static List<String> getClientIps(ServerHttpRequest request) {
        List<String> ips = new ArrayList<>();

        // 1. 按优先级检查代理头部
        for (String header : PROXY_HEADERS) {
            List<String> headerValues = request.getHeaders().get(header);
            if (headerValues != null && !headerValues.isEmpty()) {
                for (String headerValue : headerValues) {
                    if (StrUtil.isNotBlank(headerValue)) {
                        List<String> headerIps = parseIpHeader(headerValue);
                        ips.addAll(headerIps);
                        log.info("从头部 {} 解析到IP: {}", header, headerIps);
                    }
                }
            }
        }

        // 2. 获取直连IP (从URI中获取)
        request.getRemoteAddress();
        String remoteAddr = request.getRemoteAddress().getAddress().getHostAddress();
        if (StrUtil.isNotBlank(remoteAddr)) {
            ips.add(remoteAddr);
            log.info("Remote Address: {}", remoteAddr);
        }

        // 3. 过滤和验证IP地址
        List<String> validIps = normalizeAndFilterIps(ips);

        log.info("最终解析的IP列表: {}", validIps);
        return validIps;
    }

    /**
     * 获取所有可能的客户端IP地址 - HttpServletRequest版本
     *
     * @param request HttpServletRequest对象
     * @return IP地址列表，按信任度排序
     */
    public static List<String> getClientIps(HttpServletRequest request) {
        List<String> ips = new ArrayList<>();

        // 1. 按优先级检查代理头部
        for (String header : PROXY_HEADERS) {
            String headerValue = request.getHeader(header);
            if (StrUtil.isNotBlank(headerValue)) {
                List<String> headerIps = parseIpHeader(headerValue);
                ips.addAll(headerIps);
                log.info("从头部 {} 解析到IP: {}", header, headerIps);
            }
        }

        // 2. 获取直连IP (从getRemoteAddr)
        String remoteAddr = request.getRemoteAddr();
        if (StrUtil.isNotBlank(remoteAddr)) {
            ips.add(remoteAddr);
            log.info("Remote Address: {}", remoteAddr);
        }

        // 3. 过滤和验证IP地址
        List<String> validIps = normalizeAndFilterIps(ips);

        log.info("最终解析的IP列表: {}", validIps);
        return validIps;
    }

    /**
     * 解析IP头部值
     * 支持逗号分隔的多个IP，以及 Forwarded 头部的特殊格式
     *
     * @param headerValue 头部值
     * @return IP地址列表
     */
    private static List<String> parseIpHeader(String headerValue) {
        List<String> ips = new ArrayList<>();

        if (StrUtil.isBlank(headerValue)) {
            return ips;
        }

        // 处理 Forwarded 头部的特殊格式: for=***********:8080;proto=http
        if (headerValue.contains("for=")) {
            String[] parts = headerValue.split("[;,]");
            for (String part : parts) {
                part = part.trim();
                if (part.startsWith("for=")) {
                    String forValue = part.substring(4);
                    // 移除引号和方括号
                    forValue = forValue.replaceAll("[\"\\[\\]]", "");
                    // 移除端口号
                    if (forValue.contains(":")) {
                        forValue = forValue.substring(0, forValue.lastIndexOf(":"));
                    }
                    ips.add(forValue);
                }
            }
        } else {
            // 处理普通的逗号分隔格式
            String[] parts = headerValue.split("[,;]");
            for (String part : parts) {
                part = part.trim();
                if (StrUtil.isNotBlank(part) && !part.equalsIgnoreCase("unknown")) {
                    // 移除端口号
                    if (part.contains(":") && !part.contains("::")) {
                        // IPv4地址去端口
                        part = part.substring(0, part.lastIndexOf(":"));
                    }
                    ips.add(part);
                }
            }
        }

        return ips;
    }

    /**
     * 标准化和过滤IP地址列表
     *
     * @param ips 原始IP列表
     * @return 过滤后的有效IP列表
     */
    private static List<String> normalizeAndFilterIps(List<String> ips) {
        List<String> validIps = new ArrayList<>();
        Set<String> seenIps = new HashSet<>();

        for (String ip : ips) {
            String normalizedIp = normalizeIp(ip);

            // 验证IP格式
            if (isValidIp(normalizedIp) && !seenIps.contains(normalizedIp)) {
                validIps.add(normalizedIp);
                seenIps.add(normalizedIp);
            }
        }

        return validIps;
    }

    /**
     * 标准化IP地址
     *
     * @param ip 原始IP
     * @return 标准化后的IP
     */
    private static String normalizeIp(String ip) {
        if (StrUtil.isBlank(ip)) {
            return "";
        }

        ip = ip.trim();

        // 移除常见的无效值
        if (ip.equalsIgnoreCase("unknown") ||
            ip.equalsIgnoreCase("null") ||
            ip.equals("0.0.0.0")) {
            return "";
        }

        // 移除端口号（如果存在）
        if (ip.contains(":") && !ip.contains("::")) {
            // 这是IPv4:port格式
            int lastColonIndex = ip.lastIndexOf(":");
            try {
                Integer.parseInt(ip.substring(lastColonIndex + 1));
                ip = ip.substring(0, lastColonIndex);
            } catch (NumberFormatException e) {
                // 不是port，可能是IPv6，保持原样
            }
        }

        return ip;
    }

    /**
     * 验证IP地址是否有效
     *
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        if (StrUtil.isBlank(ip)) {
            return false;
        }

        // 使用Java的InetAddress验证
        try {
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 选择最佳的客户端IP地址
     * 优先选择公网IP，其次是内网IP
     *
     * @param candidateIps 候选IP列表
     * @return 最佳IP地址
     */
    private static String selectBestClientIp(List<String> candidateIps) {
        if (candidateIps.isEmpty()) {
            return "unknown";
        }

        String firstValidIp = null;

        // 优先查找公网IP
        for (String ip : candidateIps) {
            if (StrUtil.isNotBlank(ip)) {
                if (firstValidIp == null) {
                    firstValidIp = ip;
                }

                // 如果是公网IP，优先返回
                if (!isLocalIp(ip)) {
                    log.info("选择公网IP: {}", ip);
                    return ip;
                }
            }
        }

        // 如果没有公网IP，返回第一个有效的IP
        String result = firstValidIp != null ? firstValidIp : "unknown";
        log.info("选择IP: {}", result);
        return result;
    }

    /**
     * 判断是否为本地/内网IP
     *
     * @param ip IP地址
     * @return 是否为本地IP
     */
    private static boolean isLocalIp(String ip) {
        if (StrUtil.isBlank(ip)) {
            return true;
        }

        // 检查是否为本地回环地址
        if ("127.0.0.1".equals(ip) || "::1".equals(ip) || "localhost".equals(ip)) {
            return true;
        }

        // 检查是否为RFC 1918私有地址
        return LOCAL_IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 获取地理位置信息（可扩展接入第三方IP库）
     *
     * @param ip IP地址
     * @return 地理位置信息
     */
    public static String getLocationInfo(String ip) {
        if (StrUtil.isBlank(ip) || isLocalIp(ip)) {
            return "localhost";
        }

        // TODO: 这里可以接入第三方IP地理位置服务
        // 比如：IP2Region、GeoIP2等
        return "未知地区";
    }

    /**
     * 验证IP是否在可信代理列表中
     *
     * @param ip IP地址
     * @return 是否为可信代理
     */
    public static boolean isTrustedProxy(String ip) {
        if (StrUtil.isBlank(ip)) {
            return false;
        }

        // 检查默认可信代理列表
        return DEFAULT_TRUSTED_PROXIES.contains(ip) || isLocalIp(ip);
    }

    /**
     * 获取详细的IP解析信息（用于调试）- ServerHttpRequest版本
     *
     * @param request ServerHttpRequest对象
     * @return IP解析详情
     */
    public static Map<String, Object> getIpDetails(ServerHttpRequest request) {
        Map<String, Object> details = new HashMap<>();

        // 收集所有头部信息
        Map<String, String> headers = new HashMap<>();
        for (String header : PROXY_HEADERS) {
            List<String> values = request.getHeaders().get(header);
            if (values != null && !values.isEmpty()) {
                headers.put(header, String.join(", ", values));
            }
        }

        details.put("headers", headers);
        details.put("remoteAddr", request.getRemoteAddress() != null ?
            request.getRemoteAddress().getAddress().getHostAddress() : "unknown");
        details.put("allIps", getClientIps(request));
        details.put("clientIp", getClientIp(request));
        details.put("locationInfo", getLocationInfo(getClientIp(request)));

        return details;
    }

    /**
     * 获取详细的IP解析信息（用于调试）- HttpServletRequest版本
     *
     * @param request HttpServletRequest对象
     * @return IP解析详情
     */
    public static Map<String, Object> getIpDetails(HttpServletRequest request) {
        Map<String, Object> details = new HashMap<>();

        // 收集所有头部信息
        Map<String, String> headers = new HashMap<>();
        for (String header : PROXY_HEADERS) {
            String headerValue = request.getHeader(header);
            if (StrUtil.isNotBlank(headerValue)) {
                headers.put(header, headerValue);
            }
        }

        details.put("headers", headers);
        details.put("remoteAddr", request.getRemoteAddr());
        details.put("allIps", getClientIps(request));
        details.put("clientIp", getClientIp(request));
        details.put("locationInfo", getLocationInfo(getClientIp(request)));

        return details;
    }
}
