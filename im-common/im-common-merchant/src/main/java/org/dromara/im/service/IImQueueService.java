package org.dromara.im.service;

import org.dromara.im.domain.vo.ImConversationVo;

/**
 * 排队管理服务接口
 */
public interface IImQueueService {

    /**
     * 处理排队中的访客，尝试分配客服
     *
     * @param merchantId 商户ID
     * @return 成功分配的数量
     */
    int processWaitingQueue(Long merchantId);

    /**
     * 当客服结束服务时，处理排队
     *
     * @param merchantId 商户ID
     * @param adminId    客服ID
     */
    void processQueueOnServiceEnd(Long merchantId, Long adminId);

    /**
     * 获取排队中的下一个访客
     *
     * @param merchantId 商户ID
     * @return 排队中的会话
     */
    ImConversationVo getNextWaitingConversation(Long merchantId);

    /**
     * 分配访客给指定客服
     *
     * @param conversation 会话信息
     * @param adminId      客服ID
     * @return 是否分配成功
     */
    boolean assignConversationToService(ImConversationVo conversation, Long adminId);
}
