package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImTranslationPackage;

import java.math.BigDecimal;

/**
 * 翻译套餐业务对象 im_translation_package
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImTranslationPackage.class, reverseConvertGenerate = false)
public class ImTranslationPackageBo extends BaseEntity {

    /**
     * 套餐ID
     */
    private Long translatePackageId;

    /**
     * 套餐编码
     */
    private String packageCode;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 字符配额（单位：万字符）
     */
    private Long characterQuota;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 货币类型 USDT=USDT CNY=人民币 USD=美元
     */
    private String currencyType;

    /**
     * 支付方式 USDT-TRC20=USDT-TRC20 USDT-ERC20=USDT-ERC20
     */
    @NotBlank(message = "支付方式 USDT-TRC20=USDT-TRC20 USDT-ERC20=USDT-ERC20不能为空", groups = {AddGroup.class, EditGroup.class})
    private String paymentMethod;

    /**
     * 套餐类型 0=字符包 1=无限套餐
     */
    private String packageType;

    /**
     * 排序顺序
     */
    private Long sortOrder;

    /**
     * 状态 1=正常 2=停用 3=下架
     */
    private Long status;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantId;


}
