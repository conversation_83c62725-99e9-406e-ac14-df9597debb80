package org.dromara.im.service;

/**
 * 游客客服分配服务接口
 * 负责处理游客与客服的分配逻辑
 *
 * <AUTHOR>
 */
public interface IVisitorServiceAssignmentService {

    /**
     * 为游客分配客服
     *
     * @param visitorId  游客ID
     * @param businessId 商户ID
     * @param channel    渠道
     * @return 分配的客服ID，如果分配失败返回null
     */
    Long assignServiceToVisitor(String visitorId, Long businessId, String channel);

    /**
     * 转发消息给指定客服
     *
     * @param visitorId   游客ID
     * @param businessId  商户ID
     * @param serviceId   客服ID
     * @param content     消息内容
     * @param messageType 消息类型
     */
    void forwardMessageToService(String visitorId, Long businessId, Long serviceId, String content, String messageType);

    /**
     * 转发消息给已分配的客服
     *
     * @param visitorId   游客ID
     * @param businessId  商户ID
     * @param content     消息内容
     * @param messageType 消息类型
     */
    void forwardMessageToAssignedService(String visitorId, Long businessId, String content, String messageType);

    /**
     * 通知客服游客已离线
     *
     * @param visitorId  游客ID
     * @param businessId 商户ID
     */
    void notifyServiceVisitorOffline(String visitorId, Long businessId);

    /**
     * 获取游客当前分配的客服ID
     *
     * @param visitorId  游客ID
     * @param businessId 商户ID
     * @return 客服ID，如果未分配返回null
     */
    String getAssignedServiceId(String visitorId, Long businessId);

    /**
     * 释放游客与客服的分配关系
     *
     * @param visitorId  游客ID
     * @param businessId 商户ID
     */
    void releaseAssignment(String visitorId, Long businessId);

    /**
     * 检查客服是否在线
     *
     * @param serviceId  客服ID
     * @param businessId 商户ID
     * @return 是否在线
     */
    boolean isServiceOnline(Long serviceId, Long businessId);

    /**
     * 获取客服当前服务的游客数量
     *
     * @param serviceId  客服ID
     * @param businessId 商户ID
     * @return 服务的游客数量
     */
    int getServiceVisitorCount(Long serviceId, Long businessId);
}
