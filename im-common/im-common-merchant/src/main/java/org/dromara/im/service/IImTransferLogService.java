package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImTransferLogBo;
import org.dromara.im.domain.vo.ImTransferLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 会话转接记录Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-17
 */
public interface IImTransferLogService {

    /**
     * 查询会话转接记录
     *
     * @param id 主键
     * @return 会话转接记录
     */
    ImTransferLogVo queryById(Long id);

    /**
     * 分页查询会话转接记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 会话转接记录分页列表
     */
    TableDataInfo<ImTransferLogVo> queryPageList(ImTransferLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的会话转接记录列表
     *
     * @param bo 查询条件
     * @return 会话转接记录列表
     */
    List<ImTransferLogVo> queryList(ImTransferLogBo bo);

    /**
     * 新增会话转接记录
     *
     * @param bo 会话转接记录
     * @return 是否新增成功
     */
    Boolean insertByBo(ImTransferLogBo bo);

    /**
     * 修改会话转接记录
     *
     * @param bo 会话转接记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ImTransferLogBo bo);

    /**
     * 校验并批量删除会话转接记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
