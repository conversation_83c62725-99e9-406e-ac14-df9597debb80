package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImMemberGroup;
import org.dromara.im.domain.bo.ImMemberGroupBo;
import org.dromara.im.domain.vo.ImMemberGroupVo;
import org.dromara.im.mapper.ImMemberGroupMapper;
import org.dromara.im.service.IImMemberGroupService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 成员分组Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImMemberGroupServiceImpl implements IImMemberGroupService {

    private final ImMemberGroupMapper baseMapper;

    /**
     * 查询成员分组
     *
     * @param memberGroupId 主键
     * @return 成员分组
     */
    @Override
    public ImMemberGroupVo queryById(Long memberGroupId) {
        return baseMapper.selectVoById(memberGroupId);
    }

    /**
     * 分页查询成员分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 成员分组分页列表
     */
    @Override
    public TableDataInfo<ImMemberGroupVo> queryPageList(ImMemberGroupBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMemberGroup> lqw = buildQueryWrapper(bo);
        Page<ImMemberGroupVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的成员分组列表
     *
     * @param bo 查询条件
     * @return 成员分组列表
     */
    @Override
    public List<ImMemberGroupVo> queryList(ImMemberGroupBo bo) {
        LambdaQueryWrapper<ImMemberGroup> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMemberGroup> buildQueryWrapper(ImMemberGroupBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMemberGroup> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImMemberGroup::getMemberGroupId);
        lqw.eq(Objects.nonNull(bo.getMerchantId()), ImMemberGroup::getMerchantId, bo.getMerchantId());
        lqw.like(StringUtils.isNotBlank(bo.getGroupName()), ImMemberGroup::getGroupName, bo.getGroupName());
        lqw.eq(StringUtils.isNotBlank(bo.getWebhook()), ImMemberGroup::getWebhook, bo.getWebhook());
        lqw.eq(StringUtils.isNotBlank(bo.getWebhookUrl()), ImMemberGroup::getWebhookUrl, bo.getWebhookUrl());
        return lqw;
    }

    /**
     * 新增成员分组
     *
     * @param bo 成员分组
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMemberGroupBo bo) {
        ImMemberGroup add = MapstructUtils.convert(bo, ImMemberGroup.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMemberGroupId(add.getMemberGroupId());
        }
        return flag;
    }

    /**
     * 修改成员分组
     *
     * @param bo 成员分组
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMemberGroupBo bo) {
        ImMemberGroup update = MapstructUtils.convert(bo, ImMemberGroup.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMemberGroup entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除成员分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
