package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImMemberGroup;
import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.domain.ImMerchantChannel;
import org.dromara.im.domain.ImMerchantChannelRelation;
import org.dromara.im.domain.bo.ImMerchantAdministratorBo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantChannelRelationVo;
import org.dromara.im.mapper.ImMemberGroupMapper;
import org.dromara.im.mapper.ImMerchantAdministratorMapper;
import org.dromara.im.mapper.ImMerchantChannelMapper;
import org.dromara.im.mapper.ImMerchantChannelRelationMapper;
import org.dromara.im.service.IImMerchantAdministratorService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商户管理员Service业务层处理
 * <p>
 * 主要功能：
 * 1. 商户管理员的CRUD操作
 * 2. 管理员与渠道关系的查询和维护
 * 3. 分页查询时自动填充渠道信息
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImMerchantAdministratorServiceImpl implements IImMerchantAdministratorService {

    /**
     * 商户管理员数据访问层
     */
    private final ImMerchantAdministratorMapper baseMapper;

    /**
     * 商户渠道关系数据访问层
     */
    private final ImMerchantChannelRelationMapper merchantChannelRelationMapper;

    /**
     * 渠道配置数据访问层
     */
    private final ImMerchantChannelMapper merchantChannelMapper;


    private final ImMemberGroupMapper merchantGroupMapper;

    /**
     * 查询商户管理员
     *
     * @param adminId 主键
     * @return 商户管理员
     */
    @Override
    public ImMerchantAdministratorVo queryById(Long adminId) {
        return baseMapper.selectVoById(adminId);
    }

    /**
     * 分页查询商户管理员列表
     * <p>
     * 功能说明：
     * 1. 根据查询条件构建查询包装器
     * 2. 如果指定了渠道ID，则先查询该渠道下的管理员
     * 3. 执行分页查询
     * 4. 为查询结果填充渠道名称信息
     *
     * @param bo        查询条件（包含管理员基本信息和渠道ID等）
     * @param pageQuery 分页参数（页码、页大小等）
     * @return 商户管理员分页列表（包含渠道名称信息）
     */
    @Override
    public TableDataInfo<ImMerchantAdministratorVo> queryPageList(ImMerchantAdministratorBo bo, PageQuery pageQuery) {
        // 构建基础查询条件
        LambdaQueryWrapper<ImMerchantAdministrator> lqw = buildQueryWrapper(bo);

        // 如果指定了渠道ID，则需要先查询该渠道下的管理员ID列表
        if (Objects.nonNull(bo.getMerchantChannelId())) {
            // 查询指定渠道下的商户渠道关系
            List<ImMerchantChannelRelationVo> channelRelations = merchantChannelRelationMapper.selectVoList(
                Wrappers.lambdaQuery(ImMerchantChannelRelation.class)
                    .eq(ImMerchantChannelRelation::getMerchantChannelId, bo.getMerchantChannelId())
            );

            // 提取管理员ID列表
            List<Long> adminIdList = channelRelations.stream()
                .map(ImMerchantChannelRelationVo::getAdminId)
                .toList();

            // 添加管理员ID过滤条件
            lqw.in(ImMerchantAdministrator::getAdminId, adminIdList);
        }

        // 执行分页查询
        Page<ImMerchantAdministratorVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 提取所有管理员ID，用于后续查询渠道信息
        List<Long> adminIdList = result.getRecords().stream()
            .map(ImMerchantAdministratorVo::getAdminId)
            .distinct()
            .toList();
        // 如果管理员ID列表不为空，则查询并填充渠道信息
        if (CollectionUtils.isNotEmpty(adminIdList)) {
            // 1. 根据管理员ID列表查询商户渠道关系
            List<ImMerchantChannelRelationVo> imMerchantChannelRelationVos = merchantChannelRelationMapper
                .selectVoList(Wrappers.lambdaQuery(ImMerchantChannelRelation.class)
                    .in(ImMerchantChannelRelation::getAdminId, adminIdList)
                );

            if (CollectionUtils.isNotEmpty(imMerchantChannelRelationVos)) {
                // 2. 提取所有渠道ID
                List<Long> channelIdList = imMerchantChannelRelationVos.stream()
                    .map(ImMerchantChannelRelationVo::getMerchantChannelId)
                    .distinct()
                    .toList();

                // 3. 根据渠道ID列表查询渠道配置信息（只查询必要字段：ID和名称）
                List<ImMerchantChannel> imChannelConfigs = merchantChannelMapper.selectList(
                    Wrappers.lambdaQuery(ImMerchantChannel.class)
                        .select(ImMerchantChannel::getMerchantChannelId,
                            ImMerchantChannel::getChannelName, ImMerchantChannel::getMerchantChannelName)
                        .in(ImMerchantChannel::getMerchantChannelId, channelIdList)
                        .eq(ImMerchantChannel::getDefaultChannel, "0")
                );

                // 4. 构建渠道ID -> 渠道配置的映射，便于快速查找
                Map<Long, ImMerchantChannel> channelConfigMap = imChannelConfigs.stream()
                    .collect(Collectors.toMap(ImMerchantChannel::getMerchantChannelId, e -> e));

                // 5. 按管理员ID分组商户渠道关系，便于后续填充
                Map<Long, List<ImMerchantChannelRelationVo>> adminChannelRelationMap = imMerchantChannelRelationVos.stream()
                    .collect(Collectors.groupingBy(ImMerchantChannelRelationVo::getAdminId));

                // 6. 遍历结果列表，为每个管理员填充渠道名称
                for (ImMerchantAdministratorVo var : result.getRecords()) {
                    // 获取当前管理员的渠道关系列表
                    List<ImMerchantChannelRelationVo> adminChannelRelations = adminChannelRelationMap.get(var.getAdminId());

                    if (CollectionUtils.isNotEmpty(adminChannelRelations)) {
                        // 提取渠道名称列表并设置到管理员对象中
                        List<String> channelNameList = adminChannelRelations.stream()
                            .map(relation -> {

                                ImMerchantChannel imChannelConfig = channelConfigMap.get(relation.getMerchantChannelId());
                                if (Objects.nonNull(imChannelConfig)) {
                                    return imChannelConfig.getMerchantChannelName();
                                }
                                return null;
                            }).filter(Objects::nonNull)
                            .toList();

                        List<Long> merchantChannelIdList = adminChannelRelations.stream()
                            .map(relation -> {

                                ImMerchantChannel imChannelConfig = channelConfigMap.get(relation.getMerchantChannelId());
                                if (Objects.nonNull(imChannelConfig)) {
                                    return imChannelConfig.getMerchantChannelId();
                                }
                                return null;
                            }).filter(Objects::nonNull)
                            .toList();
                        var.setMerchantChannelNameList(channelNameList);
                        var.setMerchantChannelIdList(merchantChannelIdList);
                    }
                }
            }
        }


        // 根据分组id查询分组

        List<Long> memberGroupIdList = result.getRecords().stream()
            .map(ImMerchantAdministratorVo::getMemberGroupId)
            .distinct()
            .toList();

        if (CollectionUtils.isNotEmpty(memberGroupIdList)) {
            List<ImMemberGroup> imChannelConfigs = merchantGroupMapper.selectList(
                Wrappers.lambdaQuery(ImMemberGroup.class)
                    .select(ImMemberGroup::getGroupName, ImMemberGroup::getMemberGroupId)
                    .in(ImMemberGroup::getMemberGroupId, memberGroupIdList)
            );
            Map<Long, ImMemberGroup> channelConfigMap = imChannelConfigs.stream()
                .collect(Collectors.toMap(ImMemberGroup::getMemberGroupId, e -> e));

            for (ImMerchantAdministratorVo var : result.getRecords()) {
                ImMemberGroup imChannelConfig = channelConfigMap.get(var.getMemberGroupId());
                if (Objects.nonNull(imChannelConfig)) {
                    var.setMemberGroupName(imChannelConfig.getGroupName());
                }
            }
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商户管理员列表
     *
     * @param bo 查询条件
     * @return 商户管理员列表
     */
    @Override
    public List<ImMerchantAdministratorVo> queryList(ImMerchantAdministratorBo bo) {
        LambdaQueryWrapper<ImMerchantAdministrator> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMerchantAdministrator> buildQueryWrapper(ImMerchantAdministratorBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMerchantAdministrator> lqw = Wrappers.lambdaQuery();
        if (Objects.equals(bo.getSortParam(), 2)) {
            lqw.orderByAsc(ImMerchantAdministrator::getSequenceSort);
        } else if (Objects.equals(bo.getSortParam(), 3)) {
            lqw.orderByAsc(ImMerchantAdministrator::getPrioritySort);
        } else {
            lqw.orderByDesc(ImMerchantAdministrator::getAdminId);
        }
        lqw.eq(StringUtils.isNotBlank(bo.getAdminCode()), ImMerchantAdministrator::getAdminCode, bo.getAdminCode());
        lqw.eq(bo.getMerchantId() != null, ImMerchantAdministrator::getMerchantId, bo.getMerchantId());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), ImMerchantAdministrator::getEmail, bo.getEmail());
        lqw.like(StringUtils.isNotBlank(bo.getNickname()), ImMerchantAdministrator::getNickname, bo.getNickname());
        lqw.eq(StringUtils.isNotBlank(bo.getRoleType()), ImMerchantAdministrator::getRoleType, bo.getRoleType());
        lqw.eq(bo.getMemberGroupId() != null, ImMerchantAdministrator::getMemberGroupId, bo.getMemberGroupId());
        return lqw;
    }

    /**
     * 新增商户管理员
     *
     * @param bo 商户管理员
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMerchantAdministratorBo bo) {
        ImMerchantAdministrator add = MapstructUtils.convert(bo, ImMerchantAdministrator.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAdminId(add.getAdminId());
        }
        return flag;
    }

    /**
     * 修改商户管理员
     *
     * @param bo 商户管理员
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMerchantAdministratorBo bo) {
        ImMerchantAdministrator update = MapstructUtils.convert(bo, ImMerchantAdministrator.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMerchantAdministrator entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除商户管理员信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<ImMerchantAdministratorVo> queryByIds(List<Long> adminIdList) {
        return baseMapper.selectVoByIds(adminIdList);
    }

    @Override
    public List<ImMerchantAdministratorVo> getServicesByBusinessId(Long businessId) {
        LambdaQueryWrapper<ImMerchantAdministrator> lqw = Wrappers.lambdaQuery(ImMerchantAdministrator.class)
            .eq(ImMerchantAdministrator::getMerchantId, businessId)
            .eq(ImMerchantAdministrator::getStatus, "1");// 只查询启用状态的客服
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public void sortAdmin(ImMerchantAdministratorBo bo) {
        baseMapper.update(new LambdaUpdateWrapper<>(ImMerchantAdministrator.class)
            .set(Objects.equals(bo.getSortParam(), 2), ImMerchantAdministrator::getSequenceSort, bo.getSort())
            .set(Objects.equals(bo.getSortParam(), 3), ImMerchantAdministrator::getPrioritySort, bo.getSort())
            .eq(ImMerchantAdministrator::getAdminId, bo.getAdminId())
        );
    }
}
