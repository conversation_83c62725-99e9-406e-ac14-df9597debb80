package org.dromara.im.utils;

import lombok.Getter;
import org.dromara.common.core.utils.StringUtils;

/**
 * 密码值对象
 *
 * <AUTHOR>
 */
@Getter
public class Password {


    private final String value;

    public Password(String password) {
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        this.value = password;
    }


    public static boolean matches(String password, String inputPassword) {
        return StringUtils.equals(password, inputPassword);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Password password = (Password) o;
        return value.equals(password.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return "Password{value='***'}";
    }
}
