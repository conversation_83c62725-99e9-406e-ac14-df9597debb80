package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ImException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImChatPackage;
import org.dromara.im.domain.bo.ImChatPackageBo;
import org.dromara.im.domain.vo.ImChatPackageVo;
import org.dromara.im.mapper.ImChatPackageMapper;
import org.dromara.im.service.IImChatPackageService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 套餐Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImChatPackageServiceImpl implements IImChatPackageService {

    private final ImChatPackageMapper baseMapper;

    /**
     * 查询套餐
     *
     * @param id 主键
     * @return 套餐
     */
    @Override
    public ImChatPackageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询套餐列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 套餐分页列表
     */
    @Override
    public TableDataInfo<ImChatPackageVo> queryPageList(ImChatPackageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImChatPackage> lqw = buildQueryWrapper(bo);
        Page<ImChatPackageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的套餐列表
     *
     * @param bo 查询条件
     * @return 套餐列表
     */
    @Override
    public List<ImChatPackageVo> queryList(ImChatPackageBo bo) {
        LambdaQueryWrapper<ImChatPackage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImChatPackage> buildQueryWrapper(ImChatPackageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImChatPackage> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImChatPackage::getSortOrder);
        lqw.like(StringUtils.isNotBlank(bo.getPackageName()), ImChatPackage::getPackageName, bo.getPackageName());
        lqw.eq(StringUtils.isNotBlank(bo.getPackageType()), ImChatPackage::getPackageType, bo.getPackageType());
        lqw.eq(bo.getPrice() != null, ImChatPackage::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), ImChatPackage::getCurrency, bo.getCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getBillingCycle()), ImChatPackage::getBillingCycle, bo.getBillingCycle());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), ImChatPackage::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getFeatureConfig()), ImChatPackage::getFeatureConfig, bo.getFeatureConfig());
        lqw.eq(bo.getStatus() != null, ImChatPackage::getStatus, bo.getStatus());
        lqw.eq(bo.getSortOrder() != null, ImChatPackage::getSortOrder, bo.getSortOrder());
        return lqw;
    }

    /**
     * 新增套餐
     *
     * @param bo 套餐
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImChatPackageBo bo) {
        ImChatPackage add = MapstructUtils.convert(bo, ImChatPackage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setChatPackageId(add.getChatPackageId());
        }
        return flag;
    }

    /**
     * 修改套餐
     *
     * @param bo 套餐
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImChatPackageBo bo) {
        ImChatPackage update = MapstructUtils.convert(bo, ImChatPackage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImChatPackage entity) {
        //TODO 做一些数据校验,如唯一约束
        Long l = baseMapper.selectCount(new LambdaQueryWrapper<>(ImChatPackage.class)
            .ne(Objects.nonNull(entity.getChatPackageId()), ImChatPackage::getChatPackageId, entity.getChatPackageId())
            .eq(ImChatPackage::getPackageName, entity.getPackageName())
        );
        if (l > 0) {
            throw new ImException("套餐名称已存在");
        }
    }

    /**
     * 校验并批量删除套餐信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
