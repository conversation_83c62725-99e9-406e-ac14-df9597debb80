package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.im.domain.ImChatPackage;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 套餐视图对象 im_chat_package
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImChatPackage.class)
public class ImChatPackageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @ExcelProperty(value = "套餐ID")
    private Long chatPackageId;

    /**
     * 套餐名称
     */
    @ExcelProperty(value = "套餐名称")
    private String packageName;

    /**
     * 1->标准版;2->专业版;3->集团版
     */
    @ExcelProperty(value = "1->标准版;2->专业版;3->集团版")
    private String packageType;

    /**
     * 套餐价格(USD)
     */
    @ExcelProperty(value = "套餐价格(USD)")
    private BigDecimal price;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 货币单位
     */
    @ExcelProperty(value = "货币单位")
    private String currency;

    @ExcelProperty(value = "计费周期数量")
    private Integer billingPeriod;

    /**
     * 计费周期(0-月付 1-季付 2-年付)
     */
    @ExcelProperty(value = "计费周期")
    private String billingCycle;

    /**
     * 套餐描述
     */
    @ExcelProperty(value = "套餐描述")
    private String description;

    /**
     * 功能配置JSON，采用驼峰命名
     */
    @ExcelProperty(value = "功能配置JSON，采用驼峰命名")
    private String featureConfig;

    /**
     * 状态(1-启用 0-禁用)
     */
    @ExcelProperty(value = "状态(1-启用 0-禁用)")
    private Long status;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long sortOrder;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


    private PackageFeatureVO packageFeatureVO;

    public PackageFeatureVO getPackageFeatureVO() {
        if (StringUtils.isNotEmpty(featureConfig)) {
            return JsonUtils.parseObject(featureConfig, PackageFeatureVO.class);
        }
        return packageFeatureVO;
    }

    public BigDecimal getOriginalPrice() {
        if (price == null) {
            return BigDecimal.ZERO;
        }
        return price.multiply(new BigDecimal("1.5"));
    }
}
