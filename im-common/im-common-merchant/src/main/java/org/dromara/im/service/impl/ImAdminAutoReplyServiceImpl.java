package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
    import org.dromara.common.mybatis.core.page.PageQuery;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.im.domain.ImMerchantChannel;
import org.dromara.im.domain.dto.AutoReplyDTO;
import org.dromara.im.domain.vo.ImMerchantChannelVo;
import org.dromara.im.mapper.ImMerchantChannelMapper;
import org.dromara.im.service.IImMerchantChannelService;
import org.dromara.im.utils.SendMessageUtil;
import org.springframework.stereotype.Service;
import org.dromara.im.domain.bo.ImAdminAutoReplyBo;
import org.dromara.im.domain.vo.ImAdminAutoReplyVo;
import org.dromara.im.domain.ImAdminAutoReply;
import org.dromara.im.mapper.ImAdminAutoReplyMapper;
import org.dromara.im.service.IImAdminAutoReplyService;

import java.nio.file.Watchable;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Objects;

/**
 * 用户自动回复配置Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImAdminAutoReplyServiceImpl implements IImAdminAutoReplyService {

    private final ImAdminAutoReplyMapper baseMapper;

    private final ImMerchantChannelMapper merchantChannelMapper;

    /**
     * 查询用户自动回复配置
     *
     * @param adminnAutoReplyId 主键
     * @return 用户自动回复配置
     */
    @Override
    public ImAdminAutoReplyVo queryById(Long adminnAutoReplyId) {
        return baseMapper.selectVoById(adminnAutoReplyId);
    }

        /**
         * 分页查询用户自动回复配置列表
         *
         * @param bo        查询条件
         * @param pageQuery 分页参数
         * @return 用户自动回复配置分页列表
         */
        @Override
        public TableDataInfo<ImAdminAutoReplyVo> queryPageList(ImAdminAutoReplyBo bo, PageQuery pageQuery) {
            LambdaQueryWrapper<ImAdminAutoReply> lqw = buildQueryWrapper(bo);
            Page<ImAdminAutoReplyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
            return TableDataInfo.build(result);
        }

    /**
     * 查询符合条件的用户自动回复配置列表
     *
     * @param bo 查询条件
     * @return 用户自动回复配置列表
     */
    @Override
    public List<ImAdminAutoReplyVo> queryList(ImAdminAutoReplyBo bo) {
        LambdaQueryWrapper<ImAdminAutoReply> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImAdminAutoReply> buildQueryWrapper(ImAdminAutoReplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImAdminAutoReply> lqw = Wrappers.lambdaQuery();
                lqw.orderByAsc(ImAdminAutoReply::getAdminAutoReplyId);
                    lqw.eq(bo.getAdminId() != null, ImAdminAutoReply::getAdminId, bo.getAdminId());
                    lqw.eq(bo.getMerchantChannelId() != null, ImAdminAutoReply::getMerchantChannelId, bo.getMerchantChannelId());
                    lqw.eq(StringUtils.isNotBlank(bo.getAutoReplyContext()), ImAdminAutoReply::getAutoReplyContext, bo.getAutoReplyContext());
                    lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImAdminAutoReply::getTenantId, bo.getTenantId());
        return lqw;
    }

    @Override
    public ImAdminAutoReplyVo queryOne(ImAdminAutoReplyBo bo) {
        LambdaQueryWrapper<ImAdminAutoReply> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 新增用户自动回复配置
     *
     * @param bo 用户自动回复配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImAdminAutoReplyBo bo) {
        ImAdminAutoReply add = MapstructUtils.convert(bo, ImAdminAutoReply. class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAdminAutoReplyId(add.getAdminAutoReplyId());
        }
        return flag;
    }

    /**
     * 修改用户自动回复配置
     *
     * @param bo 用户自动回复配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImAdminAutoReplyBo bo) {
        ImAdminAutoReply update = MapstructUtils.convert(bo, ImAdminAutoReply. class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImAdminAutoReply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户自动回复配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public boolean checkSendStartServiceWarn(Long adminId, Long merchantId, Long merchantChannelId) {
        AutoReplyDTO autoReplyDTO = queryAutoReplyDTO(adminId, merchantId, merchantChannelId);
        if (AutoReplyDTO.enableWelcomeMessageEnabled(autoReplyDTO)) {
            return true;
        }
        return false;
    }

    private AutoReplyDTO queryAutoReplyDTO(Long adminId, Long merchantId, Long merchantChannelId) {
        if (Objects.isNull(merchantChannelId)) {
            LambdaQueryWrapper<ImMerchantChannel> lwq = Wrappers.lambdaQuery();
            lwq.eq(ImMerchantChannel::getDefaultChannel, "1");
            lwq.eq(ImMerchantChannel::getMerchantId, merchantId);
            lwq.last("limit 1");
            ImMerchantChannelVo merchantChannelVo = merchantChannelMapper.selectVoOne(lwq);
            merchantChannelId = merchantChannelVo.getMerchantChannelId();
        }
        LambdaQueryWrapper<ImAdminAutoReply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImAdminAutoReply::getAdminId, adminId);
        wrapper.eq(ImAdminAutoReply::getMerchantChannelId, merchantChannelId);
        wrapper.last("limit 1");
        ImAdminAutoReplyVo imAdminAutoReplyVo = baseMapper.selectVoOne(wrapper);
        if (Objects.nonNull(imAdminAutoReplyVo)) {
            AutoReplyDTO autoReplyDTO = JsonUtils.parseObject(imAdminAutoReplyVo.getAutoReplyContext(), AutoReplyDTO.class);
            return autoReplyDTO;
        }else {
            return AutoReplyDTO.getDefault();
        }
    }

    @Override
    public void checkSendEndServiceWarn(Long adminId, Long merchantId, Long merchantChannelId) {

    }

    @Override
    public void checkSendBusyServiceWarn(Long adminId, Long merchantId, Long merchantChannelId) {

    }

    @Override
    public void checkSendNoVisitorResponseWarn(Long adminId, Long merchantId, Long merchantChannelId) {

    }
}
