package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImChannelTranslateBo;
import org.dromara.im.domain.vo.ImChannelTranslateVo;

import java.util.Collection;
import java.util.List;

/**
 * 渠道翻译设置Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
public interface IImChannelTranslateService {

    /**
     * 查询渠道翻译设置
     *
     * @param channelTranslateId 主键
     * @return 渠道翻译设置
     */
    ImChannelTranslateVo queryById(Long channelTranslateId);

    /**
     * 分页查询渠道翻译设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道翻译设置分页列表
     */
    TableDataInfo<ImChannelTranslateVo> queryPageList(ImChannelTranslateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的渠道翻译设置列表
     *
     * @param bo 查询条件
     * @return 渠道翻译设置列表
     */
    List<ImChannelTranslateVo> queryList(ImChannelTranslateBo bo);

    /**
     * 新增渠道翻译设置
     *
     * @param bo 渠道翻译设置
     * @return 是否新增成功
     */
    Boolean insertByBo(ImChannelTranslateBo bo);

    /**
     * 修改渠道翻译设置
     *
     * @param bo 渠道翻译设置
     * @return 是否修改成功
     */
    Boolean updateByBo(ImChannelTranslateBo bo);

    /**
     * 校验并批量删除渠道翻译设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    int batchInsertByBo(List<ImChannelTranslateBo> bo, Long adminId);
}
