package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.ImPaymentOrder;
import org.dromara.im.domain.bo.ImPaymentOrderBo;
import org.dromara.im.domain.dto.PayRequest;
import org.dromara.im.domain.vo.ImChatPackageVo;
import org.dromara.im.domain.vo.ImPaymentOrderVo;
import org.dromara.im.domain.vo.ImTranslationPackageVo;
import org.dromara.im.mapper.ImChatPackageMapper;
import org.dromara.im.mapper.ImMerchantMapper;
import org.dromara.im.mapper.ImPaymentOrderMapper;
import org.dromara.im.mapper.ImTranslationPackageMapper;
import org.dromara.im.service.IImPaymentOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支付订单Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImPaymentOrderServiceImpl implements IImPaymentOrderService {

    private final ImPaymentOrderMapper baseMapper;

    private final ImChatPackageMapper chatPackageMapper;

    private final ImTranslationPackageMapper imTranslationPackageMapper;

    private final ImMerchantMapper merchantMapper;

    /**
     * 查询支付订单
     *
     * @param paymentOrderId 主键
     * @return 支付订单
     */
    @Override
    public ImPaymentOrderVo queryById(Long paymentOrderId) {
        return baseMapper.selectVoById(paymentOrderId);
    }

    /**
     * 分页查询支付订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付订单分页列表
     */
    @Override
    public TableDataInfo<ImPaymentOrderVo> queryPageList(ImPaymentOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImPaymentOrder> lqw = buildQueryWrapper(bo);
        Page<ImPaymentOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<ImPaymentOrderVo> records = result.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> merchantIdList = records.stream()
                .map(ImPaymentOrderVo::getMerchantId)
                .toList();
            if (CollectionUtils.isNotEmpty(merchantIdList)) {
                List<ImMerchant> merchantList = merchantMapper.selectByIds(merchantIdList);
                Map<Long, ImMerchant> merchantMap = merchantList.stream()
                    .collect(Collectors.toMap(ImMerchant::getMerchantId, v -> v));
                records.forEach(record -> {
                    ImMerchant imMerchant = merchantMap.get(record.getMerchantId());
                    if (imMerchant != null) {
                        record.setMerchantName(imMerchant.getCompanyName());
                    } else {
                        record.setMerchantName("未知商户");
                    }
                });
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付订单列表
     *
     * @param bo 查询条件
     * @return 支付订单列表
     */
    @Override
    public List<ImPaymentOrderVo> queryList(ImPaymentOrderBo bo) {
        LambdaQueryWrapper<ImPaymentOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImPaymentOrder> buildQueryWrapper(ImPaymentOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImPaymentOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ImPaymentOrder::getCreateTime);
        lqw.eq(bo.getMerchantId() != null, ImPaymentOrder::getMerchantId, bo.getMerchantId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderType()), ImPaymentOrder::getOrderType, bo.getOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderStatus()), ImPaymentOrder::getOrderStatus, bo.getOrderStatus());
        lqw.eq(bo.getOriginalPrice() != null, ImPaymentOrder::getOriginalPrice, bo.getOriginalPrice());
        lqw.eq(bo.getPaymentAmount() != null, ImPaymentOrder::getPaymentAmount, bo.getPaymentAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), ImPaymentOrder::getPaymentMethod, bo.getPaymentMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderDetail()), ImPaymentOrder::getOrderDetail, bo.getOrderDetail());
        lqw.eq(StringUtils.isNotBlank(bo.getExtra()), ImPaymentOrder::getExtra, bo.getExtra());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImPaymentOrder::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增支付订单
     *
     * @param bo 支付订单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImPaymentOrderBo bo) {
        ImPaymentOrder add = MapstructUtils.convert(bo, ImPaymentOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPaymentOrderId(add.getPaymentOrderId());
        }
        return flag;
    }

    /**
     * 修改支付订单
     *
     * @param bo 支付订单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImPaymentOrderBo bo) {
        ImPaymentOrder update = MapstructUtils.convert(bo, ImPaymentOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImPaymentOrder entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除支付订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional
    public void create(PayRequest request, Long merchantId) {
        ImPaymentOrder entity = new ImPaymentOrder();
        entity.setMerchantId(merchantId);
        entity.setOrderType(request.getOrderType());
        entity.setOrderStatus("0"); // 待支付
        entity.setPaymentMethod("0"); // USDT

        switch (request.getOrderType()) {
            case "0": // 商户订单（包括套餐购买和套餐升级）
                createChatPackageUpgradeOrder(entity, request);
                break;
            case "1": // 翻译订单（包括翻译套餐购买和翻译容量升级）
                // 翻译容量升级
                createTranslationCapacityUpgradeOrder(entity, request);
                break;
            default:
                throw new IllegalArgumentException("不支持的订单类型: " + request.getOrderType());
        }

        baseMapper.insert(entity);
    }

    @Override
    @Transactional
    public void paySuccess(ImPaymentOrderBo order) {
        ImPaymentOrder imPaymentOrder = baseMapper.selectById(order.getPaymentOrderId());
        updateMerchantAfterOrderCreated(imPaymentOrder);
    }

    @Override
    @Transactional
    public void payFailed(ImPaymentOrderBo order) {
        baseMapper.update(new LambdaUpdateWrapper<>(ImPaymentOrder.class)
            .set(ImPaymentOrder::getOrderStatus, "2")
            .eq(ImPaymentOrder::getPaymentOrderId, order.getPaymentOrderId())
        );
    }

    /**
     * 创建商户套餐升级订单
     */
    private void createChatPackageUpgradeOrder(ImPaymentOrder entity, PayRequest request) {
        if (request.getChatPackageId() == null) {
            throw new IllegalArgumentException("当前套餐ID不能为空");
        }

        // 获取当前套餐信息
        ImChatPackageVo currentPackage = chatPackageMapper.selectVoById(request.getChatPackageId());
        if (currentPackage == null) {
            throw new IllegalArgumentException("当前套餐不存在");
        }

        // 根据计费周期计算实际的月数  价格*坐席数
        BigDecimal payAmount = currentPackage.getPrice()
            .multiply(new BigDecimal(request.getSeatsCount()));
        entity.setOriginalPrice(payAmount);
        entity.setPaymentAmount(payAmount);
        entity.setOrderDetail("购买" + currentPackage.getPackageName());

        // 将套餐信息存储到extra字段
        HashMap<Object, Object> data = new HashMap<>();
        data.put("chatPackage", JsonUtils.toJsonString(currentPackage));
        data.put("seatsCount", request.getSeatsCount());
        entity.setExtra(JsonUtils.toJsonString(data));
    }

    /**
     * 创建翻译容量升级订单
     */
    private void createTranslationCapacityUpgradeOrder(ImPaymentOrder entity, PayRequest bo) {


        // 获取翻译套餐信息
        ImTranslationPackageVo translationPackage = imTranslationPackageMapper.selectVoById(bo.getTranslatePackageId());
        if (translationPackage == null) {
            throw new IllegalArgumentException("翻译套餐不存在");
        }


        entity.setOriginalPrice(translationPackage.getPrice());
        entity.setPaymentAmount(translationPackage.getPrice());
        entity.setOrderDetail("翻译容量购买 " + translationPackage.getCharacterQuota() + "万字符 (" +
            translationPackage.getPrice() + " / 万字符)");
        HashMap<Object, Object> data = new HashMap<>();
        data.put("translationPackage", JsonUtils.toJsonString(translationPackage));
        entity.setExtra(JsonUtils.toJsonString(data));
    }

    /**
     * 订单创建成功后更新商户信息
     *
     * @param order 支付订单
     */
    private void updateMerchantAfterOrderCreated(ImPaymentOrder order) {
        try {
            // 根据订单类型更新商户信息
            if ("0".equals(order.getOrderType())) {
                // 商户订单 - 更新过期时间
                updateMerchantExpireTime(order);
            } else if ("1".equals(order.getOrderType())) {
                // 翻译订单 - 更新翻译容量
                updateMerchantTranslationCapacity(order);
            }
        } catch (Exception e) {
            log.error("更新商户信息失败，订单ID: {}, 错误: {}", order.getPaymentOrderId(), e.getMessage(), e);
            // 这里可以选择抛出异常回滚事务，或者记录日志继续执行
        }
    }

    /**
     * 更新商户过期时间
     *
     * @param order 支付订单
     */
    private void updateMerchantExpireTime(ImPaymentOrder order) {
        // 获取当前商户信息
        ImMerchant merchant = merchantMapper.selectById(order.getMerchantId());
        if (merchant == null) {
            log.info("商户不存在，无法更新过期时间，商户ID: {}", order.getMerchantId());
            return;
        }

        // 计算新的过期时间
        long currentTime = System.currentTimeMillis();
        long currentExpireTime = merchant.getExpiredTime() != null ? merchant.getExpiredTime() : currentTime;

        // 如果当前过期时间已经过期，则从当前时间开始计算
        long baseTime = Math.max(currentExpireTime, currentTime);

        // 从订单的extra字段中获取套餐信息来计算延长时间
        ImChatPackageVo chatPackageVo = getExtendMonthsFromOrderExtra(order);
        // 根据计费周期计算延长时间

        long extendTime = calculateExtendTime(chatPackageVo.getBillingPeriod(), chatPackageVo.getBillingCycle());

        long newExpireTime = baseTime + extendTime;

        Integer seatsCount = getSeatsCountFromOrderExtra(order);

        Integer chatNumber = chatPackageVo.buildPackageFeatureVO().getChatNumber();

        // 更新商户过期时间
        merchantMapper.update(new LambdaUpdateWrapper<>(ImMerchant.class)
            .set(ImMerchant::getSeatsCount, seatsCount)
            .set(ImMerchant::getPackageType, chatPackageVo.getPackageType())
            .set(ImMerchant::getExpiredTime, newExpireTime)
            .set(ImMerchant::getChatNumber, chatNumber)
            .eq(ImMerchant::getMerchantId, order.getMerchantId())
        );

        baseMapper.update(new LambdaUpdateWrapper<>(ImPaymentOrder.class)
            .set(ImPaymentOrder::getOrderStatus, "1")
            .eq(ImPaymentOrder::getPaymentOrderId, order.getPaymentOrderId())
        );

        log.info("更新商户过期时间成功，商户ID: {}, 新过期时间: {}", order.getMerchantId(), newExpireTime);
    }

    private Integer getSeatsCountFromOrderExtra(ImPaymentOrder order) {
        Map<String, Object> extraData = getExtraData(order);
        // 获取翻译套餐信息
        return (Integer) extraData.get("seatsCount");
    }

    /**
     * 更新商户翻译容量
     *
     * @param order 支付订单
     */
    private void updateMerchantTranslationCapacity(ImPaymentOrder order) {
        // 获取当前商户信息
        ImMerchant merchant = merchantMapper.selectById(order.getMerchantId());
        if (merchant == null) {
            log.info("商户不存在，无法更新翻译容量，商户ID: {}", order.getMerchantId());
            return;
        }

        // 从订单的extra字段中获取翻译容量
        long addCapacity = getTranslationCapacityFromOrderExtra(order);

        if (addCapacity > 0) {
            // 当前翻译总容量
            long currentCapacity = merchant.getTranslateNumber() != null ? merchant.getTranslateNumber() : 0L;
            long newCapacity = currentCapacity + addCapacity;

            // 更新商户翻译容量
            merchantMapper.update(new LambdaUpdateWrapper<>(ImMerchant.class)
                .set(ImMerchant::getTranslateNumber, newCapacity)
                .eq(ImMerchant::getMerchantId, order.getMerchantId())
            );

            log.info("更新商户翻译容量成功，商户ID: {}, 增加容量: {}万字符, 新总容量: {}万字符",
                order.getMerchantId(), addCapacity, newCapacity);
        }
    }

    /**
     * 从订单的extra字段中获取延长月数
     *
     * @param order 支付订单
     * @return 延长月数
     */
    private ImChatPackageVo getExtendMonthsFromOrderExtra(ImPaymentOrder order) {
        if (StringUtils.isEmpty(order.getExtra())) {
            return null;
        }

        try {
            // 解析extra字段中的数据
            Map<String, Object> extraData = getExtraData(order);
            // 获取套餐信息
            String chatPackageJson = (String) extraData.get("chatPackage");
            ImChatPackageVo chatPackage = JsonUtils.parseObject(chatPackageJson, ImChatPackageVo.class);

            if (chatPackage != null) {
                // 根据计费周期计算实际月数
                return chatPackage;
            }
        } catch (Exception e) {
            log.error("解析订单extra字段失败，订单ID: {}, extra: {}", order.getPaymentOrderId(), order.getExtra(), e);
        }

        return null;
    }

    private static Map<String, Object> getExtraData(ImPaymentOrder order) {
        return JsonUtils.parseMap(order.getExtra());
    }

    /**
     * 从订单的extra字段中获取翻译容量
     *
     * @param order 支付订单
     * @return 翻译容量（万字符）
     */
    private long getTranslationCapacityFromOrderExtra(ImPaymentOrder order) {
        if (StringUtils.isEmpty(order.getExtra())) {
            return 0L;
        }

        try {
            // 解析extra字段中的数据
            Map<String, Object> extraData = getExtraData(order);
            if (extraData == null || !extraData.containsKey("translationPackage")) {
                return 0L;
            }

            // 获取翻译套餐信息
            String translationPackageJson = (String) extraData.get("translationPackage");
            ImTranslationPackageVo translationPackage = JsonUtils.parseObject(translationPackageJson, ImTranslationPackageVo.class);

            if (translationPackage != null && translationPackage.getCharacterQuota() != null) {
                return translationPackage.getCharacterQuota();
            }
        } catch (Exception e) {
            log.error("解析订单extra字段中的翻译套餐信息失败，订单ID: {}, extra: {}", order.getPaymentOrderId(), order.getExtra(), e);
        }

        return 0L;
    }

    /**
     * 根据计费周期和计费周期数量计算延长时间
     *
     * @param billingPeriod 计费周期数量
     * @param billingCycle 计费周期类型 (0-月付 2-年付)
     * @return 延长时间（毫秒）
     */
    private long calculateExtendTime(Integer billingPeriod, String billingCycle) {
        if (billingPeriod == null || billingPeriod <= 0) {
            log.warn("计费周期数量无效: {}", billingPeriod);
            return 0L;
        }

        if (billingCycle == null) {
            log.warn("计费周期类型为空，默认使用月付");
            billingCycle = "0";
        }

        long extendTime;

        switch (billingCycle) {
            case "0": // 月付
                // 每个计费周期 = 30天
                extendTime = billingPeriod * 30L * 24L * 60L * 60L * 1000L;
                log.info("月付计算：{} 个月 = {} 天", billingPeriod, billingPeriod * 30);
                break;
            case "1": // 月付
                // 每个计费周期 = 30天
                extendTime = billingPeriod * 30L * 24L * 60L * 60L * 1000L;
                log.info("月付计算：{} 个月 = {} 天", billingPeriod, billingPeriod * 30);
                break;
            case "2": // 年付
                // 每个计费周期 = 365天
                extendTime = billingPeriod * 365L * 24L * 60L * 60L * 1000L;
                log.info("年付计算：{} 年 = {} 天", billingPeriod, billingPeriod * 365);
                break;

            default:
                log.info("未知的计费周期类型: {}，默认使用月付", billingCycle);
                extendTime = billingPeriod * 30L * 24L * 60L * 60L * 1000L;
                break;
        }

        log.info("计费周期计算结果 - 周期数量: {}, 周期类型: {}, 延长时间: {} 毫秒", billingPeriod, billingCycle, extendTime);

        return extendTime;
    }
}
