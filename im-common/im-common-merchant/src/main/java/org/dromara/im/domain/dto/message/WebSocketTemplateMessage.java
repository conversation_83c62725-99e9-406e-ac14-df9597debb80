package org.dromara.im.domain.dto.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebSocket消息模板，严格对齐前端文档
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketTemplateMessage<T> {
    /**
     * 事件类型，如 "service-event" ”visitor_event”
     * @see MessageEvent
     */
    private String event;


    /**
     * 消息体
     */
    private T data;

}
