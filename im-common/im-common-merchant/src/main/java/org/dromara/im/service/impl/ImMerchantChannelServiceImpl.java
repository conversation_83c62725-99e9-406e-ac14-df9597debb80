package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.exception.ImException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.ImMerchantChannel;
import org.dromara.im.domain.ImMerchantChannelRelation;
import org.dromara.im.domain.bo.ImMerchantChannelBo;
import org.dromara.im.domain.dto.AssignmentRulesDto;
import org.dromara.im.domain.dto.ChannelDialogRulesDto;
import org.dromara.im.domain.vo.ImMerchantChannelVo;
import org.dromara.im.mapper.ImMerchantAdministratorMapper;
import org.dromara.im.mapper.ImMerchantChannelMapper;
import org.dromara.im.mapper.ImMerchantChannelRelationMapper;
import org.dromara.im.mapper.ImMerchantMapper;
import org.dromara.im.service.IImMerchantChannelService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 商户接入渠道（聚合）Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImMerchantChannelServiceImpl implements IImMerchantChannelService {

    private final ImMerchantChannelMapper baseMapper;

    private final ImMerchantMapper merchantMapper;

    private final ImMerchantChannelRelationMapper merchantChannelRelationMapper;


    /**
     * 查询商户接入渠道（聚合）
     *
     * @param id 主键
     * @return 商户接入渠道（聚合）
     */
    @Override
    public ImMerchantChannelVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public List<ImMerchantChannelVo> queryByIds(List<Long> ids) {
        return baseMapper.selectVoByIds(ids);
    }

    /**
     * 分页查询商户接入渠道（聚合）列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户接入渠道（聚合）分页列表
     */
    @Override
    public TableDataInfo<ImMerchantChannelVo> queryPageList(ImMerchantChannelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMerchantChannel> lqw = buildQueryWrapper(bo);
        Page<ImMerchantChannelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商户接入渠道（聚合）列表
     *
     * @param bo 查询条件
     * @return 商户接入渠道（聚合）列表
     */
    @Override
    public List<ImMerchantChannelVo> queryList(ImMerchantChannelBo bo) {
        LambdaQueryWrapper<ImMerchantChannel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMerchantChannel> buildQueryWrapper(ImMerchantChannelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMerchantChannel> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ImMerchantChannel::getMerchantChannelId);
        lqw.eq(bo.getMerchantId() != null, ImMerchantChannel::getMerchantId, bo.getMerchantId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), ImMerchantChannel::getChannelType, bo.getChannelType());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelCode()), ImMerchantChannel::getChannelCode, bo.getChannelCode());
        lqw.like(StringUtils.isNotBlank(bo.getChannelName()), ImMerchantChannel::getChannelName, bo.getChannelName());
        lqw.eq(StringUtils.isNotBlank(bo.getAccessStatus()), ImMerchantChannel::getAccessStatus, bo.getAccessStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getMerchantChannelName()), ImMerchantChannel::getMerchantChannelName, bo.getMerchantChannelName());
        lqw.eq(StringUtils.isNotBlank(bo.getDefaultChannel()), ImMerchantChannel::getDefaultChannel, bo.getDefaultChannel());
        return lqw;
    }

    /**
     * 新增商户接入渠道（聚合）
     *
     * @param bo 商户接入渠道（聚合）
     * @return 是否新增成功
     */
    @Override
    public Long insertByBo(ImMerchantChannelBo bo) {
        ImMerchantChannel add = MapstructUtils.convert(bo, ImMerchantChannel.class);
        validEntityBeforeSave(add);
        // 接入渠道需要做一下限制
        ImMerchant merchant = merchantMapper.selectOne(new LambdaQueryWrapper<>(ImMerchant.class)
            .eq(ImMerchant::getMerchantId, bo.getMerchantId()));
        Long channel = baseMapper.selectCount(new LambdaQueryWrapper<>(ImMerchantChannel.class)
            .eq(ImMerchantChannel::getMerchantId, bo.getMerchantId())
        );
        if (merchant.getChannelNumber() != null && channel >= merchant.getChannelNumber()) {
            throw new ImException("接入渠道数量已达到上限");
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMerchantChannelId(add.getMerchantChannelId());
        }
        return bo.getMerchantChannelId();
    }

    /**
     * 修改商户接入渠道（聚合）
     *
     * @param bo 商户接入渠道（聚合）
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMerchantChannelBo bo) {
        ImMerchantChannel update = MapstructUtils.convert(bo, ImMerchantChannel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMerchantChannel entity) {
        //TODO 做一些数据校验,如唯一约束

        List<ImMerchantChannel> imMerchantChannel = baseMapper.selectList(Wrappers.<ImMerchantChannel>lambdaQuery()
            .eq(ImMerchantChannel::getMerchantId, entity.getMerchantId())
            .eq(ImMerchantChannel::getMerchantChannelName, entity.getMerchantChannelName())
            .ne(Objects.nonNull(entity.getMerchantChannelId()), ImMerchantChannel::getMerchantChannelId, entity.getMerchantChannelId())
        );
        if (CollectionUtils.isNotEmpty(imMerchantChannel)) {
            throw new ImException("接入渠道已存在");
        }
    }

    /**
     * 校验并批量删除商户接入渠道（聚合）信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            Long l = merchantChannelRelationMapper.selectCount(new LambdaQueryWrapper<>(ImMerchantChannelRelation.class)
                .in(ImMerchantChannelRelation::getMerchantChannelId, ids)
            );
            if (l > 0) {
                throw new ImException("当前渠道存在用户，请解除后再试");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public ImMerchantChannelVo queryDialogRules(Long merchantId, Long merchantChannelId) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(ImMerchantChannel.class)
            .select(ImMerchantChannel::getMerchantChannelId, ImMerchantChannel::getChannelName,
                ImMerchantChannel::getDialogRules)
            .eq(ImMerchantChannel::getMerchantId, merchantId)
            .eq(ImMerchantChannel::getMerchantChannelId, merchantChannelId)
        );
    }

    @Override
    public ImMerchantChannelVo queryAssignmentRules(Long merchantId, Long merchantChannelId) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(ImMerchantChannel.class)
            .select(ImMerchantChannel::getMerchantChannelId, ImMerchantChannel::getChannelName,
                ImMerchantChannel::getAssignmentRules)
            .eq(ImMerchantChannel::getMerchantId, merchantId)
            .eq(ImMerchantChannel::getMerchantChannelId, merchantChannelId)
        );
    }

    @Override
    public void dialogRules(Long merchantChannelId, ChannelDialogRulesDto channelDialogRulesDto) {
        baseMapper.update(new LambdaUpdateWrapper<>(ImMerchantChannel.class)
            .set(ImMerchantChannel::getDialogRules, JsonUtils.toJsonString(channelDialogRulesDto))
            .eq(ImMerchantChannel::getMerchantChannelId, merchantChannelId)
        );
        String cacheKey = "channel:dialog:rules:" + merchantChannelId;
        RedisUtils.deleteObject(cacheKey);
    }

    @Override
    public void assignmentRules(Long merchantChannelId, AssignmentRulesDto assignmentRulesDto) {
        baseMapper.update(new LambdaUpdateWrapper<>(ImMerchantChannel.class)
            .set(ImMerchantChannel::getAssignmentRules, JsonUtils.toJsonString(assignmentRulesDto))
            .eq(ImMerchantChannel::getMerchantChannelId, merchantChannelId)
        );
    }
}
