package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 商户管理员对象 im_merchant_administrator
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_merchant_administrator")
public class ImMerchantAdministrator extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID
     */
    @TableId(value = "admin_id")
    private Long adminId;

    /**
     * 管理员编码
     */
    private String adminCode;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 状态 1=正常 2=停用
     */
    private String status;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 成员分组ID
     */
    private Long memberGroupId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户编号
     */
    private String tenantId;


    /**
     * 消息提示设置
     */
    private String messagePrompt;


    /**
     * 在线状态 0->离线 1->在线
     */
    private String onlineStatus;

    /**
     * 顺序分配
     */
    private Integer sequenceSort;

    /**
     * 优先分配
     */
    private Integer prioritySort;


}
