package org.dromara.im.websocket.config;

import cn.hutool.core.util.StrUtil;
import org.dromara.common.websocket.config.properties.WebSocketProperties;
import org.dromara.im.websocket.handler.VisitorWebSocketHandler;
import org.dromara.im.websocket.interceptor.VisitorWebSocketInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * 游客WebSocket配置
 * 严格限制只处理游客身份的WebSocket连接，杜绝其他身份访问
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSocket
public class VisitorWebSocketConfig implements WebSocketConfigurer {

    private final VisitorWebSocketHandler visitorWebSocketHandler;
    private final WebSocketProperties webSocketProperties;

    public VisitorWebSocketConfig(VisitorWebSocketHandler visitorWebSocketHandler,
                                  WebSocketProperties webSocketProperties) {
        this.visitorWebSocketHandler = visitorWebSocketHandler;
        this.webSocketProperties = webSocketProperties;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 设置默认的跨域访问地址
        String allowedOrigins = webSocketProperties.getAllowedOrigins();
        if (StrUtil.isBlank(allowedOrigins)) {
            allowedOrigins = "*";
        }

        // 游客专用聊天端点
        // 使用场景：wss://im.example.com/visitor/chat?visitorId=xxx&businessId=1&channel=web
        registry.addHandler(visitorWebSocketHandler, "/visitor/chat")
            .addInterceptors(new VisitorWebSocketInterceptor())
            .setAllowedOrigins(allowedOrigins);
    }
}
