package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImChannelTranslate;

import java.io.Serial;
import java.io.Serializable;


/**
 * 渠道翻译设置视图对象 im_channel_translate
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImChannelTranslate.class)
public class ImChannelTranslateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道翻译id
     */
    @ExcelProperty(value = "渠道翻译id")
    private Long channelTranslateId;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    @ExcelProperty(value = "商户渠道ID")
    private Long merchantChannelId;

    /**
     * 开启翻译;0->关闭;1->开启
     */
    @ExcelProperty(value = "开启翻译;0->关闭;1->开启")
    private String openTranslate;

    /**
     * 客服语言
     */
    @ExcelProperty(value = "客服语言")
    private String customerLanguage;

    /**
     * 用户语言
     */
    @ExcelProperty(value = "用户语言")
    private String userLanguage;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


}
