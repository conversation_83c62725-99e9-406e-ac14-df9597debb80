package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImCustomLabelBo;
import org.dromara.im.domain.vo.ImCustomLabelVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户标签Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-12
 */
public interface IImCustomLabelService {

    /**
     * 查询用户标签
     *
     * @param customLabelId 主键
     * @return 用户标签
     */
    ImCustomLabelVo queryById(Long customLabelId);

    List<ImCustomLabelVo> queryByIds(List<Long> customLabelId);

    /**
     * 分页查询用户标签列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户标签分页列表
     */
    TableDataInfo<ImCustomLabelVo> queryPageList(ImCustomLabelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户标签列表
     *
     * @param bo 查询条件
     * @return 用户标签列表
     */
    List<ImCustomLabelVo> queryList(ImCustomLabelBo bo);

    /**
     * 新增用户标签
     *
     * @param bo 用户标签
     * @return 是否新增成功
     */
    Boolean insertByBo(ImCustomLabelBo bo);

    /**
     * 修改用户标签
     *
     * @param bo 用户标签
     * @return 是否修改成功
     */
    Boolean updateByBo(ImCustomLabelBo bo);

    /**
     * 校验并批量删除用户标签信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
