package org.dromara.im.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImMerchantChannelService;
import org.dromara.im.service.IImMessageService;
import org.dromara.im.utils.message.PersonMessageUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "im.autoReply.rules.enabled", havingValue = "true", matchIfMissing = true)
public class AutoReplyTask {

    private final IImConversationService conversationService;
    private final PersonMessageUtil personMessageUtil;

    /**
     * 批量检查忙碌自动回复
     * 每2分钟执行一次
     */
//    @Scheduled(fixedDelay = 120000)
    public void checkBusyAutoReply() {
        try {
            log.info("开始批量检查忙碌自动回复");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();
            if (activeConversations.isEmpty()) {
                log.info("没有活跃会话需要检查忙碌自动回复");
                return;
            }

            // 按商户分组处理，使用不同的配置
            Map<Long, List<ImConversationVo>> conversationsByMerchant = activeConversations.stream()
                .collect(Collectors.groupingBy(ImConversationVo::getMerchantId));

            int totalChecked = 0;
            for (Map.Entry<Long, List<ImConversationVo>> entry : conversationsByMerchant.entrySet()) {
                Long merchantId = entry.getKey();
                List<ImConversationVo> merchantConversations = entry.getValue();

                try {
                    // 批量检查该商户的会话
                    personMessageUtil.batchCheckBusyAutoReply(merchantConversations);
                    totalChecked += merchantConversations.size();

                } catch (Exception e) {
                    log.error("检查商户忙碌自动回复失败 - merchantId: {}", merchantId, e);
                }
            }

            log.info("批量检查忙碌自动回复完成 - 总检查数量: {}", totalChecked);

        } catch (Exception e) {
            log.error("批量检查忙碌自动回复失败", e);
        }
    }

    /**
     * 批量检查客户无回复提醒
     * 每3分钟执行一次
     */
//    @Scheduled(fixedDelay = 180000)
    public void checkCustomerNoReplyReminder() {
        try {
            log.info("开始批量检查客户无回复提醒");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();
            if (activeConversations.isEmpty()) {
                log.info("没有活跃会话需要检查客户无回复提醒");
                return;
            }

            // 按商户分组处理，使用不同的配置
            Map<Long, List<ImConversationVo>> conversationsByMerchant = activeConversations.stream()
                .collect(Collectors.groupingBy(ImConversationVo::getMerchantId));

            int totalChecked = 0;
            for (Map.Entry<Long, List<ImConversationVo>> entry : conversationsByMerchant.entrySet()) {
                Long merchantId = entry.getKey();
                List<ImConversationVo> merchantConversations = entry.getValue();

                try {
                    // 批量检查该商户的会话
                    personMessageUtil.batchCheckCustomerNoReplyReminder(merchantConversations);
                    totalChecked += merchantConversations.size();

                } catch (Exception e) {
                    log.error("检查商户客户无回复提醒失败 - merchantId: {}", merchantId, e);
                }
            }

            log.info("批量检查客户无回复提醒完成 - 总检查数量: {}", totalChecked);

        } catch (Exception e) {
            log.error("批量检查客户无回复提醒失败", e);
        }
    }
}
