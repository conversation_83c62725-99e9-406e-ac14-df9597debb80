package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImMemberGroupBo;
import org.dromara.im.domain.vo.ImMemberGroupVo;

import java.util.Collection;
import java.util.List;

/**
 * 成员分组Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
public interface IImMemberGroupService {

    /**
     * 查询成员分组
     *
     * @param memberGroupId 主键
     * @return 成员分组
     */
    ImMemberGroupVo queryById(Long memberGroupId);

    /**
     * 分页查询成员分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 成员分组分页列表
     */
    TableDataInfo<ImMemberGroupVo> queryPageList(ImMemberGroupBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的成员分组列表
     *
     * @param bo 查询条件
     * @return 成员分组列表
     */
    List<ImMemberGroupVo> queryList(ImMemberGroupBo bo);

    /**
     * 新增成员分组
     *
     * @param bo 成员分组
     * @return 是否新增成功
     */
    Boolean insertByBo(ImMemberGroupBo bo);

    /**
     * 修改成员分组
     *
     * @param bo 成员分组
     * @return 是否修改成功
     */
    Boolean updateByBo(ImMemberGroupBo bo);

    /**
     * 校验并批量删除成员分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
