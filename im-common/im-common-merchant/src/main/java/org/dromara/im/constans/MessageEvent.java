package org.dromara.im.constans;

import lombok.Getter;

@Getter
public enum MessageEvent {


    /**
     * 游客事件-->游客发送的
     */
    VISITOR__TO_SERVICE_EVENT("visitor_event"),

    /**
     * 客服事件-->客服发送的
     */
    SERVICE_TO_VISITOR_EVENT("service_event"),

    /**
     * 系统事件-->系统触发
     */
    SYSTEM_EVENT("system_event");

    private final String code;

    MessageEvent(String code) {
        this.code = code;
    }

}
