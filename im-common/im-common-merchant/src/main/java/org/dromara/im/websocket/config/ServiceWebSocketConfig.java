package org.dromara.im.websocket.config;

import cn.hutool.core.util.StrUtil;
import org.dromara.common.websocket.config.properties.WebSocketProperties;
import org.dromara.im.websocket.handler.ServiceWebSocketHandler;
import org.dromara.im.websocket.interceptor.ServiceWebSocketInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * 客服WebSocket配置
 * 严格限制只处理客服身份的WebSocket连接，杜绝其他身份访问
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSocket
public class ServiceWebSocketConfig implements WebSocketConfigurer {

    private final ServiceWebSocketHandler serviceWebSocketHandler;
    private final WebSocketProperties webSocketProperties;

    public ServiceWebSocketConfig(ServiceWebSocketHandler serviceWebSocketHandler,
                                  WebSocketProperties webSocketProperties) {
        this.serviceWebSocketHandler = serviceWebSocketHandler;
        this.webSocketProperties = webSocketProperties;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 如果允许跨域访问的地址为空，则设置为 "*"，表示允许所有来源的跨域请求
        if (StrUtil.isBlank(webSocketProperties.getAllowedOrigins())) {
            webSocketProperties.setAllowedOrigins("*");
        }


        // 客服专用工作台端点
        // 使用场景：wss://im.example.com/service/workspace?Authorization=Bearer token&businessId=1
        registry.addHandler(serviceWebSocketHandler, "/service/workspace")
            .addInterceptors(new ServiceWebSocketInterceptor())
            .setAllowedOrigins(webSocketProperties.getAllowedOrigins());
    }
}
