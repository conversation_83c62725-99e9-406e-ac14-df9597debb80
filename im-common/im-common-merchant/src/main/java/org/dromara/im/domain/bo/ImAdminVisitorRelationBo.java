package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImAdminVisitorRelation;

import java.util.List;

/**
 * 客服与访客服务关系业务对象 im_admin_visitor_relation
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImAdminVisitorRelation.class, reverseConvertGenerate = false)

public class ImAdminVisitorRelationBo extends BaseEntity {

    /**
     * 成员分组id
     */
    private Long adminVisitorRelationId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 客服ID
     */
    private Long adminId;

    /**
     * 访客ID
     */
    private String visitorId;


    private String visitorName;

    private List<String> visitorIdList;

    /**
     * 商户渠道ID
     */
    @NotNull(message = "商户渠道ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merchantChannelId;

    /**
     * 商户渠道
     */
    @NotBlank(message = "商户渠道不能为空", groups = {AddGroup.class, EditGroup.class})
    private String merchantChannelName;

    /**
     * 是否置顶
     */
    private String isTop;

    /**
     * 服务是否结束;0->否;1->已结束
     */
    @NotBlank(message = "服务是否结束;0->否;1->已结束不能为空", groups = {AddGroup.class, EditGroup.class})
    private String endService;

    /**
     * 是否拉黑 (0=否, 1=是)
     */
    private String isBlocked;

    /**
     * 聊天备注
     */
    @NotBlank(message = "聊天备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String chatRemark;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantId;

    /**
     * current=当前;history=历史
     */
    private String fromPage;


    private Long unreadCount;
}
