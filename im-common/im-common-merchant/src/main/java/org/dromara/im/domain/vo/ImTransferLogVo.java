package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImTransferLog;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 会话转接记录视图对象 im_transfer_log
 *
 * <AUTHOR> Li
 * @date 2025-07-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImTransferLog.class)
public class ImTransferLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会话ID
     */
    @ExcelProperty(value = "会话ID")
    private Long conversationId;

    /**
     * 原客服ID
     */
    @ExcelProperty(value = "原客服ID")
    private Long fromServiceId;

    /**
     * 目标客服ID
     */
    @ExcelProperty(value = "目标客服ID")
    private Long toServiceId;

    /**
     * 转接类型: manual=手动转接, auto=自动转接, timeout=超时转接
     */
    @ExcelProperty(value = "转接类型: manual=手动转接, auto=自动转接, timeout=超时转接")
    private String transferType;

    /**
     * 转接原因
     */
    @ExcelProperty(value = "转接原因")
    private String transferReason;

    /**
     * 操作人ID
     */
    @ExcelProperty(value = "操作人ID")
    private Long transferBy;

    /**
     * 转接状态: pending=待接收, accepted=已接收, rejected=已拒绝
     */
    @ExcelProperty(value = "转接状态: pending=待接收, accepted=已接收, rejected=已拒绝")
    private String status;

    /**
     * 处理时间
     */
    @ExcelProperty(value = "处理时间")
    private Date processedTime;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


}
