package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImConversation;

import java.util.Date;

/**
 * 会话关系业务对象 im_conversation
 *
 * <AUTHOR> Li
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImConversation.class,reverseConvertGenerate =false)

public class ImConversationBo extends BaseEntity {

    /**
     * 会话唯一id
     */
    private Long conversationId;

    /**
     * 游客ID
     */
    private String visitorId;

    /**
     * 游客姓名
     */
    private String visitorName;

    /**
     * 客服头像
     */
    private String visitorAvatar;

    /**
     * 客服ID
     */
    private Long adminId;

    /**
     * 客服名称
     */
    private String adminName;

    /**
     * 客服头像
     */
    private String adminAvatar;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 渠道ID
     */
    private Long merchantChannelId;

    /**
     * 会话状态: waiting=等待分配, active=进行中, closed=已结束, transferred=已转接
     */
    private String status;

    /**
     * 是否自动分配客服: 0=手动, 1=自动;3->转接
     */
    private String autoAssigned;

    /**
     * 满意度评分 1-5
     */
    private Long rating;

    /**
     * 结束原因
     */
    private String closedReason;

    /**
     * 游客反馈
     */
    private String feedback;

    /**
     * 最后消息时间
     */
    private Long lastMessageTime;

    /**
     * 最后一条消息内容（用于列表显示）
     */
    private String lastMessageContent;

    /**
     * 消息总数
     */
    private Long messageCount;

    /**
     * 未读消息数
     */
    private Long unreadCount;

    /**
     * 游客最后读取时间
     */
    private Date visitorReadTime;

    /**
     * 客服最后读取时间
     */
    private Date adminReadTime;

    /**
     * 租户编号
     */
    private String tenantId;


}
