package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 用户自动回复配置对象 im_admin_auto_reply
 *
 * <AUTHOR> Li
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_admin_auto_reply")
public class ImAdminAutoReply extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "admin_auto_reply_id")
    private Long adminAutoReplyId;

    /**
     * 用户id
     */
    private Long adminId;

    /**
     * 商户渠道id
     */
    private Long merchantChannelId;

    /**
     * 自动回复内容
     */
    private String autoReplyContext;

    /**
     * 租户编号
     */
    private String tenantId;


}
