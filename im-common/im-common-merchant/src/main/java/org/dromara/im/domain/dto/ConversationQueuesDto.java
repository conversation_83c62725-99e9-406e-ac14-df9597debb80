package org.dromara.im.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 对话排队配置DTO
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
public class ConversationQueuesDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否开启排队 0=关闭 1=开启
     */
    private String enableQueue;

    /**
     * 正在排队提示文案:顾客在排队时显示的文字 ${queue} 表示排队人数
     */
    private String queueWaitingMessage;

    /**
     * 排队满时提示文案
     */
    private String queueFullMessage;

    /**
     * 排队接待成功提示 0=关闭 1=开启
     */
    private String queueSuccessNotification;

    /**
     * 排队接待成功提示文案
     */
    private String queueSuccessMessage;

    public static ConversationQueuesDto getDefault() {
        ConversationQueuesDto queuesDto = new ConversationQueuesDto();
        queuesDto.setEnableQueue("0");
        queuesDto.setQueueWaitingMessage("抱歉，当前客服对话人数较多，请您耐心等待，您前面还有${queue} 人在等待。");
        queuesDto.setQueueFullMessage("非常抱歉，当前客服对话人数较多，请您稍后再试，");
        queuesDto.setQueueSuccessNotification("0");
        queuesDto.setQueueSuccessMessage("让您久等了，您已成功接入对话，客服正在为您提供服务，");
        return queuesDto;
    }
}
