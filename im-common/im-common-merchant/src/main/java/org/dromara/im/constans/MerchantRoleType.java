package org.dromara.im.constans;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 商户角色类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MerchantRoleType {

    /**
     * 商户主管理员 - 拥有所有权限
     */
    MERCHANT_ADMIN("1", "merchant_admin", "商户主管理员", Set.of(
        "merchant:*",           // 商户管理
        "admin:*",              // 管理员管理
        "service:*",            // 客服管理
        "customer:*",           // 客户管理
        "conversation:*",       // 对话管理
        "message:*",            // 消息管理
        "statistics:*",         // 统计分析
        "setting:*"             // 设置管理
    )),

    /**
     * 商户客服 - 基础客服权限
     */
    MERCHANT_SERVICE("3", "merchant_service", "商户客服", Set.of(
        "customer:view",        // 查看客户
        "customer:edit",        // 编辑客户信息
        "conversation:view",    // 查看对话
        "conversation:handle",  // 处理对话
        "message:send",         // 发送消息
        "message:view",         // 查看消息
        "service:profile"       // 个人设置
    ));

    private final String roleId;

    /**
     * 角色编码
     */
    private final String roleCode;

    /**
     * 角色名称
     */
    private final String roleName;

    /**
     * 权限集合
     */
    private final Set<String> permissions;

    /**
     * 根据角色编码获取角色类型
     */
    public static MerchantRoleType getByCode(String roleCode) {
        for (MerchantRoleType roleType : values()) {
            if (roleType.getRoleId().equals(roleCode)) {
                return roleType;
            }
        }
        throw new IllegalArgumentException("未知的角色类型: " + roleCode);
    }

    /**
     * 检查是否拥有指定权限
     */
    public boolean hasPermission(String permission) {
        // 检查完全匹配
        if (permissions.contains(permission)) {
            return true;
        }

        // 检查通配符权限
        for (String p : permissions) {
            if (p.endsWith("*")) {
                String prefix = p.substring(0, p.length() - 1);
                if (permission.startsWith(prefix)) {
                    return true;
                }
            }
        }

        return false;
    }
}
