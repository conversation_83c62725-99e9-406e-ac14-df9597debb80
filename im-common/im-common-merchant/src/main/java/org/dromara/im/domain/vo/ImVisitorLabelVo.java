package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImVisitorLabel;

import java.io.Serial;
import java.io.Serializable;


/**
 * 访客标签视图对象 im_visitor_label
 *
 * <AUTHOR> Li
 * @date 2025-07-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImVisitorLabel.class)

public class ImVisitorLabelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 游客标签id
     */
    @ExcelProperty(value = "游客标签id")
    private Long visitorLabelId;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 标签ID
     */
    @ExcelProperty(value = "标签ID")
    private Long customLabelId;

    /**
     * 访客ID
     */
    @ExcelProperty(value = "访客ID")
    private String visitorId;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    private String customLabelName;


}
