package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImAdminVisitorRelation;
import org.dromara.im.domain.bo.ImAdminVisitorRelationBo;
import org.dromara.im.domain.vo.ImAdminVisitorRelationVo;
import org.dromara.im.mapper.ImAdminVisitorRelationMapper;
import org.dromara.im.service.IImAdminVisitorRelationService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 客服与访客服务关系Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImAdminVisitorRelationServiceImpl implements IImAdminVisitorRelationService {

    private final ImAdminVisitorRelationMapper baseMapper;

    /**
     * 查询客服与访客服务关系
     *
     * @param adminVisitorRelationId 主键
     * @return 客服与访客服务关系
     */
    @Override
    public ImAdminVisitorRelationVo queryById(Long adminVisitorRelationId) {
        return baseMapper.selectVoById(adminVisitorRelationId);
    }

    /**
     * 分页查询客服与访客服务关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 客服与访客服务关系分页列表
     */
    @Override
    public TableDataInfo<ImAdminVisitorRelationVo> queryPageList(ImAdminVisitorRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImAdminVisitorRelation> lqw = buildQueryWrapper(bo);
        Page<ImAdminVisitorRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的客服与访客服务关系列表
     *
     * @param bo 查询条件
     * @return 客服与访客服务关系列表
     */
    @Override
    public List<ImAdminVisitorRelationVo> queryList(ImAdminVisitorRelationBo bo) {
        LambdaQueryWrapper<ImAdminVisitorRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImAdminVisitorRelation> buildQueryWrapper(ImAdminVisitorRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImAdminVisitorRelation> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ImAdminVisitorRelation::getAdminVisitorRelationId);
        lqw.eq(bo.getMerchantId() != null, ImAdminVisitorRelation::getMerchantId, bo.getMerchantId());
        lqw.eq(bo.getAdminId() != null, ImAdminVisitorRelation::getAdminId, bo.getAdminId());
        lqw.eq(StringUtils.isNotBlank(bo.getVisitorId()), ImAdminVisitorRelation::getVisitorId, bo.getVisitorId());
        lqw.in(bo.getVisitorIdList() != null, ImAdminVisitorRelation::getVisitorId, bo.getVisitorIdList());
        lqw.eq(bo.getMerchantChannelId() != null, ImAdminVisitorRelation::getMerchantChannelId, bo.getMerchantChannelId());
        lqw.eq(bo.getIsTop() != null, ImAdminVisitorRelation::getIsTop, bo.getIsTop());
        lqw.eq(bo.getIsBlocked() != null, ImAdminVisitorRelation::getIsBlocked, bo.getIsBlocked());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImAdminVisitorRelation::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getEndService()), ImAdminVisitorRelation::getEndService, bo.getEndService());
        return lqw;
    }

    /**
     * 新增客服与访客服务关系
     *
     * @param bo 客服与访客服务关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImAdminVisitorRelationBo bo) {
        ImAdminVisitorRelation add = MapstructUtils.convert(bo, ImAdminVisitorRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAdminVisitorRelationId(add.getAdminVisitorRelationId());
        }
        return flag;
    }

    /**
     * 修改客服与访客服务关系
     *
     * @param bo 客服与访客服务关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImAdminVisitorRelationBo bo) {
        ImAdminVisitorRelation update = MapstructUtils.convert(bo, ImAdminVisitorRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImAdminVisitorRelation entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除客服与访客服务关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void createOrUpdateGetAdminVisitorRelation(Long adminId, String visitorId, Long merchantId, Long merchantChannelId) {
        try {
            // 先查询是否已存在关系
            LambdaQueryWrapper<ImAdminVisitorRelation> queryWrapper = Wrappers.lambdaQuery(ImAdminVisitorRelation.class)
                .eq(ImAdminVisitorRelation::getAdminId, adminId)
                .eq(ImAdminVisitorRelation::getVisitorId, visitorId);

            ImAdminVisitorRelationVo existingRelation = baseMapper.selectVoOne(queryWrapper);

            if (existingRelation != null) {
                // 已存在则更新
                baseMapper.update(new LambdaUpdateWrapper<>(ImAdminVisitorRelation.class)
                    .set(ImAdminVisitorRelation::getEndService, "0")
                    .eq(ImAdminVisitorRelation::getAdminVisitorRelationId, existingRelation.getAdminVisitorRelationId())
                );
                log.info("客服访客关系已存在 - adminId: {}, visitorId: {}, relationId: {}", adminId, visitorId, existingRelation.getAdminVisitorRelationId());
                return;
            }

            // 不存在则创建新关系
            ImAdminVisitorRelationBo relationBo = new ImAdminVisitorRelationBo();
            relationBo.setAdminId(adminId);
            relationBo.setVisitorId(visitorId);
            relationBo.setMerchantId(merchantId);
            relationBo.setMerchantChannelId(merchantChannelId);
            relationBo.setIsTop("0");
            relationBo.setIsBlocked("0");
            relationBo.setEndService("0");

            boolean success = insertByBo(relationBo);

            if (success) {
                // 查询刚插入的记录并返回
                ImAdminVisitorRelationVo newRelation = baseMapper.selectVoOne(queryWrapper);
                log.info("创建客服访客关系成功 - adminId: {}, visitorId: {}, relationId: {}",
                    adminId, visitorId, newRelation != null ? newRelation.getAdminVisitorRelationId() : "unknown");
            } else {
                log.error("创建客服访客关系失败 - adminId: {}, visitorId: {}", adminId, visitorId);
            }

        } catch (Exception e) {
            log.error("创建或获取客服访客关系异常 - adminId: {}, visitorId: {}", adminId, visitorId, e);
        }
    }


    /**
     * 切换访客置顶状态
     */
    public Boolean toggleTopStatus(Long adminId, String visitorId) {
        // 查询当前状态
        ImAdminVisitorRelation relation = queryImAdminVisitorRelation(adminId, visitorId);
        if (relation == null) {
            return false;
        }

        // 切换状态：0->1 或 1->0
        Long newTopStatus = (relation.getIsTop() != null && relation.getIsTop().equals("1")) ? 0L : 1L;

        // 更新状态
        LambdaUpdateWrapper<ImAdminVisitorRelation> updateWrapper = Wrappers.lambdaUpdate(ImAdminVisitorRelation.class)
            .set(ImAdminVisitorRelation::getIsTop, newTopStatus)
            .eq(ImAdminVisitorRelation::getAdminId, adminId)
            .eq(ImAdminVisitorRelation::getVisitorId, visitorId)
//            .eq(ImAdminVisitorRelation::getIsDeleted, 0)
            ;

        return baseMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Boolean blockVisitor(Long adminId, String visitorId) {
        ImAdminVisitorRelation relation = queryImAdminVisitorRelation(adminId, visitorId);
        if (relation == null) {
            return false;
        }

        // 切换状态：0->1 或 1->0
        Long newTopStatus = (relation.getIsBlocked() != null && relation.getIsBlocked().equals("1")) ? 0L : 1L;

        // 更新状态
        LambdaUpdateWrapper<ImAdminVisitorRelation> updateWrapper = Wrappers.lambdaUpdate(ImAdminVisitorRelation.class)
            .set(ImAdminVisitorRelation::getIsBlocked, newTopStatus)
            .eq(ImAdminVisitorRelation::getAdminId, adminId)
            .eq(ImAdminVisitorRelation::getVisitorId, visitorId);

        return baseMapper.update(null, updateWrapper) > 0;
    }

    private ImAdminVisitorRelation queryImAdminVisitorRelation(Long adminId, String visitorId) {
        // 查询当前状态
        LambdaQueryWrapper<ImAdminVisitorRelation> queryWrapper = Wrappers.lambdaQuery(ImAdminVisitorRelation.class)
            .eq(ImAdminVisitorRelation::getAdminId, adminId)
            .eq(ImAdminVisitorRelation::getVisitorId, visitorId);

        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateEndService(Long merchantId, String visitorId, Long adminId) {
        baseMapper.update(Wrappers.lambdaUpdate(ImAdminVisitorRelation.class)
            .set(ImAdminVisitorRelation::getEndService, "1")
            .eq(ImAdminVisitorRelation::getAdminId, adminId)
            .eq(ImAdminVisitorRelation::getVisitorId, visitorId)
            .eq(ImAdminVisitorRelation::getMerchantId, merchantId)
        );
    }

    /**
     * 更新客服访客关系的备注信息
     */
    public Boolean updateRemark(Long adminId, String visitorId, String remark) {
        LambdaUpdateWrapper<ImAdminVisitorRelation> updateWrapper = Wrappers.lambdaUpdate(ImAdminVisitorRelation.class)
            .set(ImAdminVisitorRelation::getChatRemark, remark)
            .eq(ImAdminVisitorRelation::getAdminId, adminId)
            .eq(ImAdminVisitorRelation::getVisitorId, visitorId)
            ;

        return baseMapper.update(updateWrapper) > 0;
    }
}
