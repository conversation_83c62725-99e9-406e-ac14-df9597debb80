package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.domain.dto.ConversationQueuesDto;
import org.dromara.im.domain.vo.ImMerchantVo;

import java.util.Collection;
import java.util.List;

/**
 * 商户Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
public interface IImMerchantService {

    /**
     * 查询商户
     *
     * @param merchantId 主键
     * @return 商户
     */
    ImMerchantVo queryById(Long merchantId);

    /**
     * 分页查询商户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户分页列表
     */
    TableDataInfo<ImMerchantVo> queryPageList(ImMerchantBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商户列表
     *
     * @param bo 查询条件
     * @return 商户列表
     */
    List<ImMerchantVo> queryList(ImMerchantBo bo);

    /**
     * 新增商户
     *
     * @param bo 商户
     * @return 是否新增成功
     */
    Boolean insertByBo(ImMerchantBo bo);

    /**
     * 修改商户
     *
     * @param bo 商户
     * @return 是否修改成功
     */
    Boolean updateByBo(ImMerchantBo bo);

    /**
     * 校验并批量删除商户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void conversationQueues(Long merchantId, ConversationQueuesDto conversationQueuesDto);

    ConversationQueuesDto queryConversationQueues(Long merchantId);
}
