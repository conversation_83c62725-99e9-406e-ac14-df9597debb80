package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 团队快捷回复分组对象 im_quick_replies_group
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_quick_replies_group")
public class ImQuickRepliesGroup extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 快捷回复分组id
     */
    @TableId(value = "quick_replies_group_id")
    private Long quickRepliesGroupId;

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 快捷回复分组名称
     */
    private String quickRepliesGroupName;

    /**
     * 租户编号
     */
    private String tenantId;

    private String type;

}
