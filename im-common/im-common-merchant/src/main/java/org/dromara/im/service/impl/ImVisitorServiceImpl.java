package org.dromara.im.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImVisitor;
import org.dromara.im.domain.bo.ImVisitorBo;
import org.dromara.im.domain.vo.ImMerchantChannelVo;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.im.domain.vo.ImVisitorVo;
import org.dromara.im.mapper.ImMerchantChannelMapper;
import org.dromara.im.mapper.ImMerchantMapper;
import org.dromara.im.mapper.ImVisitorMapper;
import org.dromara.im.service.IImVisitorService;
import org.dromara.im.utils.UserAgentParser;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 游客访问Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ImVisitorServiceImpl implements IImVisitorService {

    private final ImVisitorMapper baseMapper;

    private final ImMerchantMapper merchantMapper;

    private final ImMerchantChannelMapper merchantChannelMapper;

    /**
     * 查询游客
     */
    @Override
    public ImVisitorVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据游客ID和商户ID查询游客
     */
    @Override
    public ImVisitorVo queryByVisitorIdAndBusinessId(String visitorId, Long merchantId) {
        LambdaQueryWrapper<ImVisitor> lqw = Wrappers.lambdaQuery();
        lqw.eq(ImVisitor::getVisitorId, visitorId);
        lqw.eq(ImVisitor::getMerchantId, merchantId);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 查询游客列表
     */
    @Override
    public TableDataInfo<ImVisitorVo> queryPageList(ImVisitorBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImVisitor> lqw = buildQueryWrapper(bo);
        Page<ImVisitorVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询游客列表
     */
    @Override
    public List<ImVisitorVo> queryList(ImVisitorBo bo) {
        LambdaQueryWrapper<ImVisitor> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImVisitor> buildQueryWrapper(ImVisitorBo bo) {
        LambdaQueryWrapper<ImVisitor> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMerchantId() != null, ImVisitor::getMerchantId, bo.getMerchantId());
        lqw.eq(StringUtils.isNotBlank(bo.getVisitorId()), ImVisitor::getVisitorId, bo.getVisitorId());
        lqw.eq(Objects.nonNull(bo.getMerchantChannelId()), ImVisitor::getMerchantChannelId, bo.getMerchantChannelId());
        lqw.like(StringUtils.isNotBlank(bo.getVisitorName()), ImVisitor::getVisitorName, bo.getVisitorName());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), ImVisitor::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getIpAddress()), ImVisitor::getIpAddress, bo.getIpAddress());
        lqw.eq(bo.getOnlineStatus() != null, ImVisitor::getOnlineStatus, bo.getOnlineStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getFingerprint()), ImVisitor::getFingerprint, bo.getFingerprint());
        lqw.in(CollectionUtils.isNotEmpty(bo.getVisitorIdList()), ImVisitor::getVisitorId, bo.getVisitorIdList());
        return lqw;
    }

    /**
     * 新增游客
     */
    @Override
    public Boolean insertByBo(ImVisitorBo bo) {
        ImVisitor add = MapstructUtils.convert(bo, ImVisitor.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改游客
     */
    @Override
    public Boolean updateByBo(ImVisitorBo bo) {
        ImVisitor update = MapstructUtils.convert(bo, ImVisitor.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImVisitor entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除游客
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 游客连接处理（基于PHP的notice方法）
     * 支持通过指纹识别游客身份
     */
    @Override
    public ImVisitorVo handleVisitorConnection(String visitorId, Long merchantId, String visitorName,
                                               String ipAddress, String fromUrl, String userAgent,
                                               String avatarUrl, Long channel, String language, String fingerprint) {

        // 1. 优先级处理：visitorId > fingerprint > 生成新ID
        String finalVisitorId = visitorId;
        ImVisitorVo existingVisitor = null;

        if (StringUtils.isNotEmpty(finalVisitorId)) {
            // 有visitorId，直接查询
            existingVisitor = queryByVisitorIdAndBusinessId(finalVisitorId, merchantId);
            log.info("通过visitorId查询游客: {}, 结果: {}", finalVisitorId, existingVisitor != null ? "找到" : "未找到");
        } else if (StringUtils.isNotEmpty(fingerprint)) {
            // 没有visitorId但有fingerprint，通过指纹查询
            existingVisitor = findByFingerprintAndBusinessId(fingerprint, merchantId);
            if (existingVisitor != null) {
                finalVisitorId = existingVisitor.getVisitorId();
                log.info("通过指纹查询到现有游客: visitorId={}, fingerprint={}", finalVisitorId, fingerprint);
            } else {
                // 指纹没找到，生成新的visitorId
                finalVisitorId = generateVisitorId();
                log.info("指纹未找到现有游客，生成新的visitorId: {}, fingerprint: {}", finalVisitorId, fingerprint);
            }
        } else {
            // 既没有visitorId也没有fingerprint，生成新ID
            finalVisitorId = generateVisitorId();
            log.info("既没有visitorId也没有fingerprint，生成新的visitorId: {}", finalVisitorId);
        }

        // 2. 处理游客名称默认值（模拟PHP逻辑）
        String finalVisitorName = visitorName;
        if (StringUtils.isBlank(finalVisitorName)) {
            finalVisitorName = "Tourist" + finalVisitorId.substring(0, 8);
        }

        // 3. 生成渠道标识（模拟PHP逻辑）
        String finalChannel;
        if (Objects.isNull(channel)) {
            finalChannel = generateChannel(finalVisitorId, merchantId);
        } else {
            finalChannel = channel.toString();
        }

        if (existingVisitor != null) {
            // 老用户 - 更新信息
            ImVisitorBo updateBo = new ImVisitorBo();
            updateBo.setId(existingVisitor.getId());
            updateBo.setOnlineStatus("1"); // 在线

            // 更新基本信息
            if (StringUtils.isNotBlank(finalVisitorName)) {
                updateBo.setVisitorName(finalVisitorName);
            }
            if (StringUtils.isNotBlank(avatarUrl)) {
                updateBo.setAvatarUrl(avatarUrl);
            }
            if (StringUtils.isNotBlank(fromUrl)) {
                updateBo.setFromUrl(fromUrl);
            }
            if (StringUtils.isNotBlank(userAgent)) {
                updateBo.setUserAgent(userAgent);

                // 解析User-Agent并保存浏览器信息
                try {
                    Map<String, Object> browserInfo = UserAgentParser.parse(userAgent);
                    updateBo.setBrowserInfo(JSONUtil.toJsonStr(browserInfo));
                    log.info("更新游客浏览器信息: visitorId={}, browserInfo={}", finalVisitorId, browserInfo);
                } catch (Exception e) {
                    log.warn("解析User-Agent失败: {}", userAgent, e);
                }
            }
            if (StringUtils.isNotBlank(language)) {
                updateBo.setLanguage(language);
            }
            if (StringUtils.isNotBlank(finalChannel)) {
                updateBo.setChannel(finalChannel);
            }
            // 更新指纹信息（如果提供了新的指纹）
            if (StringUtils.isNotBlank(fingerprint)) {
                updateBo.setFingerprint(fingerprint);
            }

            updateByBo(updateBo);
            return queryById(existingVisitor.getId());
        } else {
            // 新用户 - 创建游客
            ImVisitorBo newBo = new ImVisitorBo();
            newBo.setVisitorId(finalVisitorId);
            // 处理商户
            ImMerchantVo imMerchantVo = merchantMapper.selectVoById(merchantId);
            if (imMerchantVo == null) {
                throw new RuntimeException("商户不存在");
            }
            newBo.setMerchantId(merchantId);
            newBo.setVisitorName(finalVisitorName);
            newBo.setIpAddress(ipAddress);
            newBo.setLocationInfo("中国-香港");
            newBo.setFromUrl(fromUrl);
            newBo.setUserAgent(userAgent);
            // 处理商户渠道ID
            Optional.ofNullable(channel).ifPresent(var -> {
                ImMerchantChannelVo merchantChannelVo = merchantChannelMapper.selectVoById(channel);
                Optional.ofNullable(merchantChannelVo)
                    .ifPresentOrElse(var2 -> newBo.setMerchantChannelId(var2.getMerchantChannelId()), () -> {
                            throw new RuntimeException("商户渠道不存在");
                        }
                    );

            });
            // 解析User-Agent并保存浏览器信息
            if (StringUtils.isNotBlank(userAgent)) {
                try {
                    Map<String, Object> browserInfo = UserAgentParser.parse(userAgent);
                    newBo.setBrowserInfo(JSONUtil.toJsonStr(browserInfo));
                    log.info("新游客浏览器信息解析: visitorId={}, browserInfo={}", finalVisitorId, browserInfo);
                } catch (Exception e) {
                    log.warn("解析User-Agent失败: {}", userAgent, e);
                }
            }

            newBo.setAvatarUrl(StringUtils.isNotBlank(avatarUrl) ? avatarUrl : "ufImgServer/neweggtest-mengjiala/20250723/fadd94704a3e4288aaf932dfbd15ecff.png");
            newBo.setChannel(finalChannel);
            newBo.setLanguage(StringUtils.isNotBlank(language) ? language : "zh-CN");
            newBo.setFingerprint(fingerprint); // 保存指纹
            newBo.setOnlineStatus("1"); // 在线


            insertByBo(newBo);
            return queryById(newBo.getId());
        }
    }

    /**
     * 游客离线处理
     */
    @Override
    public void handleVisitorOffline(String visitorId, Long merchantId) {
        ImVisitorVo visitor = queryByVisitorIdAndBusinessId(visitorId, merchantId);
        if (visitor != null) {
            ImVisitorBo updateBo = new ImVisitorBo();
            updateBo.setId(visitor.getId());
            updateBo.setOnlineStatus("0"); // 离线
            updateBo.setLastOnlineTime(System.currentTimeMillis());
            updateByBo(updateBo);
        }
    }

    /**
     * 更新游客消息时间
     */
    @Override
    public void updateVisitorMessageTime(String visitorId, Long businessId) {
        ImVisitorVo visitor = queryByVisitorIdAndBusinessId(visitorId, businessId);
        if (visitor != null) {
            ImVisitorBo updateBo = new ImVisitorBo();
            updateBo.setId(visitor.getId());
            updateBo.setUpdatedTime(new Date());
            updateByBo(updateBo);
        }
    }


    /**
     * 生成渠道标识（基于PHP的bin2hex逻辑）
     */
    private String generateChannel(String visitorId, Long businessId) {
        String channelStr = visitorId + "/" + businessId;
        return bytesToHex(channelStr.getBytes());
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }


    /**
     * 批量查询访客信息
     */
    @Override
    public List<ImVisitorVo> queryByVisitorIdsAndBusinessId(List<String> visitorIds, Long businessId) {
        try {
            if (visitorIds == null || visitorIds.isEmpty()) {
                return new ArrayList<>();
            }

            LambdaQueryWrapper<ImVisitor> wrapper = Wrappers.lambdaQuery(ImVisitor.class)
                .in(ImVisitor::getVisitorId, visitorIds)
                .eq(ImVisitor::getMerchantId, businessId);

            List<ImVisitorVo> visitorList = baseMapper.selectVoList(wrapper);

            log.info("批量查询访客信息成功 - 查询数量: {}, 返回数量: {}", visitorIds.size(), visitorList.size());
            return visitorList;

        } catch (Exception e) {
            log.error("批量查询访客信息异常 - visitorIds: {}, businessId: {}", visitorIds, businessId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public void handleVisitorOnlineStatus(String visitorId, Long merchantId) {
        ImVisitorVo visitor = queryByVisitorIdAndBusinessId(visitorId, merchantId);
        if (visitor != null) {
            ImVisitorBo updateBo = new ImVisitorBo();
            updateBo.setId(visitor.getId());
            updateBo.setOnlineStatus("1"); // 在线
            updateBo.setLastOnlineTime(System.currentTimeMillis());
            updateByBo(updateBo);
        }

    }

    /**
     * 根据指纹和商户ID查询游客
     */
    private ImVisitorVo findByFingerprintAndBusinessId(String fingerprint, Long businessId) {
        LambdaQueryWrapper<ImVisitor> lqw = Wrappers.lambdaQuery();
        lqw.eq(ImVisitor::getFingerprint, fingerprint);
        lqw.eq(ImVisitor::getMerchantId, businessId);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 生成游客ID（基于PHP的生成逻辑）
     */
    private String generateVisitorId() {
        // 基于PHP的生成逻辑：bin2hex(pack('N', time())).strtolower($common->rand(8))
        long timestamp = System.currentTimeMillis() / 1000;
        String timestampHex = Long.toHexString(timestamp);
        String randomStr = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return timestampHex + randomStr.toLowerCase();
    }

}
