package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImChannelConfigBo;
import org.dromara.im.domain.vo.ImChannelConfigVo;

import java.util.Collection;
import java.util.List;

/**
 * 渠道配置Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
public interface IImChannelConfigService {

    /**
     * 查询渠道配置
     *
     * @param channelConfigId 主键
     * @return 渠道配置
     */
    ImChannelConfigVo queryById(Long channelConfigId);

    /**
     * 分页查询渠道配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道配置分页列表
     */
    TableDataInfo<ImChannelConfigVo> queryPageList(ImChannelConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的渠道配置列表
     *
     * @param bo 查询条件
     * @return 渠道配置列表
     */
    List<ImChannelConfigVo> queryList(ImChannelConfigBo bo);

    /**
     * 新增渠道配置
     *
     * @param bo 渠道配置
     * @return 是否新增成功
     */
    Boolean insertByBo(ImChannelConfigBo bo);

    /**
     * 修改渠道配置
     *
     * @param bo 渠道配置
     * @return 是否修改成功
     */
    Boolean updateByBo(ImChannelConfigBo bo);

    /**
     * 校验并批量删除渠道配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
