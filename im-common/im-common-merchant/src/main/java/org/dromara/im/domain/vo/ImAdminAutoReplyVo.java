package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImAdminAutoReply;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户自动回复配置视图对象 im_admin_auto_reply
 *
 * <AUTHOR> Li
 * @date 2025-07-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImAdminAutoReply.class)
public class ImAdminAutoReplyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long adminAutoReplyId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long adminId;

    /**
     * 商户渠道id
     */
    @ExcelProperty(value = "商户渠道id")
    private Long merchantChannelId;

    /**
     * 自动回复内容
     */
    @ExcelProperty(value = "自动回复内容")
    private String autoReplyContext;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


}
