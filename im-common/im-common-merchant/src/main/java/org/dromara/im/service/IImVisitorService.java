package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImVisitorBo;
import org.dromara.im.domain.vo.ImVisitorVo;

import java.util.Collection;
import java.util.List;

/**
 * 游客访问Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IImVisitorService {

    /**
     * 查询游客
     *
     * @param id 主键
     * @return 游客
     */
    ImVisitorVo queryById(Long id);

    /**
     * 根据游客ID和商户ID查询游客
     *
     * @param visitorId  游客ID
     * @param businessId 商户ID
     * @return 游客
     */
    ImVisitorVo queryByVisitorIdAndBusinessId(String visitorId, Long businessId);

    /**
     * 分页查询游客列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 游客分页列表
     */
    TableDataInfo<ImVisitorVo> queryPageList(ImVisitorBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的游客列表
     *
     * @param bo 查询条件
     * @return 游客列表
     */
    List<ImVisitorVo> queryList(ImVisitorBo bo);

    /**
     * 新增游客
     *
     * @param bo 游客
     * @return 是否新增成功
     */
    Boolean insertByBo(ImVisitorBo bo);

    /**
     * 修改游客
     *
     * @param bo 游客
     * @return 是否修改成功
     */
    Boolean updateByBo(ImVisitorBo bo);

    /**
     * 校验并批量删除游客信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 游客连接处理（基于PHP的notice方法）
     * 支持通过指纹识别游客身份
     *
     * @param visitorId   游客ID
     * @param merchantId  商户ID
     * @param visitorName 游客名称
     * @param ipAddress   IP地址
     * @param fromUrl     来源URL
     * @param userAgent   用户代理
     * @param avatarUrl   头像URL
     * @param merchantChannelId     渠道Id
     * @param language    语言
     * @param fingerprint 浏览器指纹
     * @return 游客信息
     */
    ImVisitorVo handleVisitorConnection(String visitorId, Long merchantId, String visitorName,
                                        String ipAddress, String fromUrl, String userAgent,
                                        String avatarUrl, Long merchantChannelId, String language, String fingerprint);

    /**
     * 游客离线处理
     *
     * @param visitorId  游客ID
     * @param merchantId 商户ID
     */
    void handleVisitorOffline(String visitorId, Long merchantId);

    /**
     * 更新游客消息时间
     *
     * @param visitorId  游客ID
     * @param businessId 商户ID
     */
    void updateVisitorMessageTime(String visitorId, Long businessId);

    /**
     * 批量查询访客信息
     *
     * @param visitorIds 访客ID列表
     * @param businessId 商户ID
     * @return 访客信息列表
     */
    List<ImVisitorVo> queryByVisitorIdsAndBusinessId(List<String> visitorIds, Long businessId);

    void handleVisitorOnlineStatus(String visitorId, Long merchantId);
}
