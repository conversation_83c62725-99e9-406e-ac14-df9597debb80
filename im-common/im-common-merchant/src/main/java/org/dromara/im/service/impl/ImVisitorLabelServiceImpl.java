package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.spring.MybatisPlusApplicationContextAware;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImVisitorLabel;
import org.dromara.im.domain.bo.ImVisitorLabelBo;
import org.dromara.im.domain.vo.ImCustomLabelVo;
import org.dromara.im.domain.vo.ImVisitorLabelVo;
import org.dromara.im.mapper.ImCustomLabelMapper;
import org.dromara.im.mapper.ImVisitorLabelMapper;
import org.dromara.im.service.IImCustomLabelService;
import org.dromara.im.service.IImVisitorLabelService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 访客标签Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImVisitorLabelServiceImpl implements IImVisitorLabelService {

    private final ImVisitorLabelMapper baseMapper;

    private final ImCustomLabelMapper  customLabelMapper;
    private final MybatisPlusApplicationContextAware mybatisPlusSpringApplicationContextAware;

    /**
     * 查询访客标签
     *
     * @param visitorLabelId 主键
     * @return 访客标签
     */
    @Override
    public ImVisitorLabelVo queryById(Long visitorLabelId) {
        return baseMapper.selectVoById(visitorLabelId);
    }

    /**
     * 分页查询访客标签列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 访客标签分页列表
     */
    @Override
    public TableDataInfo<ImVisitorLabelVo> queryPageList(ImVisitorLabelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImVisitorLabel> lqw = buildQueryWrapper(bo);
        Page<ImVisitorLabelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的访客标签列表
     *
     * @param bo 查询条件
     * @return 访客标签列表
     */
    @Override
    public List<ImVisitorLabelVo> queryList(ImVisitorLabelBo bo) {
        LambdaQueryWrapper<ImVisitorLabel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImVisitorLabel> buildQueryWrapper(ImVisitorLabelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImVisitorLabel> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ImVisitorLabel::getVisitorLabelId);
        lqw.eq(bo.getMerchantId() != null, ImVisitorLabel::getMerchantId, bo.getMerchantId());
        lqw.eq(bo.getCustomLabelId() != null, ImVisitorLabel::getCustomLabelId, bo.getCustomLabelId());
        lqw.eq(StringUtils.isNotBlank(bo.getVisitorId()), ImVisitorLabel::getVisitorId, bo.getVisitorId());
        return lqw;
    }

    /**
     * 新增访客标签
     *
     * @param bo 访客标签
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImVisitorLabelBo bo) {
        ImVisitorLabel add = MapstructUtils.convert(bo, ImVisitorLabel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setVisitorLabelId(add.getVisitorLabelId());
        }
        return flag;
    }

    /**
     * 修改访客标签
     *
     * @param bo 访客标签
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImVisitorLabelBo bo) {
        ImVisitorLabel update = MapstructUtils.convert(bo, ImVisitorLabel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImVisitorLabel entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除访客标签信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量新增访客标签
     */
    @Override
    public Boolean insertBatch(List<ImVisitorLabelBo> bos) {
        try {
            if (bos == null || bos.isEmpty()) {
                return false;
            }

            baseMapper.delete(new LambdaQueryWrapper<>(ImVisitorLabel.class)
                .eq(ImVisitorLabel::getVisitorId, bos.get(0).getVisitorId())
                .eq(ImVisitorLabel::getMerchantId, bos.get(0).getMerchantId())
            );
            List<ImVisitorLabel> entities = new ArrayList<>();
            for (ImVisitorLabelBo bo : bos) {
                ImVisitorLabel entity = MapstructUtils.convert(bo, ImVisitorLabel.class);
                validEntityBeforeSave(entity);
                entities.add(entity);
            }

            return baseMapper.insertBatch(entities);
        } catch (Exception e) {
            log.error("批量新增访客标签失败", e);
            return false;
        }
    }

    @Override
    public List<ImVisitorLabelVo> queryVisitorLabels(List<String> visitorIds, Long merchantId) {
        LambdaQueryWrapper<ImVisitorLabel> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ImVisitorLabel::getVisitorId, visitorIds);
        List<ImVisitorLabelVo> imVisitorLabelVos = baseMapper.selectVoList(wrapper);

        if (CollectionUtils.isNotEmpty(imVisitorLabelVos)) {
            List<Long> customLabelIds = imVisitorLabelVos.stream()
                .map(ImVisitorLabelVo::getCustomLabelId)
                .distinct()
                .toList();
            List<ImCustomLabelVo> imCustomLabelVos = customLabelMapper.selectVoByIds(customLabelIds);
            Map<Long, ImCustomLabelVo> labelVoMap = imCustomLabelVos.stream()
                .collect(Collectors.toMap(ImCustomLabelVo::getCustomLabelId, Function.identity()));
            for (ImVisitorLabelVo imVisitorLabelVo : imVisitorLabelVos) {
                ImCustomLabelVo imCustomLabelVo = labelVoMap.get(imVisitorLabelVo.getCustomLabelId());
                if (imCustomLabelVo != null) {
                    imVisitorLabelVo.setCustomLabelName(imCustomLabelVo.getCustomLabelName());
                }
            }
        }
        return imVisitorLabelVos;
    }
}
