package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImMemberGroup;

/**
 * 成员分组业务对象 im_member_group
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImMemberGroup.class, reverseConvertGenerate = false)
public class ImMemberGroupBo extends BaseEntity {

    /**
     * 成员分组id
     */
    private Long memberGroupId;

    private Long merchantId;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String groupName;

    /**
     * webhook;1->tg;2->Lark;3->自定义
     */
    @NotBlank(message = "webhook不能为空", groups = {AddGroup.class, EditGroup.class})
    private String webhook;

    /**
     * webhookUrl
     */
    private String webhookUrl;


}
