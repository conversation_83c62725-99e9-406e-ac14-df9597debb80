package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImPaymentOrder;

import java.math.BigDecimal;

/**
 * 支付订单业务对象 im_payment_order
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImPaymentOrder.class, reverseConvertGenerate = false)
public class ImPaymentOrderBo extends BaseEntity {

    /**
     * 订单id
     */
    private Long paymentOrderId;

    /**
     * 商户ID
     */
    @NotNull(message = "商户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merchantId;


    private Long packageId;

    /**
     * 当前套餐ID（用于升级订单）
     */
    private Long currentPackageId;

    /**
     * 升级数量（用于翻译容量升级）
     */
    private Long upgradeQuantity;

    /**
     * 订单类型;0->商户订单;1->翻译订单
     */
    @NotBlank(message = "订单类型;0->商户订单;1->翻译订单不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderType;

    /**
     * 订单状态0->待支付;1->已支付;2->已失效
     */
    @NotBlank(message = "订单状态0->待支付;1->已支付;2->已失效不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderStatus;

    /**
     * 原价
     */
    @NotNull(message = "原价不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal originalPrice;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal paymentAmount;

    /**
     * 支付方式;0->USDT
     */
    @NotBlank(message = "支付方式;0->USDT不能为空", groups = {AddGroup.class, EditGroup.class})
    private String paymentMethod;

    /**
     * 支付详情
     */
    @NotBlank(message = "支付详情不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderDetail;

    /**
     * 扩展字段
     */
    @NotBlank(message = "扩展字段不能为空", groups = {AddGroup.class, EditGroup.class})
    private String extra;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantId;


}
