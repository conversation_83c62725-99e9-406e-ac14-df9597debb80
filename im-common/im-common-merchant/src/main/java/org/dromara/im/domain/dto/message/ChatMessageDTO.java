package org.dromara.im.domain.dto.message;

import lombok.Data;

@Data
public class ChatMessageDTO {
    /**
     * 消息类型标识，固定为 "CHAT_MESSAGE"
     */
    private String type;
    /**
     * 具体消息类型，固定为 "text"
     * @see
     */
    private String messageType = "text";
    /**
     * 文本消息内容，支持纯文本
     */
    private String content;

    /**
     * 图片宽度，单位：像素
     */
    private Integer imageWidth;
    /**
     * 图片高度，单位：像素
     */
    private Integer imageHeight;
    /**
     * 消息发送时间，ISO8601字符串，格式如 "2024-01-15T14:30:00.000Z"
     */
    private Long messageSendTime;
    /**
     * 客户端消息ID（可选），用于消息去重和状态跟踪
     */
    private String clientMsgId;

    /**
     * 访客ID
     */
    private String visitorId;

    /**
     * 访客名称
     */
    private String visitorName;

    /**
     * 访客头像
     */
    private String visitorAvatar;

    /**
     * 客服ID
     */
    private String serviceId;

    /**
     * 客服名称
     */
    private String serviceName;

    /**
     * 客服头像
     */
    private String serviceAvatar;

    /**
     * 消息方向（to_service/to_visitor）
     */
    private String direction;

    /**
     * 业务ID
     */
    private Long merchantId;

    /**
     * 消息状态（sent/delivered/read/failed）
     */
    private String status;


    public static ChatMessageDTO buildChat() {
        ChatMessageDTO chatMessageDTO = new ChatMessageDTO();
        chatMessageDTO.setMessageSendTime(System.currentTimeMillis());
        return chatMessageDTO;
    }

}
