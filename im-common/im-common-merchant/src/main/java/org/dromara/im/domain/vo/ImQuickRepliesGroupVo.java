package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImQuickRepliesGroup;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 团队快捷回复分组视图对象 im_quick_replies_group
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImQuickRepliesGroup.class)
public class ImQuickRepliesGroupVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 快捷回复分组id
     */
    @ExcelProperty(value = "快捷回复分组id")
    private Long quickRepliesGroupId;

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 快捷回复分组名称
     */
    private String quickRepliesGroupName;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    private Date createTime;

    private Date updateTime;

    /**
     * 0->个人;1->团队
     */
    private String type;

    /**
     * 回复列表
     */
    private List<ImQuickRepliesVo> repliesVoList;


}
