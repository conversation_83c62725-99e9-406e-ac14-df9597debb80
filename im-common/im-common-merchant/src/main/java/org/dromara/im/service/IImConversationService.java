package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImConversationBo;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;

import java.util.Collection;
import java.util.List;

/**
 * 会话关系Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IImConversationService {

    /**
     * 查询会话关系
     */
    ImConversationVo queryById(Long id);

    /**
     * 根据会话ID查询会话
     */
    ImConversationVo queryByConversationId(Long conversationId);

    /**
     * 根据游客ID和商户ID查询活跃会话
     */
    ImConversationVo queryActiveByVisitorAndBusiness(String visitorId, Long merchantId);

    /**
     * 查询会话关系列表
     */
    TableDataInfo<ImConversationVo> queryPageList(ImConversationBo bo, PageQuery pageQuery);

    /**
     * 查询会话关系列表
     */
    List<ImConversationVo> queryList(ImConversationBo bo);

    /**
     * 新增会话关系
     */
    Boolean insertByBo(ImConversationBo bo);

    /**
     * 修改会话关系
     */
    Boolean updateByBo(ImConversationBo bo);

    /**
     * 校验并批量删除会话关系信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * ====== 核心业务方法 ======
     */

    /**
     * 创建新会话（游客连接时调用）
     *
     * @param visitorId        游客ID
     * @param businessId       商户ID
     * @param visitorName      游客姓名
     * @param sourceChannel    来源渠道
     * @param visitorIp        游客IP
     * @param visitorUserAgent 游客浏览器信息
     * @return 会话信息
     */
    ImConversationVo createConversation(String visitorId, Long businessId, String visitorName,
                                        String sourceChannel, String visitorIp, String visitorUserAgent, Long merchantChannelId);

    /**
     * 分配客服到会话
     *
     * @param conversationId 会话ID
     * @param serviceId      客服ID (为null时自动分配)
     * @return 是否分配成功
     */
    Boolean assignServiceToConversation(Long conversationId, Long serviceId, String nickname);

    /**
     * 自动分配客服
     *
     * @param conversationId 会话ID
     * @param merchantId     商户ID
     * @return 分配的客服ID，为null表示没有可用客服
     */
    ImMerchantAdministratorVo autoAssignService(Long conversationId, Long merchantId, Long merchantChannelId, Long adminId);

    /**
     * 转接会话到其他客服
     *
     * @param conversationId 会话ID
     * @param fromServiceId  原客服ID
     * @param toServiceId    目标客服ID
     * @param transferReason 转接原因
     * @return 是否转接成功
     */
    Boolean transferConversation(Long conversationId, Long fromServiceId, Long toServiceId, String transferReason);

    /**
     * 更新会话最后消息信息
     *
     * @param conversationId     会话ID
     * @param lastMessageContent 最后消息内容
     */
    void updateLastMessage(Long conversationId, String lastMessageContent);

    /**
     * 增加会话消息计数
     *
     * @param conversationId 会话ID
     */
    void incrementMessageCount(Long conversationId);

    /**
     * 查询访客与客服的最新会话
     *
     * @param visitorId  访客ID
     * @param adminId    客服ID
     * @param merchantId 商户ID
     * @return 最新会话记录
     */
    ImConversationVo queryLatestByVisitorAndAdmin(String visitorId, Long adminId, Long merchantId);

    /**
     * 批量查询访客与客服的最新会话
     *
     * @param visitorIds 访客ID列表
     * @param adminId    客服ID
     * @param merchantId 商户ID
     * @return 最新会话记录列表
     */
    List<ImConversationVo> queryLatestByVisitorIdsAndAdmin(List<String> visitorIds, Long adminId, Long merchantId);



    /**
     * 根据游客ID和客服ID获取会话ID（用于消息路由）
     *
     * @param visitorId  游客ID
     * @param serviceId  客服ID
     * @param merchantId 商户ID
     * @return 会话ID
     */
    Long getConversationId(String visitorId, Long serviceId, Long merchantId);


    Boolean closeConversationByVisitorIdAndAdminId(String visitorId, Long adminId);

    /**
     * 更新会话评价
     *
     * @param conversationId 会话ID
     * @param rating         评分
     * @param feedback       反馈内容
     * @return 是否更新成功
     */
    Boolean updateConversationRating(Long conversationId, Long rating, String feedback);

    void clearUnreadCount(Long adminId, String visitorId);

    /**
     * 统计等待分配的会话数量
     *
     * @param merchantId 商户ID
     * @return 等待会话数量
     */
    int countWaitingConversations(Long merchantId);

    /**
     * 查找访客的排队会话
     *
     * @param visitorId 访客ID
     * @param merchantId 商户ID
     * @return 排队中的会话，如果不存在返回null
     */
    ImConversationVo findQueuedConversation(String visitorId, Long merchantId);

    /**
     * 获取访客在排队中的位置
     *
     * @param visitorId 访客ID
     * @param merchantId 商户ID
     * @return 排队位置（从1开始）
     */
    int getQueuePosition(String visitorId, Long merchantId);

    /**
     * 创建等待状态的会话
     *
     * @param visitorId 访客ID
     * @param merchantId 商户ID
     * @param channelId 渠道ID
     * @return 创建的会话
     */
    ImConversationVo createConversationInWaitingState(String visitorId,String visitorName, Long merchantId, Long channelId);

    /**
     * 更新会话状态
     *
     * @param conversationId 会话ID
     * @param status         状态
     */
    void updateConversationStatus(Long conversationId, String status);

    /**
     * 更新会话分配的客服
     *
     * @param conversationId 会话ID
     * @param adminId 客服ID
     * @return 是否更新成功
     */
    boolean updateConversationAdmin(Long conversationId, Long adminId);

    /**
     * 获取所有有排队访客的商户ID列表
     *
     * @return 商户ID列表
     */
    List<Long> getMerchantsWithWaitingConversations();

    /**
     * 查找所有活跃会话（active状态）
     *
     * @return 活跃会话列表
     */
    List<ImConversationVo> findAllActiveConversations();

    /**
     * 查找所有等待会话（waiting状态）
     *
     * @return 等待会话列表
     */
    List<ImConversationVo> findWaitingConversations();
}
