package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImVisitor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 游客访问视图对象 im_visitor
 *
 * <AUTHOR> Li
 * @date 2025-07-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImVisitor.class)
public class ImVisitorVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 游客唯一标识
     */
    @ExcelProperty(value = "游客唯一标识")
    private String visitorId;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    @ExcelProperty(value = "商户渠道ID")
    private Long merchantChannelId;

    @ExcelProperty(value = "商户渠道名称")
    private String merchantChannelName;

    /**
     * 游客显示名称
     */
    @ExcelProperty(value = "游客显示名称")
    private String visitorName;

    /**
     * 头像URL
     */
    @ExcelProperty(value = "头像URL")
    private String avatarUrl;

    /**
     * 访问渠道标识
     */
    @ExcelProperty(value = "访问渠道标识")
    private String channel;

    /**
     * 访客IP地址(支持IPv6)
     */
    @ExcelProperty(value = "访客IP地址(支持IPv6)")
    private String ipAddress;

    /**
     * IP地理位置信息
     */
    @ExcelProperty(value = "IP地理位置信息")
    private String locationInfo;

    /**
     * 来源页面URL
     */
    @ExcelProperty(value = "来源页面URL")
    private String fromUrl;

    /**
     * 浏览器信息
     */
    @ExcelProperty(value = "浏览器信息")
    private String userAgent;

    /**
     * 浏览器详细信息JSON格式
     */
    @ExcelProperty(value = "浏览器详细信息JSON格式")
    private String browserInfo;

    /**
     * 客户端语言
     */
    @ExcelProperty(value = "客户端语言")
    private String language;

    /**
     * 时区
     */
    @ExcelProperty(value = "时区")
    private String timezone;

    /**
     * 状态: 0=离线, 1=在线
     */
    @ExcelProperty(value = "状态: 0=离线, 1=在线")
    private String onlineStatus;

    /**
     * 浏览器指纹
     */
    @ExcelProperty(value = "浏览器指纹")
    private String fingerprint;

    /**
     * 备注信息
     */
    @ExcelProperty(value = "备注信息")
    private String remark;

    /**
     * 最后在线时间
     */
    @ExcelProperty(value = "最后在线时间")
    private Long lastOnlineTime;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    private List<String> visitorLabelList;

    private Date createTime;

    private Date updatedTime;

    /**
     * 服务排序
     */
    private Integer serviceSort;

    /**
     * 优先级客服排序
     */
    private Integer priorityServiceSort;

    /**
     * 访客头像
     */
    private String visitorAvatar;

    public String getVisitorAvatar() {
        return avatarUrl;
    }
}
