package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 消息历史对象 im_message
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_message")
public class ImMessage extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 消息唯一标识
     */
    private String messageId;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 发送者ID
     */
    private String senderId;

    /**
     * 发送者姓名
     */
    private String senderName;

    /**
     * 发送者类型: visitor=游客, service=客服, system=系统
     */
    private String senderType;

    /**
     * 接收者ID
     */
    private String receiverId;

    /**
     * 接收者类型: visitor=游客, service=客服
     */
    private String receiverType;

    /**
     * 消息类型: text=文本,image=图片,system=系统消息
     */
    private String messageType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息状态: sent=已发送, delivered=已送达, read=已读, failed=发送失败
     */
    private String status;

    /**
     * 是否撤回: 0=否, 1=是
     */
    private Long isRecalled;

    /**
     * 撤回时间
     */
    private Long recallTime;

    /**
     * 读取时间
     */
    private Long readTime;

    /**
     * 客户端消息ID（用于去重）
     */
    private String clientMsgId;

    /**
     * 额外数据（JSON格式）
     */
    private String extraData;

    /**
     * 消息时间
     */
    private Long messageSendTime;

    /**
     * 租户编号
     */
    private String tenantId;


}
