package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImAdminAutoReply;

/**
 * 用户自动回复配置业务对象 im_admin_auto_reply
 *
 * <AUTHOR> Li
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImAdminAutoReply.class, reverseConvertGenerate = false)
public class ImAdminAutoReplyBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long adminAutoReplyId;

    /**
     * 用户id
     */
    private Long adminId;

    /**
     * 商户渠道id
     */
    private Long merchantChannelId;

    /**
     * 自动回复内容
     */
    @NotBlank(message = "自动回复内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String autoReplyContext;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantId;


}
