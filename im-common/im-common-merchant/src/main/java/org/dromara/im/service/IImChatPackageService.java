package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImChatPackageBo;
import org.dromara.im.domain.vo.ImChatPackageVo;

import java.util.Collection;
import java.util.List;

/**
 * 套餐Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
public interface IImChatPackageService {

    /**
     * 查询套餐
     *
     * @param id 主键
     * @return 套餐
     */
    ImChatPackageVo queryById(Long id);

    /**
     * 分页查询套餐列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 套餐分页列表
     */
    TableDataInfo<ImChatPackageVo> queryPageList(ImChatPackageBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的套餐列表
     *
     * @param bo 查询条件
     * @return 套餐列表
     */
    List<ImChatPackageVo> queryList(ImChatPackageBo bo);

    /**
     * 新增套餐
     *
     * @param bo 套餐
     * @return 是否新增成功
     */
    Boolean insertByBo(ImChatPackageBo bo);

    /**
     * 修改套餐
     *
     * @param bo 套餐
     * @return 是否修改成功
     */
    Boolean updateByBo(ImChatPackageBo bo);

    /**
     * 校验并批量删除套餐信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
