package org.dromara.im.utils;

import lombok.Getter;
import lombok.Setter;

import java.io.*;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class IpLocationUtil {


    /**
     * IP地址定位工具类
     * 基于17monipdb.dat数据库文件进行IP地址定位
     */
    private static final String DATABASE_FILE_PATH = "17monipdb.dat";
    private static final int INDEX_HEADER_LENGTH = 1024;

    private static RandomAccessFile databaseFile;
    private static ByteBuffer indexBuffer;
    private static int indexLength;
    private static final Map<String, LocationInfo> cache = new ConcurrentHashMap<>();
    private static boolean initialized = false;

    // 中国行政区划代码映射
    private static final Map<String, LocationCode> LOCATION_CODES = new HashMap<>();

    static {
        initializeLocationCodes();
    }

    /**
     * 位置信息类
     */
    @Setter
    @Getter
    public static class LocationInfo {
        // Getters and Setters
        private String country;
        private String province;
        private String city;
        private String isp;
        private String locationCode;

        public LocationInfo() {
        }

        public LocationInfo(String country, String province, String city, String isp, String locationCode) {
            this.country = country;
            this.province = province;
            this.city = city;
            this.isp = isp;
            this.locationCode = locationCode;
        }

        public static LocationInfo NA() {
            return new LocationInfo("N/A", "N/A", "N/A", "N/A", "");
        }

        @Override
        public String toString() {
            return String.format("LocationInfo{country='%s', province='%s', city='%s', isp='%s', locationCode='%s'}",
                country, province, city, isp, locationCode);
        }
    }

    /**
     * 位置编码类
     */
    private static class LocationCode {
        @Getter
        private final String code;
        private final Map<String, String> cityCodes;

        public LocationCode(String code) {
            this.code = code;
            this.cityCodes = new HashMap<>();
        }

        public void addCity(String cityName, String cityCode) {
            cityCodes.put(cityName, cityCode);
        }

        public String getCityCode(String cityName) {
            if (cityName == null || cityName.isEmpty()) {
                return null;
            }

            // 精确匹配
            String exactMatch = cityCodes.get(cityName);
            if (exactMatch != null) {
                return exactMatch;
            }

            // 模糊匹配
            for (Map.Entry<String, String> entry : cityCodes.entrySet()) {
                if (entry.getKey().contains(cityName) || cityName.contains(entry.getKey())) {
                    return entry.getValue();
                }
            }

            return null;
        }

    }

    /**
     * 初始化数据库
     */
    /**
     * 初始化数据库
     */
    public static synchronized void initialize(String databasePath) throws IOException {
        if (initialized) {
            return;
        }

        String dbPath = databasePath != null ? databasePath : DATABASE_FILE_PATH;
        File file = null;

        // 优先从resources目录加载
        try {
            InputStream inputStream = IpLocationUtil.class.getClassLoader().getResourceAsStream(dbPath);
            if (inputStream != null) {
                // 创建临时文件
                file = File.createTempFile("17monipdb", ".dat");
                file.deleteOnExit();

                try (FileOutputStream fos = new FileOutputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
            }
        } catch (Exception e) {
            // 如果从resources加载失败，尝试从文件系统加载
        }

        // 如果从resources加载失败，尝试从文件系统加载
        if (file == null || !file.exists()) {
            file = new File(dbPath);
        }

        if (!file.exists()) {
            throw new IOException("数据库文件不存在: " + dbPath);
        }

        databaseFile = new RandomAccessFile(file, "r");

        // 读取索引长度
        byte[] offsetBytes = new byte[4];
        databaseFile.read(offsetBytes);
        indexLength = ByteBuffer.wrap(offsetBytes).order(ByteOrder.LITTLE_ENDIAN).getInt();

        if (indexLength < 4) {
            throw new IOException("无效的数据库文件");
        }

        // 读取索引数据
        byte[] indexBytes = new byte[indexLength - 4];
        databaseFile.read(indexBytes);
        indexBuffer = ByteBuffer.wrap(indexBytes).order(ByteOrder.LITTLE_ENDIAN);

        initialized = true;
    }

    /**
     * 查找IP地址对应的地理位置信息
     */
    public static LocationInfo findLocation(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return LocationInfo.NA();
        }

        try {
            // 确保已初始化
            if (!initialized) {
                initialize(null);
            }

            // 解析IP地址
            String resolvedIp = InetAddress.getByName(ip).getHostAddress();

            // 检查缓存
            if (cache.containsKey(resolvedIp)) {
                return cache.get(resolvedIp);
            }

            // 查找位置信息
            LocationInfo locationInfo = lookupLocation(resolvedIp);

            // 缓存结果
            cache.put(resolvedIp, locationInfo);

            return locationInfo;

        } catch (Exception e) {
            return LocationInfo.NA();
        }
    }

    /**
     * 检查IP地址所属国家
     */
    public static String checkCountry(String ip) {
        LocationInfo locationInfo = findLocation(ip);
        return locationInfo.getCountry() != null ? locationInfo.getCountry() : "N/A";
    }

    /**
     * 获取位置编码
     */
    public static String getLocationCode(String province, String city) {
        LocationCode locationCode = LOCATION_CODES.get(province);
        if (locationCode == null) {
            return "";
        }

        String code = locationCode.getCode();

        if (city != null && !city.isEmpty()) {
            String cityCode = locationCode.getCityCode(city);
            if (cityCode != null) {
                code = cityCode;
            }
        }

        return code;
    }

    /**
     * 清除缓存
     */
    public static void clearCache() {
        cache.clear();
    }

    /**
     * 获取缓存大小
     */
    public static int getCacheSize() {
        return cache.size();
    }

    /**
     * 关闭资源
     */
    public static void close() {
        if (databaseFile != null) {
            try {
                databaseFile.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
        cache.clear();
        initialized = false;
    }

    private static LocationInfo lookupLocation(String ip) throws IOException {
        String[] ipParts = ip.split("\\.");
        if (ipParts.length != 4) {
            return LocationInfo.NA();
        }

        // 验证IP地址范围
        for (String part : ipParts) {
            int value = Integer.parseInt(part);
            if (value < 0 || value > 255) {
                return LocationInfo.NA();
            }
        }

        // 将IP转换为长整型
        long ipLong = ipToLong(ip);

        // 计算索引偏移量
        int firstByte = Integer.parseInt(ipParts[0]);
        int tmpOffset = firstByte * 4;

        // 读取起始位置
        int start = readInt(indexBuffer, tmpOffset);

        // 二分查找
        int indexOffset = -1;
        int indexLength = 0;
        int maxCompLen = IpLocationUtil.indexLength - INDEX_HEADER_LENGTH - 4;

        for (int pos = start * 8 + INDEX_HEADER_LENGTH; pos < maxCompLen; pos += 8) {
            long currentIp = readLong(indexBuffer, pos);
            if (currentIp >= ipLong) {
                indexOffset = readInt(indexBuffer, pos + 4);
                indexLength = readByte(indexBuffer, pos + 7) & 0xFF;
                break;
            }
        }

        if (indexOffset == -1) {
            return LocationInfo.NA();
        }

        // 读取位置信息
        databaseFile.seek(IpLocationUtil.indexLength + indexOffset - INDEX_HEADER_LENGTH);
        byte[] locationBytes = new byte[indexLength];
        databaseFile.read(locationBytes);
        String locationStr = new String(locationBytes, "UTF-8");

        String[] locationParts = locationStr.split("\t");
        if (locationParts.length < 3) {
            return LocationInfo.NA();
        }

        // 构建位置信息
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCountry(locationParts[0]);
        locationInfo.setProvince(locationParts[1]);
        locationInfo.setCity(locationParts[2]);
        locationInfo.setIsp(locationParts.length > 3 ? locationParts[3] : "");

        // 计算位置编码
        String locationCode = getLocationCode(locationInfo.getProvince(), locationInfo.getCity());
        locationInfo.setLocationCode(locationCode);

        return locationInfo;
    }

    private static long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = result << 8 | Integer.parseInt(parts[i]);
        }
        return result;
    }

    private static int readInt(ByteBuffer buffer, int offset) {
        return buffer.getInt(offset);
    }

    private static long readLong(ByteBuffer buffer, int offset) {
        return buffer.getLong(offset);
    }

    private static byte readByte(ByteBuffer buffer, int offset) {
        return buffer.get(offset);
    }

    private static void initializeLocationCodes() {
        // 北京
        LOCATION_CODES.put("北京", new LocationCode("110000"));

        // 天津
        LOCATION_CODES.put("天津", new LocationCode("120000"));

        // 河北
        LocationCode hebei = new LocationCode("130000");
        hebei.addCity("石家庄市", "130100");
        hebei.addCity("唐山市", "130200");
        hebei.addCity("秦皇岛市", "130300");
        hebei.addCity("邯郸市", "130400");
        hebei.addCity("邢台市", "130500");
        hebei.addCity("保定市", "130600");
        hebei.addCity("张家口市", "130700");
        hebei.addCity("承德市", "130800");
        hebei.addCity("沧州市", "130900");
        hebei.addCity("廊坊市", "131000");
        hebei.addCity("衡水市", "131100");
        LOCATION_CODES.put("河北", hebei);

        // 山西
        LocationCode shanxi = new LocationCode("140000");
        shanxi.addCity("太原市", "140100");
        shanxi.addCity("大同市", "140200");
        shanxi.addCity("阳泉市", "140300");
        shanxi.addCity("长治市", "140400");
        shanxi.addCity("晋城市", "140500");
        shanxi.addCity("朔州市", "140600");
        shanxi.addCity("晋中市", "140700");
        shanxi.addCity("运城市", "140800");
        shanxi.addCity("忻州市", "140900");
        shanxi.addCity("临汾市", "141000");
        shanxi.addCity("吕梁市", "141100");
        LOCATION_CODES.put("山西", shanxi);

        // 内蒙古
        LocationCode neimenggu = new LocationCode("150000");
        neimenggu.addCity("呼和浩特市", "150100");
        neimenggu.addCity("包头市", "150200");
        neimenggu.addCity("乌海市", "150300");
        neimenggu.addCity("赤峰市", "150400");
        neimenggu.addCity("通辽市", "150500");
        neimenggu.addCity("鄂尔多斯市", "150600");
        neimenggu.addCity("呼伦贝尔市", "150700");
        neimenggu.addCity("巴彦淖尔市", "150800");
        neimenggu.addCity("乌兰察布市", "150900");
        neimenggu.addCity("兴安盟", "152200");
        neimenggu.addCity("锡林郭勒盟", "152500");
        neimenggu.addCity("阿拉善盟", "152900");
        LOCATION_CODES.put("内蒙古", neimenggu);

        // 辽宁
        LocationCode liaoning = new LocationCode("210000");
        liaoning.addCity("沈阳市", "210100");
        liaoning.addCity("大连市", "210200");
        liaoning.addCity("鞍山市", "210300");
        liaoning.addCity("抚顺市", "210400");
        liaoning.addCity("本溪市", "210500");
        liaoning.addCity("丹东市", "210600");
        liaoning.addCity("锦州市", "210700");
        liaoning.addCity("营口市", "210800");
        liaoning.addCity("阜新市", "210900");
        liaoning.addCity("辽阳市", "211000");
        liaoning.addCity("盘锦市", "211100");
        liaoning.addCity("铁岭市", "211200");
        liaoning.addCity("朝阳市", "211300");
        liaoning.addCity("葫芦岛市", "211400");
        LOCATION_CODES.put("辽宁", liaoning);

        // 吉林
        LocationCode jilin = new LocationCode("220000");
        jilin.addCity("长春市", "220100");
        jilin.addCity("吉林市", "220200");
        jilin.addCity("四平市", "220300");
        jilin.addCity("辽源市", "220400");
        jilin.addCity("通化市", "220500");
        jilin.addCity("白山市", "220600");
        jilin.addCity("松原市", "220700");
        jilin.addCity("白城市", "220800");
        jilin.addCity("延边朝鲜族自治州", "222400");
        LOCATION_CODES.put("吉林", jilin);

        // 黑龙江
        LocationCode heilongjiang = new LocationCode("230000");
        heilongjiang.addCity("哈尔滨市", "230100");
        heilongjiang.addCity("齐齐哈尔市", "230200");
        heilongjiang.addCity("鸡西市", "230300");
        heilongjiang.addCity("鹤岗市", "230400");
        heilongjiang.addCity("双鸭山市", "230500");
        heilongjiang.addCity("大庆市", "230600");
        heilongjiang.addCity("伊春市", "230700");
        heilongjiang.addCity("佳木斯市", "230800");
        heilongjiang.addCity("七台河市", "230900");
        heilongjiang.addCity("牡丹江市", "231000");
        heilongjiang.addCity("黑河市", "231100");
        heilongjiang.addCity("绥化市", "231200");
        heilongjiang.addCity("大兴安岭地区", "232700");
        LOCATION_CODES.put("黑龙江", heilongjiang);

        // 上海
        LOCATION_CODES.put("上海", new LocationCode("310000"));

        // 江苏
        LocationCode jiangsu = new LocationCode("320000");
        jiangsu.addCity("南京市", "320100");
        jiangsu.addCity("无锡市", "320200");
        jiangsu.addCity("徐州市", "320300");
        jiangsu.addCity("常州市", "320400");
        jiangsu.addCity("苏州市", "320500");
        jiangsu.addCity("南通市", "320600");
        jiangsu.addCity("连云港市", "320700");
        jiangsu.addCity("淮安市", "320800");
        jiangsu.addCity("盐城市", "320900");
        jiangsu.addCity("扬州市", "321000");
        jiangsu.addCity("镇江市", "321100");
        jiangsu.addCity("泰州市", "321200");
        jiangsu.addCity("宿迁市", "321300");
        LOCATION_CODES.put("江苏", jiangsu);

        // 浙江
        LocationCode zhejiang = new LocationCode("330000");
        zhejiang.addCity("杭州市", "330100");
        zhejiang.addCity("宁波市", "330200");
        zhejiang.addCity("温州市", "330300");
        zhejiang.addCity("嘉兴市", "330400");
        zhejiang.addCity("湖州市", "330500");
        zhejiang.addCity("绍兴市", "330600");
        zhejiang.addCity("金华市", "330700");
        zhejiang.addCity("衢州市", "330800");
        zhejiang.addCity("舟山市", "330900");
        zhejiang.addCity("台州市", "331000");
        zhejiang.addCity("丽水市", "331100");
        LOCATION_CODES.put("浙江", zhejiang);

        // 安徽
        LocationCode anhui = new LocationCode("340000");
        anhui.addCity("合肥市", "340100");
        anhui.addCity("芜湖市", "340200");
        anhui.addCity("蚌埠市", "340300");
        anhui.addCity("淮南市", "340400");
        anhui.addCity("马鞍山市", "340500");
        anhui.addCity("淮北市", "340600");
        anhui.addCity("铜陵市", "340700");
        anhui.addCity("安庆市", "340800");
        anhui.addCity("黄山市", "341000");
        anhui.addCity("滁州市", "341100");
        anhui.addCity("阜阳市", "341200");
        anhui.addCity("宿州市", "341300");
        anhui.addCity("巢湖市", "341400");
        anhui.addCity("六安市", "341500");
        anhui.addCity("亳州市", "341600");
        anhui.addCity("池州市", "341700");
        anhui.addCity("宣城市", "341800");
        LOCATION_CODES.put("安徽", anhui);

        // 福建
        LocationCode fujian = new LocationCode("350000");
        fujian.addCity("福州市", "350100");
        fujian.addCity("厦门市", "350200");
        fujian.addCity("莆田市", "350300");
        fujian.addCity("三明市", "350400");
        fujian.addCity("泉州市", "350500");
        fujian.addCity("漳州市", "350600");
        fujian.addCity("南平市", "350700");
        fujian.addCity("龙岩市", "350800");
        fujian.addCity("宁德市", "350900");
        LOCATION_CODES.put("福建", fujian);

        // 江西
        LocationCode jiangxi = new LocationCode("360000");
        jiangxi.addCity("南昌市", "360100");
        jiangxi.addCity("景德镇市", "360200");
        jiangxi.addCity("萍乡市", "360300");
        jiangxi.addCity("九江市", "360400");
        jiangxi.addCity("新余市", "360500");
        jiangxi.addCity("鹰潭市", "360600");
        jiangxi.addCity("赣州市", "360700");
        jiangxi.addCity("吉安市", "360800");
        jiangxi.addCity("宜春市", "360900");
        jiangxi.addCity("抚州市", "361000");
        jiangxi.addCity("上饶市", "361100");
        LOCATION_CODES.put("江西", jiangxi);

        // 山东
        LocationCode shandong = new LocationCode("370000");
        shandong.addCity("济南市", "370100");
        shandong.addCity("青岛市", "370200");
        shandong.addCity("淄博市", "370300");
        shandong.addCity("枣庄市", "370400");
        shandong.addCity("东营市", "370500");
        shandong.addCity("烟台市", "370600");
        shandong.addCity("潍坊市", "370700");
        shandong.addCity("济宁市", "370800");
        shandong.addCity("泰安市", "370900");
        shandong.addCity("威海市", "371000");
        shandong.addCity("日照市", "371100");
        shandong.addCity("莱芜市", "371200");
        shandong.addCity("临沂市", "371300");
        shandong.addCity("德州市", "371400");
        shandong.addCity("聊城市", "371500");
        shandong.addCity("滨州市", "371600");
        shandong.addCity("菏泽市", "371700");
        LOCATION_CODES.put("山东", shandong);

        // 河南
        LocationCode henan = new LocationCode("410000");
        henan.addCity("郑州市", "410100");
        henan.addCity("开封市", "410200");
        henan.addCity("洛阳市", "410300");
        henan.addCity("平顶山市", "410400");
        henan.addCity("安阳市", "410500");
        henan.addCity("鹤壁市", "410600");
        henan.addCity("新乡市", "410700");
        henan.addCity("焦作市", "410800");
        henan.addCity("濮阳市", "410900");
        henan.addCity("许昌市", "411000");
        henan.addCity("漯河市", "411100");
        henan.addCity("三门峡市", "411200");
        henan.addCity("南阳市", "411300");
        henan.addCity("商丘市", "411400");
        henan.addCity("信阳市", "411500");
        henan.addCity("周口市", "411600");
        henan.addCity("驻马店市", "411700");
        henan.addCity("济源市", "419001");
        LOCATION_CODES.put("河南", henan);

        // 湖北
        LocationCode hubei = new LocationCode("420000");
        hubei.addCity("武汉市", "420100");
        hubei.addCity("黄石市", "420200");
        hubei.addCity("十堰市", "420300");
        hubei.addCity("宜昌市", "420500");
        hubei.addCity("襄樊市", "420600");
        hubei.addCity("鄂州市", "420700");
        hubei.addCity("荆门市", "420800");
        hubei.addCity("孝感市", "420900");
        hubei.addCity("荆州市", "421000");
        hubei.addCity("黄冈市", "421100");
        hubei.addCity("咸宁市", "421200");
        hubei.addCity("随州市", "421300");
        hubei.addCity("恩施土家族苗族自治州", "422800");
        hubei.addCity("仙桃市", "429004");
        hubei.addCity("潜江市", "429005");
        hubei.addCity("天门市", "429006");
        hubei.addCity("神农架林区", "429021");
        LOCATION_CODES.put("湖北", hubei);

        // 湖南
        LocationCode hunan = new LocationCode("430000");
        hunan.addCity("长沙市", "430100");
        hunan.addCity("株洲市", "430200");
        hunan.addCity("湘潭市", "430300");
        hunan.addCity("衡阳市", "430400");
        hunan.addCity("邵阳市", "430500");
        hunan.addCity("岳阳市", "430600");
        hunan.addCity("常德市", "430700");
        hunan.addCity("张家界市", "430800");
        hunan.addCity("益阳市", "430900");
        hunan.addCity("郴州市", "431000");
        hunan.addCity("永州市", "431100");
        hunan.addCity("怀化市", "431200");
        hunan.addCity("娄底市", "431300");
        hunan.addCity("湘西土家族苗族自治州", "433100");
        LOCATION_CODES.put("湖南", hunan);

        // 广东
        LocationCode guangdong = new LocationCode("440000");
        guangdong.addCity("广州市", "440100");
        guangdong.addCity("韶关市", "440200");
        guangdong.addCity("深圳市", "440300");
        guangdong.addCity("珠海市", "440400");
        guangdong.addCity("汕头市", "440500");
        guangdong.addCity("佛山市", "440600");
        guangdong.addCity("江门市", "440700");
        guangdong.addCity("湛江市", "440800");
        guangdong.addCity("茂名市", "440900");
        guangdong.addCity("肇庆市", "441200");
        guangdong.addCity("惠州市", "441300");
        guangdong.addCity("梅州市", "441400");
        guangdong.addCity("汕尾市", "441500");
        guangdong.addCity("河源市", "441600");
        guangdong.addCity("阳江市", "441700");
        guangdong.addCity("清远市", "441800");
        guangdong.addCity("东莞市", "441900");
        guangdong.addCity("中山市", "442000");
        guangdong.addCity("潮州市", "445100");
        guangdong.addCity("揭阳市", "445200");
        guangdong.addCity("云浮市", "445300");
        LOCATION_CODES.put("广东", guangdong);

        // 广西
        LocationCode guangxi = new LocationCode("450000");
        guangxi.addCity("南宁市", "450100");
        guangxi.addCity("柳州市", "450200");
        guangxi.addCity("桂林市", "450300");
        guangxi.addCity("梧州市", "450400");
        guangxi.addCity("北海市", "450500");
        guangxi.addCity("防城港市", "450600");
        guangxi.addCity("钦州市", "450700");
        guangxi.addCity("贵港市", "450800");
        guangxi.addCity("玉林市", "450900");
        guangxi.addCity("百色市", "451000");
        guangxi.addCity("贺州市", "451100");
        guangxi.addCity("河池市", "451200");
        guangxi.addCity("来宾市", "451300");
        guangxi.addCity("崇左市", "451400");
        LOCATION_CODES.put("广西", guangxi);

        // 海南
        LocationCode hainan = new LocationCode("460000");
        hainan.addCity("海口市", "460100");
        hainan.addCity("三亚市", "460200");
        hainan.addCity("五指山市", "469001");
        hainan.addCity("琼海市", "469002");
        hainan.addCity("儋州市", "469003");
        hainan.addCity("文昌市", "469005");
        hainan.addCity("万宁市", "469006");
        hainan.addCity("东方市", "469007");
        hainan.addCity("定安县", "469021");
        hainan.addCity("屯昌县", "469022");
        hainan.addCity("澄迈县", "469023");
        hainan.addCity("临高县", "469024");
        hainan.addCity("白沙黎族自治县", "469025");
        hainan.addCity("昌江黎族自治县", "469026");
        hainan.addCity("乐东黎族自治县", "469027");
        hainan.addCity("陵水黎族自治县", "469028");
        hainan.addCity("保亭黎族苗族自治县", "469029");
        hainan.addCity("琼中黎族苗族自治县", "469030");
        hainan.addCity("西沙群岛", "469031");
        hainan.addCity("南沙群岛", "469032");
        hainan.addCity("中沙群岛的岛礁及其海域", "469033");
        LOCATION_CODES.put("海南", hainan);

        // 重庆
        LOCATION_CODES.put("重庆", new LocationCode("500000"));

        // 四川
        LocationCode sichuan = new LocationCode("510000");
        sichuan.addCity("成都市", "510100");
        sichuan.addCity("自贡市", "510300");
        sichuan.addCity("攀枝花市", "510400");
        sichuan.addCity("泸州市", "510500");
        sichuan.addCity("德阳市", "510600");
        sichuan.addCity("绵阳市", "510700");
        sichuan.addCity("广元市", "510800");
        sichuan.addCity("遂宁市", "510900");
        sichuan.addCity("内江市", "511000");
        sichuan.addCity("乐山市", "511100");
        sichuan.addCity("南充市", "511300");
        sichuan.addCity("眉山市", "511400");
        sichuan.addCity("宜宾市", "511500");
        sichuan.addCity("广安市", "511600");
        sichuan.addCity("达州市", "511700");
        sichuan.addCity("雅安市", "511800");
        sichuan.addCity("巴中市", "511900");
        sichuan.addCity("资阳市", "512000");
        sichuan.addCity("阿坝藏族羌族自治州", "513200");
        sichuan.addCity("甘孜藏族自治州", "513300");
        sichuan.addCity("凉山彝族自治州", "513400");
        LOCATION_CODES.put("四川", sichuan);

        // 贵州
        LocationCode guizhou = new LocationCode("520000");
        guizhou.addCity("贵阳市", "520100");
        guizhou.addCity("六盘水市", "520200");
        guizhou.addCity("遵义市", "520300");
        guizhou.addCity("安顺市", "520400");
        guizhou.addCity("铜仁地区", "522200");
        guizhou.addCity("黔西南布依族苗族自治州", "522300");
        guizhou.addCity("毕节地区", "522400");
        guizhou.addCity("黔东南苗族侗族自治州", "522600");
        guizhou.addCity("黔南布依族苗族自治州", "522700");
        LOCATION_CODES.put("贵州", guizhou);

        // 云南
        LocationCode yunnan = new LocationCode("530000");
        yunnan.addCity("昆明市", "530100");
        yunnan.addCity("曲靖市", "530300");
        yunnan.addCity("玉溪市", "530400");
        yunnan.addCity("保山市", "530500");
        yunnan.addCity("昭通市", "530600");
        yunnan.addCity("丽江市", "530700");
        yunnan.addCity("普洱市", "530800");
        yunnan.addCity("临沧市", "530900");
        yunnan.addCity("楚雄彝族自治州", "532300");
        yunnan.addCity("红河哈尼族彝族自治州", "532500");
        yunnan.addCity("文山壮族苗族自治州", "532600");
        yunnan.addCity("西双版纳傣族自治州", "532800");
        yunnan.addCity("大理白族自治州", "532900");
        yunnan.addCity("德宏傣族景颇族自治州", "533100");
        yunnan.addCity("怒江傈僳族自治州", "533300");
        yunnan.addCity("迪庆藏族自治州", "533400");
        LOCATION_CODES.put("云南", yunnan);

        // 西藏
        LocationCode xizang = new LocationCode("540000");
        xizang.addCity("拉萨市", "540100");
        xizang.addCity("昌都地区", "542100");
        xizang.addCity("山南地区", "542200");
        xizang.addCity("日喀则地区", "542300");
        xizang.addCity("那曲地区", "542400");
        xizang.addCity("阿里地区", "542500");
        xizang.addCity("林芝地区", "542600");
        LOCATION_CODES.put("西藏", xizang);

        // 陕西
        LocationCode shaanxi = new LocationCode("610000");
        shaanxi.addCity("西安市", "610100");
        shaanxi.addCity("铜川市", "610200");
        shaanxi.addCity("宝鸡市", "610300");
        shaanxi.addCity("咸阳市", "610400");
        shaanxi.addCity("渭南市", "610500");
        shaanxi.addCity("延安市", "610600");
        shaanxi.addCity("汉中市", "610700");
        shaanxi.addCity("榆林市", "610800");
        shaanxi.addCity("安康市", "610900");
        shaanxi.addCity("商洛市", "611000");
        LOCATION_CODES.put("陕西", shaanxi);

        // 甘肃
        LocationCode gansu = new LocationCode("620000");
        gansu.addCity("兰州市", "620100");
        gansu.addCity("嘉峪关市", "620200");
        gansu.addCity("金昌市", "620300");
        gansu.addCity("白银市", "620400");
        gansu.addCity("天水市", "620500");
        gansu.addCity("武威市", "620600");
        gansu.addCity("张掖市", "620700");
        gansu.addCity("平凉市", "620800");
        gansu.addCity("酒泉市", "620900");
        gansu.addCity("庆阳市", "621000");
        gansu.addCity("定西市", "621100");
        gansu.addCity("陇南市", "621200");
        gansu.addCity("临夏回族自治州", "622900");
        gansu.addCity("甘南藏族自治州", "623000");
        LOCATION_CODES.put("甘肃", gansu);

        // 青海
        LocationCode qinghai = new LocationCode("630000");
        qinghai.addCity("西宁市", "630100");
        qinghai.addCity("海东地区", "632100");
        qinghai.addCity("海北藏族自治州", "632200");
        qinghai.addCity("黄南藏族自治州", "632300");
        qinghai.addCity("海南藏族自治州", "632500");
        qinghai.addCity("果洛藏族自治州", "632600");
        qinghai.addCity("玉树藏族自治州", "632700");
        qinghai.addCity("海西蒙古族藏族自治州", "632800");
        LOCATION_CODES.put("青海", qinghai);

        // 宁夏
        LocationCode ningxia = new LocationCode("640000");
        ningxia.addCity("银川市", "640100");
        ningxia.addCity("石嘴山市", "640200");
        ningxia.addCity("吴忠市", "640300");
        ningxia.addCity("固原市", "640400");
        ningxia.addCity("中卫市", "640500");
        LOCATION_CODES.put("宁夏", ningxia);

        // 新疆
        LocationCode xinjiang = new LocationCode("650000");
        xinjiang.addCity("乌鲁木齐市", "650100");
        xinjiang.addCity("克拉玛依市", "650200");
        xinjiang.addCity("吐鲁番地区", "652100");
        xinjiang.addCity("哈密地区", "652200");
        xinjiang.addCity("昌吉回族自治州", "652300");
        xinjiang.addCity("博尔塔拉蒙古自治州", "652700");
        xinjiang.addCity("巴音郭楞蒙古自治州", "652800");
        xinjiang.addCity("阿克苏地区", "652900");
        xinjiang.addCity("克孜勒苏柯尔克孜自治州", "653000");
        xinjiang.addCity("喀什地区", "653100");
        xinjiang.addCity("和田地区", "653200");
        xinjiang.addCity("伊犁哈萨克自治州", "654000");
        xinjiang.addCity("塔城地区", "654200");
        xinjiang.addCity("阿勒泰地区", "654300");
        xinjiang.addCity("石河子市", "659001");
        xinjiang.addCity("阿拉尔市", "659002");
        xinjiang.addCity("图木舒克市", "659003");
        xinjiang.addCity("五家渠市", "659004");
        LOCATION_CODES.put("新疆", xinjiang);
    }
}
