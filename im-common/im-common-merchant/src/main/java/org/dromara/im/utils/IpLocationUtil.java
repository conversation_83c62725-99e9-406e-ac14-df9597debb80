package org.dromara.im.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@Slf4j
public class IpLocationUtil {

    private static final String DEFAULT_IP_DB_PATH = "17monipdb.dat";

    private static int offset;
    private static final int[] index = new int[256];
    private static ByteBuffer dataBuffer;
    private static ByteBuffer indexBuffer;
    private static boolean initialized = false;

    // 静态初始化块，在类加载时自动初始化
    static {
        try {
            load(DEFAULT_IP_DB_PATH);
        } catch (Exception e) {
            log.error("初始化IP数据库失败", e);
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        System.out.println(Arrays.toString(IpLocationUtil.find("*************")));
    }

    /**
     * 从classpath加载IP数据库文件
     *
     * @param filename 文件名，相对于classpath根目录
     */
    public static void load(String filename) {
        try {
            // 使用ClassPathResource获取classpath中的资源
            Resource resource = new ClassPathResource(filename);
            if (!resource.exists()) {
                log.error("IP数据库文件不存在: {}", filename);
                return;
            }

            // 从InputStream读取数据
            try (InputStream inputStream = resource.getInputStream()) {
                byte[] data = StreamUtils.copyToByteArray(inputStream);
                loadFromBytes(data);
                initialized = true;
                log.info("成功加载IP数据库: {}, 大小: {} bytes", filename, data.length);
            }
        } catch (IOException e) {
            log.error("加载IP数据库失败: {}", filename, e);
        }
    }

    /**
     * 从字节数组加载IP数据库
     *
     * @param data IP数据库的字节数据
     */
    private static void loadFromBytes(byte[] data) {
        try {
            dataBuffer = ByteBuffer.wrap(data);
            dataBuffer.position(0);
            int indexLength = dataBuffer.getInt();
            byte[] indexBytes = new byte[indexLength];
            dataBuffer.get(indexBytes, 0, indexLength - 4);
            indexBuffer = ByteBuffer.wrap(indexBytes);
            indexBuffer.order(ByteOrder.LITTLE_ENDIAN);
            offset = indexLength;

            int loop = 0;
            while (loop++ < 256) {
                index[loop - 1] = indexBuffer.getInt();
            }
            indexBuffer.order(ByteOrder.BIG_ENDIAN);
        } catch (Exception ex) {
            log.error("加载IP数据库字节数据失败", ex);
            throw new RuntimeException("IP数据库加载失败", ex);
        }
    }

    /**
     * 查找IP地址对应的地理位置信息
     *
     * @param ip IP地址字符串
     * @return 地理位置信息数组，包含国家、省份、城市等信息
     */
    public static String[] find(String ip) {
        if (!initialized) {
            log.warn("IP数据库未初始化，尝试重新加载");
            load(DEFAULT_IP_DB_PATH);
            if (!initialized) {
                log.error("IP数据库初始化失败，返回空结果");
                return new String[]{"未知", "未知", "未知", "未知"};
            }
        }

        try {
            int ip_prefix_value = Integer.parseInt(ip.substring(0, ip.indexOf(".")));
            long ip2long_value = ip2long(ip);
            int start = index[ip_prefix_value];
            int max_comp_len = offset - 1028;
            long index_offset = -1;
            int index_length = -1;
            byte b = 0;

            for (start = start * 8 + 1024; start < max_comp_len; start += 8) {
                if (int2long(indexBuffer.getInt(start)) >= ip2long_value) {
                    index_offset = bytesToLong(b, indexBuffer.get(start + 6), indexBuffer.get(start + 5), indexBuffer.get(start + 4));
                    index_length = 0xFF & indexBuffer.get(start + 7);
                    break;
                }
            }

            if (index_offset == -1) {
                return new String[]{"未知", "未知", "未知", "未知"};
            }

            byte[] areaBytes;
            synchronized (IpLocationUtil.class) {
                dataBuffer.position(offset + (int) index_offset - 1024);
                areaBytes = new byte[index_length];
                dataBuffer.get(areaBytes, 0, index_length);
            }

            return new String(areaBytes, StandardCharsets.UTF_8).split("\t", -1);
        } catch (Exception e) {
            log.error("查找IP地址失败: {}", ip, e);
            return new String[]{"未知", "未知", "未知", "未知"};
        }
    }



    private static long bytesToLong(byte a, byte b, byte c, byte d) {
        return int2long((((a & 0xff) << 24) | ((b & 0xff) << 16) | ((c & 0xff) << 8) | (d & 0xff)));
    }

    private static int str2Ip(String ip) {
        String[] ss = ip.split("\\.");
        int a, b, c, d;
        a = Integer.parseInt(ss[0]);
        b = Integer.parseInt(ss[1]);
        c = Integer.parseInt(ss[2]);
        d = Integer.parseInt(ss[3]);
        return (a << 24) | (b << 16) | (c << 8) | d;
    }

    private static long ip2long(String ip) {
        return int2long(str2Ip(ip));
    }

    private static long int2long(int i) {
        long l = i & 0x7fffffffL;
        if (i < 0) {
            l |= 0x080000000L;
        }
        return l;
    }
}
