package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImMerchantChannelRelation;
import org.dromara.im.domain.bo.ImMerchantChannelRelationBo;
import org.dromara.im.domain.vo.ImMerchantChannelRelationVo;
import org.dromara.im.mapper.ImMerchantChannelRelationMapper;
import org.dromara.im.service.IImMerchantChannelRelationService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 渠道用户关系Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImMerchantChannelRelationServiceImpl implements IImMerchantChannelRelationService {

    private final ImMerchantChannelRelationMapper baseMapper;

    /**
     * 查询渠道用户关系
     *
     * @param merchantChannelRelationId 主键
     * @return 渠道用户关系
     */
    @Override
    public ImMerchantChannelRelationVo queryById(Long merchantChannelRelationId) {
        return baseMapper.selectVoById(merchantChannelRelationId);
    }

    /**
     * 分页查询渠道用户关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道用户关系分页列表
     */
    @Override
    public TableDataInfo<ImMerchantChannelRelationVo> queryPageList(ImMerchantChannelRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMerchantChannelRelation> lqw = buildQueryWrapper(bo);
        Page<ImMerchantChannelRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的渠道用户关系列表
     *
     * @param bo 查询条件
     * @return 渠道用户关系列表
     */
    @Override
    public List<ImMerchantChannelRelationVo> queryList(ImMerchantChannelRelationBo bo) {
        LambdaQueryWrapper<ImMerchantChannelRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMerchantChannelRelation> buildQueryWrapper(ImMerchantChannelRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMerchantChannelRelation> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImMerchantChannelRelation::getMerchantChannelRelationId);
        lqw.eq(bo.getAdminId() != null, ImMerchantChannelRelation::getAdminId, bo.getAdminId());
        lqw.eq(bo.getMerchantChannelId() != null, ImMerchantChannelRelation::getMerchantChannelId, bo.getMerchantChannelId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImMerchantChannelRelation::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增渠道用户关系
     *
     * @param bo 渠道用户关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMerchantChannelRelationBo bo) {
        ImMerchantChannelRelation add = MapstructUtils.convert(bo, ImMerchantChannelRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMerchantChannelRelationId(add.getMerchantChannelRelationId());
        }
        return flag;
    }

    /**
     * 修改渠道用户关系
     *
     * @param bo 渠道用户关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMerchantChannelRelationBo bo) {
        ImMerchantChannelRelation update = MapstructUtils.convert(bo, ImMerchantChannelRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMerchantChannelRelation entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除渠道用户关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
