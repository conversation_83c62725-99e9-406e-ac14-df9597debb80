package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 翻译套餐对象 im_translation_package
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_translation_package")
public class ImTranslationPackage extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @TableId(value = "translate_package_id")
    private Long translatePackageId;

    /**
     * 套餐编码
     */
    private String packageCode;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 字符配额（单位：万字符）
     */
    private Long characterQuota;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 货币类型 USDT=USDT CNY=人民币 USD=美元
     */
    private String currencyType;

    /**
     * 支付方式 USDT-TRC20=USDT-TRC20 USDT-ERC20=USDT-ERC20
     */
    private String paymentMethod;

    /**
     * 套餐类型 0=字符包 1=无限套餐
     */
    private String packageType;

    /**
     * 排序顺序
     */
    private Long sortOrder;

    /**
     * 状态(1-启用 0-禁用)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户编号
     */
    private String tenantId;


}
