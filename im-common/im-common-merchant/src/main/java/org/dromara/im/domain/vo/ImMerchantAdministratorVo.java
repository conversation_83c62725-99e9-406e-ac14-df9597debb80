package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.domain.dto.MessagePromptDto;
import org.dromara.im.constans.MerchantRoleType;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 商户管理员视图对象 im_merchant_administrator
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMerchantAdministrator.class)
public class ImMerchantAdministratorVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID
     */
    @ExcelProperty(value = "管理员ID")
    private Long adminId;

    /**
     * 管理员编码
     */
    @ExcelProperty(value = "管理员编码")
    private String adminCode;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     * 密码
     */
    @ExcelProperty(value = "密码")
    private String password;

    /**
     * 昵称
     */
    @ExcelProperty(value = "昵称")
    private String nickname;

    /**
     * 角色类型;1->主管理员;2->子管理员;3->客服
     */
    @ExcelProperty(value = "角色类型")
    private String roleType;

    /**
     * 状态 1=正常 2=停用
     */
    @ExcelProperty(value = "状态 1=正常 2=停用")
    private String status;

    /**
     * 最后登录时间
     */
    @ExcelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    /**
     * 成员分组ID
     */
    @ExcelProperty(value = "成员分组ID")
    private Long memberGroupId;

    private String memberGroupName;

    /**
     * 头像
     */
    @ExcelProperty(value = "头像")
    private String avatar;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 坐席数量
     */
    private Long seatsCount;

    /**
     * 对话上限
     */
    private Long chatNumber;

    /**
     * 渠道数量
     */
    private Long channelNumber;

    /**
     * 已接入渠道数量
     */
    private Long connectedChannelNumber;

    /**
     * 已使用量
     */
    private Long useTranslateNumber;

    /**
     * 总量
     */
    private Long translateNumber;


    /**
     * 渠道名称
     */
    private List<String> merchantChannelNameList;

    /**
     * 渠道id
     */
    private List<Long> merchantChannelIdList;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 过期时间
     */
    private Long expiredTime;

    /**
     * 0->体验版;1->标准版;2->专业版;3->集团版
     */
    private String packageType;

    /**
     * 消息提示设置
     */
    private String messagePrompt;

    /**
     * 消息提示
     */
    private MessagePromptDto messagePromptDto;

    /**
     * 顺序分配
     */
    private Integer sequenceSort;

    /**
     * 优先分配
     */
    private Integer prioritySort;

    /**
     * 最大并发游客数（客服专用）
     */
    private Integer maxConcurrentVisitors;


    private boolean packageUsed = false;


    public void buildMessagePrompt() {
        if (StringUtils.isNotEmpty(messagePrompt)) {
            this.messagePromptDto = JsonUtils.parseObject(messagePrompt, MessagePromptDto.class);
        } else {
            this.messagePromptDto = MessagePromptDto.getDefault();
        }
    }

    /**
     * 是否为主管理员
     */
    public boolean isMainAdmin() {
        return MerchantRoleType.MERCHANT_ADMIN.getRoleId().equals(this.roleType);
    }

    /**
     * 是否为客服
     */
    public boolean isService() {
        return MerchantRoleType.MERCHANT_SERVICE.getRoleId().equals(this.roleType);
    }



}
