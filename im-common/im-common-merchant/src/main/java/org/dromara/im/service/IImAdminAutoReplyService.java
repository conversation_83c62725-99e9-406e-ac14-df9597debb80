package org.dromara.im.service;

import org.apache.commons.lang3.tuple.Pair;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImAdminAutoReplyBo;
import org.dromara.im.domain.vo.ImAdminAutoReplyVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户自动回复配置Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-23
 */
public interface IImAdminAutoReplyService {

    /**
     * 查询用户自动回复配置
     *
     * @param adminnAutoReplyId 主键
     * @return 用户自动回复配置
     */
    ImAdminAutoReplyVo queryById(Long adminnAutoReplyId);

    /**
     * 分页查询用户自动回复配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户自动回复配置分页列表
     */
    TableDataInfo<ImAdminAutoReplyVo> queryPageList(ImAdminAutoReplyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户自动回复配置列表
     *
     * @param bo 查询条件
     * @return 用户自动回复配置列表
     */
    List<ImAdminAutoReplyVo> queryList(ImAdminAutoReplyBo bo);


    ImAdminAutoReplyVo queryOne(ImAdminAutoReplyBo bo);

    /**
     * 新增用户自动回复配置
     *
     * @param bo 用户自动回复配置
     * @return 是否新增成功
     */
    Boolean insertByBo(ImAdminAutoReplyBo bo);

    /**
     * 修改用户自动回复配置
     *
     * @param bo 用户自动回复配置
     * @return 是否修改成功
     */
    Boolean updateByBo(ImAdminAutoReplyBo bo);

    /**
     * 校验并批量删除用户自动回复配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 发送开始服务提示
     */
    Pair<Boolean, String> checkSendStartServiceWarn(Long adminId, Long merchantId, Long merchantChannelId);

    /**
     * 发送结束服务提示
     */
    Pair<Boolean, String> checkSendEndServiceWarn(Long adminId, Long merchantId, Long merchantChannelId);


    /**
     * 发送忙碌提示
     */
    Pair<Boolean, String> checkSendBusyServiceWarn(Long adminId, Long merchantId, Long merchantChannelId);


    /**
     * 发送未回复提示
     */
    Pair<Boolean, String> checkSendNoVisitorResponseWarn(Long adminId, Long merchantId, Long merchantChannelId);
}
