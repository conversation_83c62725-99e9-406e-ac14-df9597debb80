package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImQuickRepliesGroupBo;
import org.dromara.im.domain.vo.ImQuickRepliesGroupVo;

import java.util.Collection;
import java.util.List;

/**
 * 团队快捷回复分组Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
public interface IImQuickRepliesGroupService {

    /**
     * 查询团队快捷回复分组
     *
     * @param quickRepliesGroupId 主键
     * @return 团队快捷回复分组
     */
    ImQuickRepliesGroupVo queryById(Long quickRepliesGroupId);

    List<ImQuickRepliesGroupVo> queryByIds(List<Long> quickRepliesGroupId);

    /**
     * 分页查询团队快捷回复分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 团队快捷回复分组分页列表
     */
    TableDataInfo<ImQuickRepliesGroupVo> queryPageList(ImQuickRepliesGroupBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的团队快捷回复分组列表
     *
     * @param bo 查询条件
     * @return 团队快捷回复分组列表
     */
    List<ImQuickRepliesGroupVo> queryList(ImQuickRepliesGroupBo bo);

    /**
     * 新增团队快捷回复分组
     *
     * @param bo 团队快捷回复分组
     * @return 是否新增成功
     */
    Boolean insertByBo(ImQuickRepliesGroupBo bo);

    /**
     * 修改团队快捷回复分组
     *
     * @param bo 团队快捷回复分组
     * @return 是否修改成功
     */
    Boolean updateByBo(ImQuickRepliesGroupBo bo);

    /**
     * 校验并批量删除团队快捷回复分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
