package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImPaymentOrderBo;
import org.dromara.im.domain.dto.PayRequest;
import org.dromara.im.domain.vo.ImPaymentOrderVo;

import java.util.Collection;
import java.util.List;

/**
 * 支付订单Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
public interface IImPaymentOrderService {

    /**
     * 查询支付订单
     *
     * @param paymentOrderId 主键
     * @return 支付订单
     */
    ImPaymentOrderVo queryById(Long paymentOrderId);

    /**
     * 分页查询支付订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付订单分页列表
     */
    TableDataInfo<ImPaymentOrderVo> queryPageList(ImPaymentOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的支付订单列表
     *
     * @param bo 查询条件
     * @return 支付订单列表
     */
    List<ImPaymentOrderVo> queryList(ImPaymentOrderBo bo);

    /**
     * 新增支付订单
     *
     * @param bo 支付订单
     * @return 是否新增成功
     */
    Boolean insertByBo(ImPaymentOrderBo bo);

    /**
     * 修改支付订单
     *
     * @param bo 支付订单
     * @return 是否修改成功
     */
    Boolean updateByBo(ImPaymentOrderBo bo);

    /**
     * 校验并批量删除支付订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void create(PayRequest bo, Long merchantId);
}
