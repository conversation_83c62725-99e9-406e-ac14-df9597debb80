package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.im.domain.ImTranslationPackage;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 翻译套餐视图对象 im_translation_package
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImTranslationPackage.class)
public class ImTranslationPackageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @ExcelProperty(value = "套餐ID")
    private Long translatePackageId;

    /**
     * 套餐编码
     */
    @ExcelProperty(value = "套餐编码")
    private String packageCode;

    /**
     * 套餐名称
     */
    @ExcelProperty(value = "套餐名称")
    private String packageName;

    /**
     * 字符配额（单位：万字符）
     */
    @ExcelProperty(value = "字符配额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位：万字符")
    private Long characterQuota;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 价格
     */
    @ExcelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 货币类型 USDT=USDT CNY=人民币 USD=美元
     */
    @ExcelProperty(value = "货币类型 USDT=USDT CNY=人民币 USD=美元")
    private String currencyType;

    /**
     * 支付方式 USDT-TRC20=USDT-TRC20 USDT-ERC20=USDT-ERC20
     */
    @ExcelProperty(value = "支付方式 USDT-TRC20=USDT-TRC20 USDT-ERC20=USDT-ERC20")
    private String paymentMethod;

    /**
     * 套餐类型 0=字符包 1=无限套餐
     */
    @ExcelProperty(value = "套餐类型 0=字符包 1=无限套餐")
    private String packageType;

    /**
     * 排序顺序
     */
    @ExcelProperty(value = "排序顺序")
    private Long sortOrder;

    /**
     * 状态(1-启用 0-禁用)
     */
    @ExcelProperty(value = "状态(1-启用 0-禁用)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


}
