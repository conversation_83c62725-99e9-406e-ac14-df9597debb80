package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImChannelConfig;
import org.dromara.im.domain.bo.ImChannelConfigBo;
import org.dromara.im.domain.vo.ImChannelConfigVo;
import org.dromara.im.mapper.ImChannelConfigMapper;
import org.dromara.im.service.IImChannelConfigService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 渠道配置Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImChannelConfigServiceImpl implements IImChannelConfigService {

    private final ImChannelConfigMapper baseMapper;

    /**
     * 查询渠道配置
     *
     * @param channelConfigId 主键
     * @return 渠道配置
     */
    @Override
    public ImChannelConfigVo queryById(Long channelConfigId) {
        return baseMapper.selectVoById(channelConfigId);
    }

    /**
     * 分页查询渠道配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道配置分页列表
     */
    @Override
    public TableDataInfo<ImChannelConfigVo> queryPageList(ImChannelConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImChannelConfig> lqw = buildQueryWrapper(bo);
        Page<ImChannelConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的渠道配置列表
     *
     * @param bo 查询条件
     * @return 渠道配置列表
     */
    @Override
    public List<ImChannelConfigVo> queryList(ImChannelConfigBo bo) {
        LambdaQueryWrapper<ImChannelConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImChannelConfig> buildQueryWrapper(ImChannelConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImChannelConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImChannelConfig::getChannelConfigId);
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), ImChannelConfig::getChannelType, bo.getChannelType());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelCode()), ImChannelConfig::getChannelCode, bo.getChannelCode());
        lqw.like(StringUtils.isNotBlank(bo.getChannelName()), ImChannelConfig::getChannelName, bo.getChannelName());
        lqw.eq(bo.getCreatedAt() != null, ImChannelConfig::getCreatedAt, bo.getCreatedAt());
        lqw.eq(bo.getUpdatedAt() != null, ImChannelConfig::getUpdatedAt, bo.getUpdatedAt());
        return lqw;
    }

    /**
     * 新增渠道配置
     *
     * @param bo 渠道配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImChannelConfigBo bo) {
        ImChannelConfig add = MapstructUtils.convert(bo, ImChannelConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setChannelConfigId(add.getChannelConfigId());
        }
        return flag;
    }

    /**
     * 修改渠道配置
     *
     * @param bo 渠道配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImChannelConfigBo bo) {
        ImChannelConfig update = MapstructUtils.convert(bo, ImChannelConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImChannelConfig entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除渠道配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
