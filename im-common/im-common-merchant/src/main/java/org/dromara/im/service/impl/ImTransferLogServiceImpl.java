package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImTransferLog;
import org.dromara.im.domain.bo.ImTransferLogBo;
import org.dromara.im.domain.vo.ImTransferLogVo;
import org.dromara.im.mapper.ImTransferLogMapper;
import org.dromara.im.service.IImTransferLogService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 会话转接记录Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImTransferLogServiceImpl implements IImTransferLogService {

    private final ImTransferLogMapper baseMapper;

    /**
     * 查询会话转接记录
     *
     * @param id 主键
     * @return 会话转接记录
     */
    @Override
    public ImTransferLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询会话转接记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 会话转接记录分页列表
     */
    @Override
    public TableDataInfo<ImTransferLogVo> queryPageList(ImTransferLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImTransferLog> lqw = buildQueryWrapper(bo);
        Page<ImTransferLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的会话转接记录列表
     *
     * @param bo 查询条件
     * @return 会话转接记录列表
     */
    @Override
    public List<ImTransferLogVo> queryList(ImTransferLogBo bo) {
        LambdaQueryWrapper<ImTransferLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImTransferLog> buildQueryWrapper(ImTransferLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImTransferLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImTransferLog::getId);
        lqw.eq(Objects.nonNull(bo.getConversationId()), ImTransferLog::getConversationId, bo.getConversationId());
        lqw.eq(bo.getFromServiceId() != null, ImTransferLog::getFromServiceId, bo.getFromServiceId());
        lqw.eq(bo.getToServiceId() != null, ImTransferLog::getToServiceId, bo.getToServiceId());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferType()), ImTransferLog::getTransferType, bo.getTransferType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferReason()), ImTransferLog::getTransferReason, bo.getTransferReason());
        lqw.eq(bo.getTransferBy() != null, ImTransferLog::getTransferBy, bo.getTransferBy());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ImTransferLog::getStatus, bo.getStatus());
        lqw.eq(bo.getProcessedTime() != null, ImTransferLog::getProcessedTime, bo.getProcessedTime());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImTransferLog::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增会话转接记录
     *
     * @param bo 会话转接记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImTransferLogBo bo) {
        ImTransferLog add = MapstructUtils.convert(bo, ImTransferLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改会话转接记录
     *
     * @param bo 会话转接记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImTransferLogBo bo) {
        ImTransferLog update = MapstructUtils.convert(bo, ImTransferLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImTransferLog entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除会话转接记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
