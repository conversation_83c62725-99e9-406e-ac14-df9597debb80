package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 成员分组对象 im_member_group
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_member_group")
public class ImMemberGroup extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成员分组id
     */
    @TableId(value = "member_group_id")
    private Long memberGroupId;

    private Long merchantId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * webhook;1->tg;2->Lark;3->自定义
     */
    private String webhook;

    /**
     * webhookUrl
     */
    private String webhookUrl;

}
