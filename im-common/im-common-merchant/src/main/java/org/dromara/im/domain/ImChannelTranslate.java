package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 渠道翻译设置对象 im_channel_translate
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_channel_translate")
public class ImChannelTranslate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道翻译id
     */
    @TableId(value = "channel_translate_id")
    private Long channelTranslateId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * 开启翻译;0->关闭;1->开启
     */
    private String openTranslate;

    /**
     * 客服语言
     */
    private String customerLanguage;

    /**
     * 用户语言
     */
    private String userLanguage;

    /**
     * 租户编号
     */
    private String tenantId;


}
