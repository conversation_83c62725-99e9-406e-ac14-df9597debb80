package org.dromara.im.domain.vo;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.im.domain.ImMessage;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;


/**
 * 消息历史视图对象 im_message
 *
 * <AUTHOR> Li
 * @date 2025-07-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMessage.class)

public class ImMessageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 消息唯一标识
     */
    @ExcelProperty(value = "消息唯一标识")
    private String messageId;

    /**
     * 会话ID
     */
    @ExcelProperty(value = "会话ID")
    private Long conversationId;

    /**
     * 发送者ID
     */
    @ExcelProperty(value = "发送者ID")
    private String senderId;

    /**
     * 发送者姓名
     */
    @ExcelProperty(value = "发送者姓名")
    private String senderName;

    /**
     * 发送者类型: visitor=游客, service=客服, system=系统
     */
    @ExcelProperty(value = "发送者类型: visitor=游客, service=客服, system=系统")
    private String senderType;

    /**
     * 接收者ID
     */
    @ExcelProperty(value = "接收者ID")
    private String receiverId;

    /**
     * 接收者类型: visitor=游客, service=客服
     */
    @ExcelProperty(value = "接收者类型: visitor=游客, service=客服")
    private String receiverType;

    /**
     * 消息类型: text=文本,image=图片,system=系统消息
     */
    @ExcelProperty(value = "消息类型: text=文本,image=图片,system=系统消息")
    private String messageType;

    /**
     * 消息内容
     */
    @ExcelProperty(value = "消息内容")
    private String content;

    /**
     * 消息状态: sent=已发送, delivered=已送达, read=已读, failed=发送失败
     */
    @ExcelProperty(value = "消息状态: sent=已发送, delivered=已送达, read=已读, failed=发送失败")
    private String status;

    /**
     * 是否撤回: 0=否, 1=是
     */
    @ExcelProperty(value = "是否撤回: 0=否, 1=是")
    private Long isRecalled;

    /**
     * 撤回时间
     */
    @ExcelProperty(value = "撤回时间")
    private Long recallTime;

    /**
     * 读取时间
     */
    @ExcelProperty(value = "读取时间")
    private Long readTime;

    /**
     * 客户端消息ID（用于去重）
     */
    @ExcelProperty(value = "客户端消息ID", converter = ExcelDictConvert.class)
    private String clientMsgId;

    /**
     * 额外数据（JSON格式）
     */
    @ExcelProperty(value = "额外数据", converter = ExcelDictConvert.class)
    private String extraData;

    /**
     * 消息时间
     */
    @ExcelProperty(value = "消息时间")
    private Long messageSendTime;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


    private String imageWidth;

    private String imageHeight;

    public String getImageWidth() {
        if (StringUtils.isNotBlank(extraData)) {
            Map<String, Object> data = JsonUtils.parseMap(extraData);
            if (MapUtil.isNotEmpty(data) && data.containsKey("imageWidth")) {
                return (String) data.get("imageWidth");
            }
        }
        return imageWidth;
    }

    public String getImageHeight() {
        if (StringUtils.isNotBlank(extraData)) {
            Map<String, Object> data = JsonUtils.parseMap(extraData);
            if (MapUtil.isNotEmpty(data) && data.containsKey("imageHeight")) {
                return (String) data.get("imageHeight");
            }
        }
        return imageHeight;
    }
}
