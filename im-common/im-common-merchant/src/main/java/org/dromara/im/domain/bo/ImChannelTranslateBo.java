package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImChannelTranslate;

/**
 * 渠道翻译设置业务对象 im_channel_translate
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImChannelTranslate.class, reverseConvertGenerate = false)
public class ImChannelTranslateBo extends BaseEntity {

    /**
     * 渠道翻译id
     */
    private Long channelTranslateId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * 开启翻译;0->关闭;1->开启
     */
    @NotBlank(message = "开启翻译;0->关闭;1->开启不能为空", groups = {AddGroup.class, EditGroup.class})
    private String openTranslate;

    /**
     * 客服语言
     */
    @NotBlank(message = "客服语言不能为空", groups = {AddGroup.class, EditGroup.class})
    private String customerLanguage;

    /**
     * 用户语言
     */
    @NotBlank(message = "用户语言不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userLanguage;


}
