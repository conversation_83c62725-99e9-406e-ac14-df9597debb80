package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImMerchantAdministratorBo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;

import java.util.Collection;
import java.util.List;

/**
 * 商户管理员Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-07
 */
public interface IImMerchantAdministratorService {

    /**
     * 查询商户管理员
     *
     * @param adminId 主键
     * @return 商户管理员
     */
    ImMerchantAdministratorVo queryById(Long adminId);

    /**
     * 分页查询商户管理员列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户管理员分页列表
     */
    TableDataInfo<ImMerchantAdministratorVo> queryPageList(ImMerchantAdministratorBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商户管理员列表
     *
     * @param bo 查询条件
     * @return 商户管理员列表
     */
    List<ImMerchantAdministratorVo> queryList(ImMerchantAdministratorBo bo);

    /**
     * 新增商户管理员
     *
     * @param bo 商户管理员
     * @return 是否新增成功
     */
    Boolean insertByBo(ImMerchantAdministratorBo bo);

    /**
     * 修改商户管理员
     *
     * @param bo 商户管理员
     * @return 是否修改成功
     */
    Boolean updateByBo(ImMerchantAdministratorBo bo);

    /**
     * 校验并批量删除商户管理员信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<ImMerchantAdministratorVo> queryByIds(List<Long> adminIdList);

    /**
     * 根据商户ID获取客服列表
     *
     * @param businessId 商户ID
     * @return 客服列表
     */
    List<ImMerchantAdministratorVo> getServicesByBusinessId(Long businessId);

    void sortAdmin(ImMerchantAdministratorBo bo);
}
