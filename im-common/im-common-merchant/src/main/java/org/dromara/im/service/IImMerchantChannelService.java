package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImMerchantChannelBo;
import org.dromara.im.domain.dto.AssignmentRulesDto;
import org.dromara.im.domain.dto.ChannelDialogRulesDto;
import org.dromara.im.domain.vo.ImMerchantChannelVo;

import java.util.Collection;
import java.util.List;

/**
 * 商户接入渠道（聚合）Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
public interface IImMerchantChannelService {

    /**
     * 查询商户接入渠道（聚合）
     *
     * @param id 主键
     * @return 商户接入渠道（聚合）
     */
    ImMerchantChannelVo queryById(Long id);

    List<ImMerchantChannelVo> queryByIds(List<Long> ids);

    /**
     * 分页查询商户接入渠道（聚合）列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户接入渠道（聚合）分页列表
     */
    TableDataInfo<ImMerchantChannelVo> queryPageList(ImMerchantChannelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商户接入渠道（聚合）列表
     *
     * @param bo 查询条件
     * @return 商户接入渠道（聚合）列表
     */
    List<ImMerchantChannelVo> queryList(ImMerchantChannelBo bo);

    /**
     * 新增商户接入渠道（聚合）
     *
     * @param bo 商户接入渠道（聚合）
     * @return 是否新增成功
     */
    Long insertByBo(ImMerchantChannelBo bo);

    /**
     * 修改商户接入渠道（聚合）
     *
     * @param bo 商户接入渠道（聚合）
     * @return 是否修改成功
     */
    Boolean updateByBo(ImMerchantChannelBo bo);

    /**
     * 校验并批量删除商户接入渠道（聚合）信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    ImMerchantChannelVo queryDialogRules(Long merchantId, Long merchantChannelId);

    ImMerchantChannelVo queryAssignmentRules(Long merchantId, Long merchantChannelId);

    void dialogRules(Long merchantChannelId, ChannelDialogRulesDto channelDialogRulesDto);

    void assignmentRules(Long merchantChannelId, AssignmentRulesDto assignmentRulesDto);
}
