package org.dromara.im.utils;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.websocket.dto.WebSocketMessageDto;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.MessageDirection;
import org.dromara.im.constans.MessageEvent;
import org.dromara.im.constans.MessageType;
import org.dromara.im.domain.dto.message.ChatMessageDTO;
import org.dromara.im.domain.dto.message.ChatTemplateMessage;
import org.dromara.im.domain.vo.ImConversationVo;

import java.util.List;


@Slf4j
public class SendMessageUtil {

    public static String buildSessionKey(Long merchantId, Long adminId) {
        return merchantId + "-" + adminId;
    }

    public static String buildSessionKey(Long merchantId, String visitorId) {
        return merchantId + "-" + visitorId;
    }

    public static void sendMessageStatus(String sessionKey, String visitorId, String status, String clientMsgId, MessageEvent event) {
        try {
            ChatMessageDTO chat = ChatMessageDTO.buildChat();
            chat.setType(MessageType.MESSAGE_STATUS);
            chat.setVisitorId(visitorId);
            chat.setStatus(status);
            chat.setClientMsgId(clientMsgId);
            ChatTemplateMessage chatTemplateMessage = ChatTemplateMessage.builder()
                .event(event.getCode())
                .data(chat)
                .build();
            WebSocketUtils.sendMessage(sessionKey, JSONUtil.toJsonStr(chatTemplateMessage));

            log.info("发送消息状态给:{} - serviceId: {}, visitorId: {}, status: {}", event.getCode(), sessionKey, visitorId, status);

        } catch (Exception e) {
            log.error("发送消息状态失败 - serviceId: {}, visitorId: {}", sessionKey, visitorId, e);
        }
    }

    /**
     * 通知客服有新游客
     */
    public static void notifyServiceNewVisitor(String visitorId, Long serviceId) {
        try {
            String serviceSessionKey = SendMessageUtil.buildSessionKey(serviceId, serviceId);
            ChatMessageDTO chat = ChatMessageDTO.buildChat();
            chat.setType(MessageType.NEW_VISITOR);
            chat.setVisitorId(visitorId);
            chat.setContent("你有新的用户进线啦");
            chat.setStatus("sent");
            ChatTemplateMessage chatTemplateMessage = ChatTemplateMessage.builder()
                .event(MessageEvent.VISITOR__TO_SERVICE_EVENT.getCode())
                .data(chat)
                .build();
            WebSocketUtils.sendMessage(serviceSessionKey, JSONUtil.toJsonStr(chatTemplateMessage));

        } catch (Exception e) {
            log.error("通知客服新游客失败", e);
        }
    }


    /**
     * 发送开始服务提示
     */
    public static void sendPersonTipMessage(ImConversationVo conversation, String content) {
        ChatMessageDTO chat = ChatMessageDTO.buildChat();
        chat.setType(MessageType.CHAT_MESSAGE);
        chat.setServiceId(conversation.getAdminId().toString());
        chat.setServiceName(conversation.getAdminName());
        chat.setServiceAvatar(conversation.getAdminAvatar());
        chat.setMessageType("text");
        chat.setVisitorId(conversation.getVisitorId());
        chat.setVisitorName(conversation.getVisitorName());
        chat.setVisitorAvatar(conversation.getVisitorAvatar());
        chat.setDirection(MessageDirection.to_visitor.name());
        chat.setMerchantId(conversation.getMerchantId());
        chat.setContent(content);
        // 硬编码发送分配成功消息
        ChatTemplateMessage message = ChatTemplateMessage.builder()
            .event(MessageEvent.SERVICE_TO_VISITOR_EVENT.getCode())
            .data(chat)
            .build();
        String visitorSessionKey = buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());
        WebSocketUtils.sendMessage(visitorSessionKey, JSONUtil.toJsonStr(message));
        log.info("发送客服分配成功消息 - conversationId: {}", conversation.getConversationId());

    }


    /**
     * 客服消息转发给访客
     */
    public static void forwardServiceMessageToVisitor(ImConversationVo conversationVo, String content, String messageType) {
        String visitorId = conversationVo.getVisitorId();
        String serviceId = conversationVo.getAdminId().toString();
        try {
            String visitorSessionKey = buildSessionKey(conversationVo.getMerchantId(), visitorId);
            if (StringUtils.isNotBlank(visitorSessionKey)) {
                // 构造符合现有格式的消息
                ChatMessageDTO chatData = new ChatMessageDTO();
                chatData.setType(MessageType.CHAT_MESSAGE);
                chatData.setMessageType(messageType);
                chatData.setContent(content);
                chatData.setMessageSendTime(System.currentTimeMillis());
                chatData.setVisitorId(visitorId);
                chatData.setVisitorName(conversationVo.getVisitorName());
                chatData.setVisitorAvatar(conversationVo.getVisitorAvatar());
                chatData.setServiceId(serviceId);
                chatData.setServiceName(conversationVo.getAdminName());
                chatData.setServiceAvatar(conversationVo.getAdminAvatar());
                chatData.setDirection(MessageDirection.to_visitor.name());
                chatData.setMerchantId(conversationVo.getMerchantId());

                ChatTemplateMessage wsMsg = ChatTemplateMessage.builder()
                    .event(MessageEvent.SERVICE_TO_VISITOR_EVENT.getCode())
                    .data(chatData)
                    .build();

                WebSocketUtils.sendMessage(visitorSessionKey, JSONUtil.toJsonStr(wsMsg));

                log.info("转发客服消息给访客成功 - visitorId: {}, serviceId: {}, content: {}",
                    visitorId, serviceId, content);

            } else {
                log.info("访客会话不存在，无法转发客服消息 - visitorId: {}, businessId: {}",
                    visitorId, null);
            }

        } catch (Exception e) {
            log.error("转发客服消息给访客失败 - visitorId: {}", visitorId, e);
        }
    }

    /**
     * 访客消息转发给客服
     */
    public static void forwardVisitorMessageToService(ImConversationVo conversationVo, String messageType, Long adminId, String content) {
        try {
            Long merchantId = conversationVo.getMerchantId();
            String serviceSessionKey = buildSessionKey(merchantId, adminId);
            if (StringUtils.isNotBlank(serviceSessionKey)) {
                // 构造符合现有格式的消息
                ChatMessageDTO chatData = new ChatMessageDTO();
                chatData.setType(MessageType.CHAT_MESSAGE);
                chatData.setMessageType(messageType);
                chatData.setVisitorId(conversationVo.getVisitorId());
                chatData.setVisitorName(conversationVo.getVisitorName());
                chatData.setVisitorAvatar(conversationVo.getVisitorAvatar());
                chatData.setServiceId(adminId.toString());
                chatData.setServiceName(conversationVo.getAdminName());
                chatData.setServiceAvatar(conversationVo.getAdminAvatar());
                chatData.setContent(content);
                chatData.setDirection(MessageDirection.to_service.name());
                chatData.setMerchantId(merchantId);
                chatData.setMessageSendTime(System.currentTimeMillis());

                ChatTemplateMessage wsMsg = ChatTemplateMessage.builder()
                    .event(MessageEvent.VISITOR__TO_SERVICE_EVENT.getCode())
                    .data(chatData)
                    .build();
                // 创建WebSocket消息DTO对象
                WebSocketMessageDto webSocketMessageDto = new WebSocketMessageDto();
                webSocketMessageDto.setSessionKeys(List.of(serviceSessionKey));
                webSocketMessageDto.setMessage(JSONUtil.toJsonStr(wsMsg));
                WebSocketUtils.publishMessage(webSocketMessageDto);

                log.info("转发访客消息给客服成功 - visitorId: {}, content: {}", adminId, content);

            } else {
                log.info("客服会话不存在，无法转发访客消息 - visitorId: {}",
                    adminId);
            }

        } catch (Exception e) {
            log.error("转发访客消息给客服失败 - visitorId: {}", adminId, e);
        }
    }


}
