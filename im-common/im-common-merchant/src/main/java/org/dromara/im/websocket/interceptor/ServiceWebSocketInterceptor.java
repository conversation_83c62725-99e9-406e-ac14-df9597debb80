package org.dromara.im.websocket.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

import static org.dromara.common.websocket.constant.WebSocketConstants.LOGIN_USER_KEY;

/**
 * 客服WebSocket握手拦截器
 * 专门处理客服连接，需要token验证且userType必须为SERVICE
 *
 * <AUTHOR>
 */
@Slf4j
public class ServiceWebSocketInterceptor implements HandshakeInterceptor {

    /**
     * WebSocket握手之前执行的前置处理方法
     * 验证token有效性和客服身份
     */
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {
        try {
            // 检查是否登录 是否有token
            LoginUser loginUser = LoginHelper.getLoginUser();
            attributes.put(LOGIN_USER_KEY, loginUser);
            return true;
        } catch (Exception e) {
            log.error("访客WebSocket连接验证失败", e);
            return false;
        }
    }

    /**
     * WebSocket握手成功后执行的后置处理方法
     */
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            log.error("[service-interceptor] 握手后处理异常", exception);
        }
    }
}
