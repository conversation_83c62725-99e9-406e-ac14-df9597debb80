package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 套餐对象 im_chat_package
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@TableName("im_chat_package")
public class ImChatPackage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 套餐ID
     */
    @TableId(value = "chat_package_id")
    private Long chatPackageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 1->标准版;2->专业版;3->集团版
     */
    private String packageType;

    /**
     * 套餐价格(USD)
     */
    private BigDecimal price;

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 计费周期数量
     */
    private Integer billingPeriod;

    /**
     * 计费周期(0-月付 1-季付 2-年付)
     */
    private String billingCycle;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 功能配置JSON，采用驼峰命名
     */
    private String featureConfig;

    /**
     * 状态(1-启用 0-禁用)
     */
    private String status;

    /**
     * 排序
     */
    private Long sortOrder;

    /**
     * 备注
     */
    private String remark;

    // 坐席数量限制
    private Integer seatsCount;
    // 接待数据限制
    private Integer chatNumber;
    // 渠道数量
    private Integer channelNumber;


}
