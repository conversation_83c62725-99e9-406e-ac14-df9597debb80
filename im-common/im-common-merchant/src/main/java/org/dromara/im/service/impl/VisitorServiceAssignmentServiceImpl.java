package org.dromara.im.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.websocket.dto.WebSocketMessageDto;
import org.dromara.common.websocket.holder.WebSocketSessionHolder;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.MessageType;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.service.IImMerchantAdministratorService;
import org.dromara.im.service.IVisitorServiceAssignmentService;
import org.dromara.im.utils.SendMessageUtil;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static org.dromara.im.constans.MessageEvent.SERVICE_TO_VISITOR_EVENT;

/**
 * 游客客服分配服务实现
 * 实现多种分配策略：轮询、负载均衡、随机分配等
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VisitorServiceAssignmentServiceImpl implements IVisitorServiceAssignmentService {

    private final IImMerchantAdministratorService merchantAdministratorService;

    // Redis键前缀
    private static final String ASSIGNMENT_KEY_PREFIX = "visitor:assignment:";
    private static final String SERVICE_VISITOR_COUNT_PREFIX = "service:visitor:count:";

    // 分配关系过期时间（24小时）
    private static final Duration ASSIGNMENT_EXPIRE_TIME = Duration.ofHours(24);

    @Override
    public Long assignServiceToVisitor(String visitorId, Long businessId, String channel) {
        try {
            // 检查是否已经分配过客服
            Long existingServiceId = Long.parseLong(getAssignedServiceId(visitorId, businessId));
            if (existingServiceId != null && isServiceOnline(existingServiceId, businessId)) {
                log.info("[assign-service] 游客{}已分配客服{}", visitorId, existingServiceId);
                notifyServiceVisitorOnline(visitorId, businessId, existingServiceId);
                return existingServiceId;
            }

            // 获取可用的客服列表
            List<ImMerchantAdministratorVo> availableServices = getAvailableServices(businessId, channel);
            if (availableServices.isEmpty()) {
                log.info("[assign-service] 商户{}渠道{}没有可用客服", businessId, channel);
                sendNoServiceAvailableMessage(visitorId, businessId);
                return null;
            }

            // 根据负载均衡策略选择客服
            Long selectedServiceId = selectServiceByLoadBalance(availableServices, businessId);

            if (selectedServiceId != null) {
                // 保存分配关系
                saveAssignment(visitorId, businessId, selectedServiceId);

                // 增加客服的游客计数
                incrementServiceVisitorCount(selectedServiceId, businessId);

                // 通知客服有新游客
                notifyServiceNewVisitor(visitorId, selectedServiceId);

                // 通知游客分配成功
                notifyVisitorAssignmentSuccess(visitorId, businessId, selectedServiceId);

                log.info("[assign-service] 成功为游客{}分配客服{}", visitorId, selectedServiceId);
                return selectedServiceId;
            }

            log.info("[assign-service] 为游客{}分配客服失败", visitorId);
            return null;

        } catch (Exception e) {
            log.error("[assign-service] 分配客服异常", e);
            return null;
        }
    }

    @Override
    public void forwardMessageToService(String visitorId, Long businessId, Long serviceId, String content, String messageType) {
        try {
            // 构造消息
            Map<String, Object> message = Map.of(
                "type", MessageType.VISITOR_MESSAGE,
                "fromVisitorId", visitorId,
                "businessId", businessId,
                "content", content,
                "messageType", messageType,
                "messageSendTime", System.currentTimeMillis()
            );

            // 发送给指定客服
            String serviceSessionKey = generateServiceSessionKey(serviceId, businessId);
            WebSocketMessageDto webSocketMessage = new WebSocketMessageDto();
            webSocketMessage.setSessionKeys(List.of(serviceSessionKey));
            webSocketMessage.setMessage(JSONUtil.toJsonStr(message));

            WebSocketUtils.publishMessage(webSocketMessage);

            log.info("[forward-message] 转发游客{}消息给客服{}: {}", visitorId, serviceId, content);

        } catch (Exception e) {
            log.error("[forward-message] 转发消息失败", e);
        }
    }

    @Override
    public void forwardMessageToAssignedService(String visitorId, Long businessId, String content, String messageType) {
        Long assignedServiceId = Long.parseLong(getAssignedServiceId(visitorId, businessId));
        if (assignedServiceId != null) {
            forwardMessageToService(visitorId, businessId, assignedServiceId, content, messageType);
        } else {
            log.info("[forward-message] 游客{}未分配客服，无法转发消息", visitorId);
            // 尝试重新分配客服
            Long newServiceId = assignServiceToVisitor(visitorId, businessId, null);
            if (newServiceId != null) {
                forwardMessageToService(visitorId, businessId, newServiceId, content, messageType);
            }
        }
    }

    @Override
    public void notifyServiceVisitorOffline(String visitorId, Long businessId) {
        Long assignedServiceId = Long.parseLong(getAssignedServiceId(visitorId, businessId));
        if (assignedServiceId != null) {
            try {
                // 构造离线通知消息
                Map<String, Object> message = Map.of(
                    "type", MessageType.VISITOR_OFFLINE,
                    "visitorId", visitorId,
                    "businessId", businessId,
                    "messageSendTime", System.currentTimeMillis()
                );

                // 发送给客服
                String serviceSessionKey = generateServiceSessionKey(assignedServiceId, businessId);
                WebSocketMessageDto webSocketMessage = new WebSocketMessageDto();
                webSocketMessage.setSessionKeys(List.of(serviceSessionKey));
                webSocketMessage.setMessage(JSONUtil.toJsonStr(message));

                WebSocketUtils.publishMessage(webSocketMessage);

                // 减少客服的游客计数
                decrementServiceVisitorCount(assignedServiceId, businessId);

                // 释放分配关系
                releaseAssignment(visitorId, businessId);

                log.info("[visitor-offline] 通知客服{}游客{}已离线", assignedServiceId, visitorId);

            } catch (Exception e) {
                log.error("[visitor-offline] 通知客服游客离线失败", e);
            }
        }
    }

    @Override
    public String getAssignedServiceId(String visitorId, Long businessId) {
        String key = ASSIGNMENT_KEY_PREFIX + businessId + ":" + visitorId;
        return RedisUtils.getCacheObject(key);
    }

    @Override
    public void releaseAssignment(String visitorId, Long businessId) {
        String key = ASSIGNMENT_KEY_PREFIX + businessId + ":" + visitorId;
        RedisUtils.deleteObject(key);
    }

    @Override
    public boolean isServiceOnline(Long serviceId, Long businessId) {
        String sessionKey = generateServiceSessionKey(serviceId, businessId);
        return WebSocketSessionHolder.existSession(sessionKey);
    }

    @Override
    public int getServiceVisitorCount(Long serviceId, Long businessId) {
        String key = SERVICE_VISITOR_COUNT_PREFIX + businessId + ":" + serviceId;
        Integer count = RedisUtils.getCacheObject(key);
        return count != null ? count : 0;
    }

    /**
     * 获取可用的客服列表
     */
    private List<ImMerchantAdministratorVo> getAvailableServices(Long businessId, String channel) {
        // 获取该商户的所有客服
        List<ImMerchantAdministratorVo> allServices = merchantAdministratorService.getServicesByBusinessId(businessId);

        // 过滤在线且可用的客服
        return allServices.stream()
            .filter(service -> isServiceAvailable(service, businessId, channel))
            .toList();
    }

    /**
     * 检查客服是否可用
     */
    private boolean isServiceAvailable(ImMerchantAdministratorVo service, Long businessId, String channel) {
        // 检查客服状态
        if (!StringUtils.equals(service.getStatus(), "1")) {
            return false;
        }

        // 检查是否在线
        if (!isServiceOnline(service.getAdminId(), businessId)) {
            return false;
        }

        // 检查渠道权限（如果有渠道限制）
        // TODO: 根据实际业务需求实现渠道权限检查

        // 检查客服当前负载
        int currentLoad = getServiceVisitorCount(service.getAdminId(), businessId);
        int maxLoad = service.getMaxConcurrentVisitors() != null ? service.getMaxConcurrentVisitors() : 10;

        return currentLoad < maxLoad;
    }

    /**
     * 根据负载均衡策略选择客服
     */
    private Long selectServiceByLoadBalance(List<ImMerchantAdministratorVo> availableServices, Long businessId) {
        if (availableServices.isEmpty()) {
            return null;
        }

        // 策略1: 负载最少优先
        return availableServices.stream()
            .min(Comparator.comparingInt(service ->
                getServiceVisitorCount(service.getAdminId(), businessId)))
            .map(ImMerchantAdministratorVo::getAdminId)
            .orElse(null);
    }

    /**
     * 保存分配关系
     */
    private void saveAssignment(String visitorId, Long businessId, Long serviceId) {
        String key = ASSIGNMENT_KEY_PREFIX + businessId + ":" + visitorId;
        RedisUtils.setCacheObject(key, serviceId, ASSIGNMENT_EXPIRE_TIME);
    }

    /**
     * 增加客服游客计数
     */
    private void incrementServiceVisitorCount(Long serviceId, Long businessId) {
        String key = SERVICE_VISITOR_COUNT_PREFIX + businessId + ":" + serviceId;
        RedisUtils.increment(key);
        RedisUtils.expire(key, ASSIGNMENT_EXPIRE_TIME);
    }

    /**
     * 减少客服游客计数
     */
    private void decrementServiceVisitorCount(Long serviceId, Long businessId) {
        String key = SERVICE_VISITOR_COUNT_PREFIX + businessId + ":" + serviceId;
        RedisUtils.decrement(key);
    }

    /**
     * 生成客服会话Key
     */
    private String generateServiceSessionKey(Long serviceId, Long businessId) {
        return "service:" + businessId + ":" + serviceId;
    }

    /**
     * 通知客服有新游客
     */
    private void notifyServiceNewVisitor(String visitorId, Long serviceId) {
        try {

            String serviceSessionKey = SendMessageUtil.buildSessionKey(serviceId, serviceId);
            // 构建状态消息，格式与现有消息保持一致
            JSONObject data = new JSONObject();
            data.put("type", MessageType.NEW_VISITOR);
            data.put("visitorId", visitorId);
            data.put("content", "你有新的用户进线啦");
            data.put("status", "sent"); // sent, delivered, read, failed
            data.put("messageSendTime", System.currentTimeMillis());

            JSONObject statusMessage = new JSONObject();
            statusMessage.put("event", SERVICE_TO_VISITOR_EVENT.getCode());
            statusMessage.put("data", data);

            // 发送状态消息给客服
            WebSocketUtils.sendMessage(serviceSessionKey, statusMessage.toString());

        } catch (Exception e) {
            log.error("通知客服新游客失败", e);
        }
    }

    /**
     * 通知客服游客上线
     */
    private void notifyServiceVisitorOnline(String visitorId, Long businessId, Long serviceId) {
        try {
            Map<String, Object> message = Map.of(
                "type", MessageType.VISITOR_ONLINE,
                "visitorId", visitorId,
                "businessId", businessId,
                "messageSendTime", System.currentTimeMillis()
            );

            String serviceSessionKey = generateServiceSessionKey(serviceId, businessId);
            WebSocketMessageDto webSocketMessage = new WebSocketMessageDto();
            webSocketMessage.setSessionKeys(List.of(serviceSessionKey));
            webSocketMessage.setMessage(JSONUtil.toJsonStr(message));

            WebSocketUtils.publishMessage(webSocketMessage);
        } catch (Exception e) {
            log.error("通知客服游客上线失败", e);
        }
    }

    /**
     * 通知游客分配成功
     */
    private void notifyVisitorAssignmentSuccess(String visitorId, Long businessId, Long serviceId) {
        try {
            Map<String, Object> message = Map.of(
                "type", MessageType.ASSIGNMENT_SUCCESS,
                "serviceId", serviceId,
                "businessId", businessId,
                "message", "客服分配成功，正在为您服务",
                "messageSendTime", System.currentTimeMillis()
            );

            String visitorSessionKey = "visitor:" + businessId + ":" + visitorId;
            WebSocketMessageDto webSocketMessage = new WebSocketMessageDto();
            webSocketMessage.setSessionKeys(List.of(visitorSessionKey));
            webSocketMessage.setMessage(JSONUtil.toJsonStr(message));

            WebSocketUtils.publishMessage(webSocketMessage);
        } catch (Exception e) {
            log.error("通知游客分配成功失败", e);
        }
    }

    /**
     * 发送无客服可用消息
     */
    private void sendNoServiceAvailableMessage(String visitorId, Long businessId) {
        try {
            Map<String, Object> message = Map.of(
                "type", MessageType.NO_SERVICE_AVAILABLE,
                "message", "抱歉，当前没有客服在线，请稍后再试或留言",
                "messageSendTime", System.currentTimeMillis()
            );

            String visitorSessionKey = "visitor:" + businessId + ":" + visitorId;
            WebSocketMessageDto webSocketMessage = new WebSocketMessageDto();
            webSocketMessage.setSessionKeys(List.of(visitorSessionKey));
            webSocketMessage.setMessage(JSONUtil.toJsonStr(message));

            WebSocketUtils.publishMessage(webSocketMessage);
        } catch (Exception e) {
            log.error("发送无客服可用消息失败", e);
        }
    }
}
