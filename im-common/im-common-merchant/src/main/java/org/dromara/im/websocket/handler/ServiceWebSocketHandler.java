package org.dromara.im.websocket.handler;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.websocket.constant.WebSocketConstants;
import org.dromara.common.websocket.holder.WebSocketSessionHolder;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.MessageEvent;
import org.dromara.im.constans.MessageSendType;
import org.dromara.im.constans.MessageStatus;
import org.dromara.im.constans.MessageType;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImMessageService;
import org.dromara.im.utils.SendMessageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.util.Map;

/**
 * 客服WebSocket处理器
 * 处理客服与访客的一对一聊天
 * 集成现有的消息格式和推送机制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceWebSocketHandler extends AbstractWebSocketHandler {

    @Autowired
    private IImMessageService messageService;

    @Autowired
    private IImConversationService conversationService;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 从拦截器传递的属性中获取登录用户信息
        LoginUser loginUser = (LoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);
        if (loginUser == null) {
            log.error("客服连接失败：无法获取登录用户信息");
            session.close(CloseStatus.BAD_DATA);
            return;
        }

        // 在IM系统中，客服的sessionKey使用businessId
        Long merchantId = loginUser.getDeptId();
        Long adminId = loginUser.getUserId();
        String sessionKey = SendMessageUtil.buildSessionKey(merchantId, adminId);

        // 将客服信息存储到session属性中
        session.getAttributes().put("loginUser", loginUser);
        session.getAttributes().put("serviceSessionKey", sessionKey);

        // 使用统一的会话管理器
        WebSocketSessionHolder.addSession(sessionKey, session);

        log.info("客服连接建立成功 - adminId: {}, serviceName: {}, sessionKey: {}",
            loginUser.getUserId(), loginUser.getUsername(), sessionKey);
    }


    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {

        String payload = message.getPayload();
        if (payload.trim().isEmpty()) {
            log.warn("收到空消息，忽略处理");
            return;
        }

        // 建议：先判断是不是 JSON
        if (!JSONUtil.isTypeJSON(payload)) {
            log.warn("非法消息，不是 JSON，断开连接: {}", payload);
            session.close(CloseStatus.BAD_DATA);
            return;
        }
        LoginUser loginUser = (LoginUser) session.getAttributes().get("loginUser");
        String serviceSessionKey = (String) session.getAttributes().get("serviceSessionKey");

        log.info("收到客服消息 - adminId: {}, sessionKey: {}, message: {}",
            loginUser.getUserId(), serviceSessionKey, payload);

        // 处理客服消息
        handleServiceMessage(loginUser, loginUser.getDeptId(), payload);
    }

    /**
     * 处理接收到的二进制消息
     *
     * @param session WebSocket会话
     * @param message 接收到的二进制消息
     * @throws Exception 处理消息过程中可能抛出的异常
     */
    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws Exception {
        super.handleBinaryMessage(session, message);
    }


    /**
     * 处理接收到的Pong消息（心跳监测）
     *
     * @param session WebSocket会话
     * @param message 接收到的Pong消息
     */
    @Override
    protected void handlePongMessage(WebSocketSession session, PongMessage message) {
        log.info("发送Pong消息（心跳监测）");
        WebSocketUtils.sendPongMessage(session);
    }


    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("[transport error] sessionId: {} , exception:{}", session.getId(), exception.getMessage());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        LoginUser loginUser = (LoginUser) session.getAttributes().get("loginUser");
        String serviceSessionKey = (String) session.getAttributes().get("serviceSessionKey");

        if (loginUser != null) {
            // 移除会话
            if (serviceSessionKey != null) {
                WebSocketSessionHolder.removeSession(serviceSessionKey);
            }
            log.info("客服连接关闭 - serviceId: {}, serviceName: {}, closeStatus: {}",
                loginUser.getUserId(), loginUser.getUsername(), closeStatus);
        }
    }

    /**
     * 处理客服消息
     */
    private void handleServiceMessage(LoginUser loginUser, Long merchantId, String payload) {
        try {
            // 解析消息内容
            Map<String, Object> messageMap = JSONUtil.parseObj(payload);
            JSONObject data = JSONUtil.parseObj(messageMap.get("data"));
            String type = (String) data.get("type");

            if (MessageType.isChatMessage(type)) {// 回复访客消息
                handleReplyToVisitor(loginUser, merchantId, data);
            }

        } catch (Exception e) {
            log.error("消息JSON解析失败，尝试处理为简单文本: {}", payload);
            // 如果JSON解析失败，可能是简单的文本消息，暂时记录日志
            log.info("客服发送简单文本消息 - serviceId: {}, message: {}", loginUser.getUserId(), payload);
        }
    }

    /**
     * 处理客服回复访客的消息（标准格式）
     */
    private void handleReplyToVisitor(LoginUser loginUser, Long merchantId, Map<String, Object> messageMap) {
        try {
            String visitorId = (String) messageMap.get("visitorId"); // 注意这里是visitorId（与现有格式保持一致）
            String content = (String) messageMap.get("content");
            String clientMsgId = (String) messageMap.get("clientMsgId");
            String messageType = (String) messageMap.get("messageType");
            String imageWidth = (String) messageMap.get("imageWidth");
            String imageHeight = (String) messageMap.get("imageHeight");

            log.info("处理客服回复访客的消息visitorId:{},content:{}", visitorId, content);
            if (visitorId != null && content != null) {
                // 1. 获取会话ID
                Long conversationId = conversationService.getConversationId(visitorId, loginUser.getUserId(), merchantId);
                log.info("获取会话ID:{},content:{}", visitorId, content);

                if (conversationId != null) {
                    // 2. 保存客服消息到数据库
                    try {
                        messageService.saveMessage(conversationId, MessageSendType.service.name(), loginUser.getUserId().toString(),
                            loginUser.getUsername(), MessageSendType.visitor.name(), visitorId, messageType, content, clientMsgId, imageWidth, imageHeight);
                        log.info("客服消息保存成功 - conversationId: {}, serviceId: {}, visitorId: {}, content: {}",
                            conversationId, loginUser.getUserId(), visitorId, content);
                    } catch (Exception e) {
                        log.error("客服消息保存失败 - conversationId: {}, serviceId: {}, visitorId: {}",
                            conversationId, loginUser.getUserId(), visitorId, e);
                        return;
                    }

                    // 3. 更新会话信息
                    try {
                        conversationService.updateLastMessage(conversationId, content);
                        conversationService.incrementMessageCount(conversationId);
                    } catch (Exception e) {
                        log.error("更新会话信息失败 - conversationId: {}", conversationId, e);
                    }
                }

                // 4. 使用现有的聊天消息推送方法
                SendMessageUtil.forwardServiceMessageToVisitor(visitorId, loginUser.getDeptId(), loginUser.getUserId().toString(), content, messageType);

                // 5. 向客服发送消息发送状态确认
                sendMessageStatusToService(loginUser, merchantId, visitorId, clientMsgId);

                log.info("客服回复访客消息 - serviceId: {}, visitorId: {}, content: {}",
                    loginUser.getUserId(), visitorId, content);
            } else {
                log.warn("客服回复消息参数不完整 - serviceId: {}, visitorId: {}, content: {}",
                    loginUser.getUserId(), visitorId, content);
            }

        } catch (Exception e) {
            log.error("处理客服回复消息失败 - serviceId: {}", loginUser.getUserId(), e);
        }
    }

    /**
     * 向客服发送消息状态确认
     */
    private void sendMessageStatusToService(LoginUser loginUser, Long merchantId, String visitorId, String clientMsgId) {
        String serviceSessionKey = SendMessageUtil.buildSessionKey(merchantId, loginUser.getUserId());
        SendMessageUtil.sendMessageStatus(serviceSessionKey, visitorId, MessageStatus.sent, clientMsgId,MessageEvent.SERVICE_TO_VISITOR_EVENT);
    }

}
