package org.dromara.im.websocket.handler;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.websocket.constant.WebSocketConstants;
import org.dromara.common.websocket.holder.WebSocketSessionHolder;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.*;
import org.dromara.im.domain.dto.ImMessageDto;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImMessageService;
import org.dromara.im.utils.SendMessageUtil;
import org.dromara.im.utils.message.PersonMessageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.util.Map;

/**
 * 客服WebSocket处理器
 * 处理客服与访客的一对一聊天
 * 集成现有的消息格式和推送机制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceWebSocketHandler extends AbstractWebSocketHandler {

    @Autowired
    private IImMessageService messageService;

    @Autowired
    private IImConversationService conversationService;

    @Autowired
    private PersonMessageUtil personMessageUtil;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 从拦截器传递的属性中获取登录用户信息
        LoginUser loginUser = (LoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);
        if (loginUser == null) {
            log.error("客服连接失败：无法获取登录用户信息");
            session.close(CloseStatus.BAD_DATA);
            return;
        }

        // 在IM系统中，客服的sessionKey使用businessId
        Long merchantId = loginUser.getDeptId();
        Long adminId = loginUser.getUserId();
        String sessionKey = SendMessageUtil.buildSessionKey(merchantId, adminId);

        // 将客服信息存储到session属性中
        session.getAttributes().put("loginUser", loginUser);
        session.getAttributes().put("serviceSessionKey", sessionKey);

        // 记录客服上线活跃状态
        personMessageUtil.recordServiceActivity(adminId);

        // 使用统一的会话管理器
        WebSocketSessionHolder.addSession(sessionKey, session);

        log.info("客服连接建立成功 - adminId: {}, serviceName: {}, sessionKey: {}",
            loginUser.getUserId(), loginUser.getUsername(), sessionKey);
    }


    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {

        String payload = message.getPayload();
        if (payload.trim().isEmpty()) {
            log.info("收到空消息，忽略处理");
            return;
        }

        // 建议：先判断是不是 JSON
        if (!JSONUtil.isTypeJSON(payload)) {
            log.info("非法消息，不是 JSON，断开连接: {}", payload);
            session.close(CloseStatus.BAD_DATA);
            return;
        }
        LoginUser loginUser = (LoginUser) session.getAttributes().get("loginUser");
        String serviceSessionKey = (String) session.getAttributes().get("serviceSessionKey");

        log.info("收到客服消息 - adminId: {}, sessionKey: {}, message: {}", loginUser.getUserId(), serviceSessionKey, payload);

        // 处理客服消息
        handleServiceMessage(loginUser, loginUser.getDeptId(), payload);
    }

    /**
     * 处理接收到的二进制消息
     *
     * @param session WebSocket会话
     * @param message 接收到的二进制消息
     * @throws Exception 处理消息过程中可能抛出的异常
     */
    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws Exception {
        super.handleBinaryMessage(session, message);
    }


    /**
     * 处理接收到的Pong消息（心跳监测）
     *
     * @param session WebSocket会话
     * @param message 接收到的Pong消息
     */
    @Override
    protected void handlePongMessage(WebSocketSession session, PongMessage message) {
        log.info("发送Pong消息（心跳监测）");
        WebSocketUtils.sendPongMessage(session);
    }


    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("[transport error] sessionId: {} , exception:{}", session.getId(), exception.getMessage());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        LoginUser loginUser = (LoginUser) session.getAttributes().get("loginUser");
        String serviceSessionKey = (String) session.getAttributes().get("serviceSessionKey");

        if (loginUser != null) {
            // 移除会话
            if (serviceSessionKey != null) {
                WebSocketSessionHolder.removeSession(serviceSessionKey);
            }
            log.info("客服连接关闭 - serviceId: {}, serviceName: {}, closeStatus: {}",
                loginUser.getUserId(), loginUser.getUsername(), closeStatus);
        }
    }

    /**
     * 处理客服消息
     */
    private void handleServiceMessage(LoginUser loginUser, Long merchantId, String payload) {
        try {
            // 解析消息内容
            Map<String, Object> messageMap = JSONUtil.parseObj(payload);
            JSONObject data = JSONUtil.parseObj(messageMap.get("data"));
            String type = (String) data.get("type");

            if (MessageType.isChatMessage(type)) {// 回复访客消息
                handleReplyToVisitor(loginUser, merchantId, data);
            }else if (MessageType.serviceReceivedFromVisitor(type)) {

            }

        } catch (Exception e) {
            log.error("消息JSON解析失败，尝试处理为简单文本: {}", payload);
            // 如果JSON解析失败，可能是简单的文本消息，暂时记录日志
            log.info("客服发送简单文本消息 - serviceId: {}, message: {}", loginUser.getUserId(), payload);
        }
    }

    /**
     * 处理客服回复访客的消息（标准格式）
     */
    private void handleReplyToVisitor(LoginUser loginUser, Long merchantId, Map<String, Object> messageMap) {
        try {
            String visitorId = (String) messageMap.get("visitorId"); // 注意这里是visitorId（与现有格式保持一致）
            String content = (String) messageMap.get("content");
            String clientMsgId = (String) messageMap.get("clientMsgId");
            String messageType = (String) messageMap.get("messageType");
            String imageWidth = (String) messageMap.get("imageWidth");
            String imageHeight = (String) messageMap.get("imageHeight");

            log.info("处理客服回复访客的消息visitorId:{},content:{}", visitorId, content);
            if (visitorId != null && content != null) {
                // 1. 获取会话ID
                ImConversationVo conversationVo = conversationService.getConversationId(visitorId, loginUser.getUserId(), merchantId);
                log.info("获取会话ID:{},content:{}", visitorId, content);

                if (conversationVo != null) {
                    // 1. 如果是没有发现对应的客服id需要补充进来
                    autoRichAdminInfo(loginUser, conversationVo);
                    // 2. 保存客服消息到数据库
                    if (saveMessageSuccess(conversationVo, messageType, content, imageHeight, imageWidth, clientMsgId)) {
                        return;
                    }
                    // 3. 更新会话最后消息信息
                    Long conversationId = conversationVo.getConversationId();
                    conversationService.updateLastMessage(conversationId, content);

                    // 4. 记录客服活跃状态（用于忙碌自动回复判断）
                    personMessageUtil.recordServiceActivity(loginUser.getUserId());

                    // 5. 重置忙碌回复标记（客服重新活跃）
                    personMessageUtil.resetBusyReplyFlag(conversationVo.getConversationId());

                    // 6. 使用现有的聊天消息推送方法
                    SendMessageUtil.forwardServiceMessageToVisitor(conversationVo, content, messageType);

                    // 7. 向客服发送消息发送状态确认
                    SendMessageUtil.sendMessageStatus(SendMessageUtil.buildSessionKey(merchantId, loginUser.getUserId()),
                        visitorId, MessageStatus.sent, clientMsgId,messageType, MessageEvent.SERVICE_TO_VISITOR_EVENT);
                    log.info("客服回复访客消息 - serviceId: {}, visitorId: {}, content: {}", loginUser.getUserId(), visitorId, content);
                }
            } else {
                String serviceSessionKey = SendMessageUtil.buildSessionKey(merchantId, loginUser.getUserId());
                SendMessageUtil.sendMessageStatus(serviceSessionKey, visitorId, MessageStatus.sent, clientMsgId, messageType, MessageEvent.SERVICE_TO_VISITOR_EVENT);
                log.info("客服回复消息参数不完整 - serviceId: {}, visitorId: {}, content: {}", loginUser.getUserId(), visitorId, content);
            }

        } catch (Exception e) {
            log.error("处理客服回复消息失败 - serviceId: {}", loginUser.getUserId(), e);
        }
    }

    private void autoRichAdminInfo(LoginUser loginUser, ImConversationVo conversationVo) {
        if (conversationVo.getAdminId() == null) {
            ImMerchantAdministratorVo assignedService = new ImMerchantAdministratorVo();
            assignedService.setAdminId(loginUser.getUserId());
            assignedService.setNickname(loginUser.getNickname());
            assignedService.setAvatar(loginUser.getAvatarUrl());
            conversationService.updateConversationAdmin(conversationVo.getConversationId(), assignedService);
            conversationVo.setAdminId(loginUser.getUserId());
            conversationVo.setAdminName(loginUser.getNickname());
            conversationVo.setAdminAvatar(loginUser.getAvatarUrl());
        }
    }

    private boolean saveMessageSuccess(ImConversationVo conversationVo, String messageType, String content,
                                       String imageHeight, String imageWidth,String clientMsgId) {
        try {

            ImMessageDto messageDto = buildImMessageDto(conversationVo, messageType, content, imageHeight, imageWidth, clientMsgId);
            messageService.saveMessage(messageDto);
            log.info("客服消息保存成功 - conversationId: {}, serviceId: {}, visitorId: {}, content: {}", conversationVo.getConversationId(), conversationVo.getAdminId(), conversationVo.getVisitorId(), content);
        } catch (Exception e) {
            log.error("客服消息保存失败 - conversationId: {}, serviceId: {}, visitorId: {}",
                conversationVo.getConversationId(), conversationVo.getAdminId(), conversationVo.getVisitorId(), e);
            return true;
        }
        return false;
    }

    private static ImMessageDto buildImMessageDto(ImConversationVo conversationVo, String messageType,
                                                  String content, String imageHeight, String imageWidth, String clientMsgId) {
        ImMessageDto messageDto = new ImMessageDto();
        messageDto.setMerchantId(conversationVo.getMerchantId());
        messageDto.setConversationId(conversationVo.getConversationId());
        messageDto.setSenderType(MessageSendType.service.name());
        messageDto.setSenderId(conversationVo.getAdminId().toString());
        messageDto.setSenderName(conversationVo.getVisitorName());
        messageDto.setSenderAvatar(conversationVo.getAdminAvatar());
        messageDto.setReceiverId(conversationVo.getVisitorId());
        messageDto.setReceiverType(MessageSendType.visitor.name());
        messageDto.setReceiverName(conversationVo.getVisitorName());
        messageDto.setReceiverAvatar(conversationVo.getVisitorAvatar());
        messageDto.setMessageType(messageType);
        messageDto.setContent(content);
        messageDto.setDirection(MessageDirection.to_visitor.name());
        messageDto.setTimestamp(System.currentTimeMillis());
        messageDto.setClientMsgId(clientMsgId);
        if (StringUtils.isNotBlank(imageHeight) && StringUtils.isNotBlank(imageWidth)) {
            messageDto.setImageHeight(imageHeight);
            messageDto.setImageWidth(imageWidth);
        }
        return messageDto;
    }

}
