package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImAdminVisitorRelationBo;
import org.dromara.im.domain.vo.ImAdminVisitorRelationVo;

import java.util.Collection;
import java.util.List;

/**
 * 客服与访客服务关系Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-18
 */
public interface IImAdminVisitorRelationService {

    /**
     * 查询客服与访客服务关系
     *
     * @param adminVisitorRelationId 主键
     * @return 客服与访客服务关系
     */
    ImAdminVisitorRelationVo queryById(Long adminVisitorRelationId);

    /**
     * 分页查询客服与访客服务关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 客服与访客服务关系分页列表
     */
    TableDataInfo<ImAdminVisitorRelationVo> queryPageList(ImAdminVisitorRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的客服与访客服务关系列表
     *
     * @param bo 查询条件
     * @return 客服与访客服务关系列表
     */
    List<ImAdminVisitorRelationVo> queryList(ImAdminVisitorRelationBo bo);

    /**
     * 新增客服与访客服务关系
     *
     * @param bo 客服与访客服务关系
     * @return 是否新增成功
     */
    Boolean insertByBo(ImAdminVisitorRelationBo bo);

    /**
     * 修改客服与访客服务关系
     *
     * @param bo 客服与访客服务关系
     * @return 是否修改成功
     */
    Boolean updateByBo(ImAdminVisitorRelationBo bo);

    /**
     * 校验并批量删除客服与访客服务关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 创建或获取客服访客关系
     * 如果关系已存在则直接返回，不存在则创建后返回
     *
     * @param adminId           客服ID
     * @param visitorId         访客ID
     * @param merchantId        商户ID
     * @param merchantChannelId 商户渠道ID
     */
    void createOrGetAdminVisitorRelation(Long adminId, String visitorId, Long merchantId, Long merchantChannelId);



    /**
     * 切换访客置顶状态
     *
     * @param adminId   客服ID
     * @param visitorId 访客ID
     * @return 是否操作成功
     */
    Boolean toggleTopStatus(Long adminId, String visitorId);

    Boolean blockVisitor(Long adminId, String visitorId);

    void updateEndService(Long merchantId, String visitorId, Long adminId);

    /**
     * 更新客服访客关系的备注信息
     *
     * @param adminId   客服ID
     * @param visitorId 访客ID
     * @param remark    备注内容
     * @return 是否更新成功
     */
    Boolean updateRemark(Long adminId, String visitorId, String remark);
}
