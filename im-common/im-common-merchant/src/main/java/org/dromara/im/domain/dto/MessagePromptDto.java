package org.dromara.im.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 消息提醒设置请求
 *
 * <AUTHOR>
 */
@Data
public class MessagePromptDto implements Serializable {

    /**
     * 新对话提醒开关 0=关闭 1=开启
     */
    private String newDialogPrompt;

    /**
     * 新消息提醒开关 0=关闭 1=开启
     */
    private String newMessagePrompt;

    /**
     * 对话准入提醒开关 0=关闭 1=开启
     */
    private String dialogAccessPrompt;

    /**
     * 对话转出提醒开关 0=关闭 1=开启
     */
    private String dialogTransferPrompt;

    /**
     * 手机电脑同时提醒开关 0=关闭 1=开启
     */
    private String mobileDesktopPrompt;

    public static MessagePromptDto getDefault() {
        MessagePromptDto dto = new MessagePromptDto();
        dto.setNewDialogPrompt("1");
        dto.setNewMessagePrompt("1");
        dto.setDialogAccessPrompt("1");
        dto.setDialogTransferPrompt("0");
        dto.setMobileDesktopPrompt("0");
        return dto;
    }

}
