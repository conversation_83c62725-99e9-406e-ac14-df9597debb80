package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImTransferLog;

import java.util.Date;

/**
 * 会话转接记录业务对象 im_transfer_log
 *
 * <AUTHOR> Li
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImTransferLog.class, reverseConvertGenerate = false)
public class ImTransferLogBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 原客服ID
     */
    @NotNull(message = "原客服ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long fromServiceId;

    /**
     * 目标客服ID
     */
    private Long toServiceId;

    /**
     * 转接类型: manual=手动转接, auto=自动转接, timeout=超时转接
     */
    private String transferType;

    /**
     * 转接原因
     */
    @NotBlank(message = "转接原因不能为空", groups = {AddGroup.class, EditGroup.class})
    private String transferReason;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long transferBy;

    /**
     * 转接状态: pending=待接收, accepted=已接收, rejected=已拒绝
     */
    private String status;

    /**
     * 处理时间
     */
    @NotNull(message = "处理时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date processedTime;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tenantId;


}
