package org.dromara.im.constans;

public interface MessageType {

    /**
     * 消息状态
     */
    String MESSAGE_STATUS = "MESSAGE_STATUS";

    /**
     * 聊天消息
     */
    String CHAT_MESSAGE = "CHAT_MESSAGE";

    /**
     * 排队成功
     */
    String QUEUE_SUCCESS = "QUEUE_SUCCESS";

    /**
     * 排队等待
     */
    String QUEUE_WAITING = "QUEUE_WAITING";

    /**
     * 排队已满
     */
    String QUEUE_FULL = "QUEUE_FULL";

    /**
     * 无可用客服
     */
    String NO_SERVICE_AVAILABLE = "NO_SERVICE_AVAILABLE";

    /**
     * 访客消息
     */
    String VISITOR_MESSAGE = "VISITOR_MESSAGE";

    /**
     * 访客离线
     */
    String VISITOR_OFFLINE = "VISITOR_OFFLINE";

    /**
     * 客服消息
     */
    String NEW_VISITOR = "NEW_VISITOR";

    /**
     * 新的游客
     */
    String VISITOR_ONLINE="VISITOR_ONLINE";

    /**
     * 分配成功
     */
    String ASSIGNMENT_SUCCESS="ASSIGNMENT_SUCCESS";

    /**
     * 通知客服
     */
    String SYSTEM_NOTIFICATION = "SYSTEM_NOTIFICATION";
}
