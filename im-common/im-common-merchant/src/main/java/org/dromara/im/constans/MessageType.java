package org.dromara.im.constans;

import org.dromara.common.core.utils.StringUtils;

public interface MessageType {

    /**
     * 消息状态
     */
    String MESSAGE_STATUS = "MESSAGE_STATUS";

    /**
     * 聊天消息
     */
    String CHAT_MESSAGE = "CHAT_MESSAGE";

    /**
     * 排队成功
     */
    String QUEUE_SUCCESS = "QUEUE_SUCCESS";

    /**
     * 排队等待
     */
    String QUEUE_WAITING = "QUEUE_WAITING";

    /**
     * 排队已满
     */
    String QUEUE_FULL = "QUEUE_FULL";


    /**
     * 客服消息
     */
    String NEW_VISITOR = "NEW_VISITOR";

    /**
     * 访客收到客服消息
     */
    String VISITOR_RECEIVED_FROM_SERVICE = "VISITOR_RECEIVED_FROM_SERVICE";

    /**
     * 客服收到访客消息
     */
    String SERVICE__RECEIVED_FROM_VISITOR = "SERVICE__RECEIVED_FROM_VISITOR";


    static boolean isChatMessage(String type) {
        return StringUtils.equals(type, CHAT_MESSAGE);
    }

    static boolean serviceReceivedFromVisitor(String type) {
        return StringUtils.equals(type, SERVICE__RECEIVED_FROM_VISITOR);
    }
}
