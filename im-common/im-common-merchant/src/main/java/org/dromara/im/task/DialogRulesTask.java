package org.dromara.im.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.websocket.holder.WebSocketSessionHolder;
import org.dromara.im.domain.bo.ImMerchantChannelBo;
import org.dromara.im.domain.dto.ChannelDialogRulesDto;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantChannelVo;
import org.dromara.im.service.IImAdminVisitorRelationService;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImMerchantChannelService;
import org.dromara.im.service.IImMessageService;
import org.dromara.im.utils.SendMessageUtil;
import org.dromara.im.utils.message.PersonMessageUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

/**
 * 对话规则定时任务
 * 处理自动结束对话、自动转接等规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "im.dialog.rules.enabled", havingValue = "true", matchIfMissing = true)
public class DialogRulesTask {

    private final IImConversationService conversationService;
    private final IImMerchantChannelService channelService;
    private final IImMessageService messageService;
    private final IImAdminVisitorRelationService adminVisitorRelationService;

    /**
     * 自动结束对话 -->已完成调试
     * 每分钟执行一次
     */
    @Scheduled(fixedDelay = 60000)
    public void checkAutoEndDialog() {
        try {
            log.info("开始检查自动结束对话规则");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();

            for (ImConversationVo conversation : activeConversations) {
                ChannelDialogRulesDto rules = getDialogRules(conversation);

                if ("1".equals(rules.getAutoEndDialog())) {
                    // 获取最后一条消息时间
                    Long lastMessageTime = messageService.getLastMessageTime(conversation.getConversationId());

                    if (lastMessageTime != null) {
                        long endTime = lastMessageTime + rules.getAutoEndDialogTime() * 60 * 1000L;


                        if (endTime < System.currentTimeMillis()) {
                            endDialog(conversation, "自动结束：对话超时 " + rules.getAutoEndDialogTime() + " 分钟");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查自动结束对话规则失败", e);
        }
    }

    /**
     * 顾客离线自动结束对话 -->已完成调试
     * 每30秒执行一次
     */
    @Scheduled(fixedDelay = 30000)
    public void checkCustomerOfflineAutoEnd() {
        try {
            log.info("开始检查顾客离线自动结束规则");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();

            for (ImConversationVo conversation : activeConversations) {
                ChannelDialogRulesDto rules = getDialogRules(conversation);

                if ("1".equals(rules.getCustomerOfflineAutoEnd())) {
                    String key = getVisitorActivityRedisKey(conversation.getVisitorId());
                    Long lastActive = RedisUtils.getCacheObject(key);

                    if (lastActive != null) {
                        long secondsOffline = (System.currentTimeMillis() - lastActive) / 1000;

                        if (secondsOffline >= rules.getCustomerOfflineTimeout()) {
                            endDialog(conversation, "顾客离线超时 " + rules.getCustomerOfflineTimeout() + " 秒");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查顾客离线自动结束规则失败", e);
        }
    }

    /**
     * 对话延误自动转接
     * 每30秒执行一次
     */
    @Scheduled(fixedDelay = 30000)
    public void checkDialogAutoTransfer() {
        try {
            log.info("开始检查对话自动转接规则");

            // 检查等待中的会话（首条消息超时转接）
            List<ImConversationVo> waitingConversations = conversationService.findWaitingConversations();

            for (ImConversationVo conversation : waitingConversations) {
                ChannelDialogRulesDto dialogRule = getDialogRules(conversation);

                if ("1".equals(dialogRule.getDialogAutoTransfer())) {
                    Long firstMessageSendTime = messageService.getFirstMessageTime(conversation.getConversationId());

                    if (firstMessageSendTime == null) {
                        continue;
                    }
                    long expireTime = System.currentTimeMillis() - firstMessageSendTime;

                    if (expireTime > 1000L * dialogRule.getFirstMessageTimeout()) {
                        int transferCount = getTransferCount(conversation.getConversationId());
                        if (transferCount < dialogRule.getMaxTransferTimes()) {
                            transferToAnotherService(conversation, "首条消息超时转接");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查对话自动转接规则失败", e);
        }
    }

    /**
     * 客服离线自动转接
     * 每分钟执行一次
     */
    @Scheduled(fixedDelay = 60000)
    public void checkServiceOfflineAutoTransfer() {
        try {
            log.info("开始检查客服离线自动转接规则");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();

            for (ImConversationVo conversation : activeConversations) {
                if (conversation.getAdminId() == null) continue;

                ChannelDialogRulesDto dialogRule = getDialogRules(conversation);

                if ("1".equals(dialogRule.getServiceOfflineAutoTransfer())) {
                    boolean isServiceOnline = checkServiceOnlineStatus(conversation.getAdminId(), conversation.getMerchantId());
                    Long firstMessageSendTime = messageService.getLastMessageTime(conversation.getConversationId());
                    if (firstMessageSendTime == null) {
                        continue;
                    }
                    long expiredTime = System.currentTimeMillis() - firstMessageSendTime;
                    if (!isServiceOnline && (expiredTime > 1000L * dialogRule.getCustomerMessageInterval())) {
                        int transferCount = getTransferCount(conversation.getConversationId());
                        if (transferCount < dialogRule.getMaxTransferTimes()) {
                            transferToAnotherService(conversation, "客服离线自动转接");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查客服离线自动转接规则失败", e);
        }
    }


    /**
     * 记录顾客活跃状态
     */
    public void recordCustomerActivity(String visitorId) {
        String key = getVisitorActivityRedisKey(visitorId);
        RedisUtils.setCacheObject(key, System.currentTimeMillis(), Duration.ofHours(24));
    }

    private static String getVisitorActivityRedisKey(String visitorId) {
        return "visitor:last_active:" + visitorId;
    }

    /**
     * 记录顾客最后发送消息时间
     */
    public void recordCustomerMessage(String visitorId) {
        String key = getVisitorLastSendMessageRedisKey(visitorId);
        RedisUtils.setCacheObject(key, String.valueOf(System.currentTimeMillis()), Duration.ofHours(24));
    }

    private static String getVisitorLastSendMessageRedisKey(String visitorId) {
        return "visitor:last_message:" + visitorId;
    }

    /**
     * 获取对话规则
     */
    private ChannelDialogRulesDto getDialogRules(ImConversationVo conversationVo) {
        Long merchantChannelId = conversationVo.getMerchantChannelId();
        if (null == merchantChannelId) {
            ImMerchantChannelBo bo = new ImMerchantChannelBo();
            bo.setMerchantId(conversationVo.getMerchantId());
            bo.setDefaultChannel("1");
            List<ImMerchantChannelVo> imMerchantChannelVos = channelService.queryList(bo);
            ImMerchantChannelVo merchantChannelVo = imMerchantChannelVos.get(0);
            merchantChannelId = merchantChannelVo.getMerchantChannelId();
        }
        String cacheKey = "channel:dialog:rules:" + merchantChannelId;
        String cachedRules = RedisUtils.getCacheObject(cacheKey);
        if (cachedRules != null) {
            try {
                return JsonUtils.parseObject(cachedRules, ChannelDialogRulesDto.class);
            } catch (Exception e) {
                log.info("解析缓存的对话规则失败", e);
            }
        }
        // 从数据库获取
        ImMerchantChannelVo channel = channelService.queryById(merchantChannelId);
        ChannelDialogRulesDto rules = null;
        if (channel != null) {
            channel.buildChannelDialogRulesDto();
            rules = channel.getChannelDialogRulesDto();
            RedisUtils.setCacheObject(cacheKey, channel.getDialogRules());
        }
        return rules;
    }

    /**
     * 结束对话
     */
    private void endDialog(ImConversationVo conversation, String reason) {
        // 结束对话
        conversationService.closeConversationAndAdminVisitorRelationAndProcessQueue(conversation.getVisitorId(), conversation.getAdminId(), conversation.getMerchantId());
        // 发送结束消息
        PersonMessageUtil.sendEndMessage(conversation);
        // 触发排队逻辑
        log.info("自动结束对话 - conversationId: {}, reason: {}", conversation.getConversationId(), reason);
    }

    /**
     * 转接到其他客服
     */
    private void transferToAnotherService(ImConversationVo conversation, String reason) {
        // 查找可用的其他客服

        ImMerchantAdministratorVo admin = conversationService.autoAssignService(conversation.getConversationId(),
            conversation.getMerchantId(), conversation.getMerchantChannelId(), conversation.getAdminId());

        if (admin != null) {
            // 更新会话的客服
            Long adminId = admin.getAdminId();
            conversationService.updateConversationAdmin(conversation.getConversationId(), admin);
            // 所有的消息更新到最新的客服id
            messageService.updateMessageAdminId(conversation.getVisitorId(), conversation.getConversationId(), adminId);
            // 保存数据访客与客服的关系
            adminVisitorRelationService.createOrUpdateGetAdminVisitorRelation(adminId, conversation.getVisitorId(), conversation.getMerchantId(), conversation.getMerchantChannelId());
            // 记录转接次数
            incrementTransferCount(conversation.getConversationId());

            log.info("自动转接成功 - conversationId: {}, oldAdmin: {}, newAdmin: {}, reason: {}",
                conversation.getConversationId(), conversation.getAdminId(), adminId, reason);
        }
    }

    /**
     * 检查客服在线状态
     */
    private boolean checkServiceOnlineStatus(Long adminId, Long merchantId) {
        String sessionKey = SendMessageUtil.buildSessionKey(merchantId, adminId);
        return WebSocketSessionHolder.existSession(sessionKey);
    }

    /**
     * 获取转接次数
     */
    private int getTransferCount(Long conversationId) {
        String key = "conversation:transfer:count:" + conversationId;
        String countStr = RedisUtils.getCacheObject(key);
        return countStr != null ? Integer.parseInt(countStr) : 0;
    }

    /**
     * 增加转接次数
     */
    private void incrementTransferCount(Long conversationId) {
        String key = "conversation:transfer:count:" + conversationId;
        RedisUtils.increment(key);
        RedisUtils.expire(key, Duration.ofDays(1));
    }

}
