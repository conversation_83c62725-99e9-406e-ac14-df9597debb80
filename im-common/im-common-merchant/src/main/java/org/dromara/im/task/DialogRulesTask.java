package org.dromara.im.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.websocket.holder.WebSocketSessionHolder;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.MessageEvent;
import org.dromara.im.constans.MessageType;
import org.dromara.im.domain.dto.ChannelDialogRulesDto;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMerchantChannelVo;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImMerchantChannelService;
import org.dromara.im.service.IImMessageService;
import org.dromara.im.websocket.service.WebSocketMessageRouter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 对话规则定时任务
 * 处理自动结束对话、自动转接等规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "im.dialog.rules.enabled", havingValue = "true", matchIfMissing = true)
public class DialogRulesTask {

    private final IImConversationService conversationService;
    private final IImMerchantChannelService channelService;
    private final IImMessageService messageService;
    private final RedisTemplate<String, String> redisTemplate;

    /**
     * 检查自动结束对话规则
     * 每分钟执行一次
     */
    @Scheduled(fixedDelay = 60000)
    public void checkAutoEndDialog() {
        try {
            log.debug("开始检查自动结束对话规则");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();

            for (ImConversationVo conversation : activeConversations) {
                ChannelDialogRulesDto rules = getDialogRules(conversation.getMerchantChannelId());

                if ("1".equals(rules.getAutoEndDialog())) {
                    // 获取最后一条消息时间
                    Long lastMessageTime = messageService.getLastMessageTime(conversation.getConversationId());

                    if (lastMessageTime != null) {
                        long endTime = lastMessageTime + rules.getAutoEndDialogTime() * 60 * 1000L;


                        if (endTime < System.currentTimeMillis()) {
                            endDialog(conversation, "自动结束：对话超时 " + rules.getAutoEndDialogTime() + " 分钟");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查自动结束对话规则失败", e);
        }
    }

    /**
     * 检查顾客离线自动结束规则
     * 每30秒执行一次
     */
    @Scheduled(fixedDelay = 30000)
    public void checkCustomerOfflineAutoEnd() {
        try {
            log.debug("开始检查顾客离线自动结束规则");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();

            for (ImConversationVo conversation : activeConversations) {
                ChannelDialogRulesDto rules = getDialogRules(conversation.getMerchantChannelId());

                if ("1".equals(rules.getCustomerOfflineAutoEnd())) {
                    String key = getVisitorActivityRedisKey(conversation.getVisitorId());
                    String lastActiveStr = redisTemplate.opsForValue().get(key);

                    if (lastActiveStr != null) {
                        long lastActive = Long.parseLong(lastActiveStr);
                        long secondsOffline = (System.currentTimeMillis() - lastActive) / 1000;

                        if (secondsOffline >= rules.getCustomerOfflineTimeout()) {
                            endDialog(conversation, "顾客离线超时 " + rules.getCustomerOfflineTimeout() + " 秒");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查顾客离线自动结束规则失败", e);
        }
    }

    /**
     * 检查对话自动转接规则
     * 每30秒执行一次
     */
    @Scheduled(fixedDelay = 30000)
    public void checkDialogAutoTransfer() {
        try {
            log.debug("开始检查对话自动转接规则");

            // 检查等待中的会话（首条消息超时转接）
            List<ImConversationVo> waitingConversations = conversationService.findWaitingConversations();

            for (ImConversationVo conversation : waitingConversations) {
                ChannelDialogRulesDto rules = getDialogRules(conversation.getMerchantChannelId());

                if ("1".equals(rules.getDialogAutoTransfer())) {
                    long secondsSinceCreated = ChronoUnit.SECONDS.between(null, LocalDateTime.now());

                    if (secondsSinceCreated >= rules.getFirstMessageTimeout()) {
                        int transferCount = getTransferCount(conversation.getConversationId());
                        if (transferCount < rules.getMaxTransferTimes()) {
                            transferToAnotherService(conversation, "首条消息超时转接");
                        } else {
                            endDialog(conversation, "超过最大转接次数 " + rules.getMaxTransferTimes());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查对话自动转接规则失败", e);
        }
    }

    /**
     * 检查客服离线自动转接规则
     * 每分钟执行一次
     */
    @Scheduled(fixedDelay = 60000)
    public void checkServiceOfflineAutoTransfer() {
        try {
            log.debug("开始检查客服离线自动转接规则");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();

            for (ImConversationVo conversation : activeConversations) {
                if (conversation.getAdminId() == null) continue;

                ChannelDialogRulesDto rules = getDialogRules(conversation.getMerchantChannelId());

                if ("1".equals(rules.getServiceOfflineAutoTransfer())) {
                    boolean isServiceOnline = checkServiceOnlineStatus(conversation.getAdminId(), conversation.getMerchantId());

                    if (!isServiceOnline) {
                        int transferCount = getTransferCount(conversation.getConversationId());
                        if (transferCount < rules.getMaxTransferTimes()) {
                            transferToAnotherService(conversation, "客服离线自动转接");
                        } else {
                            endDialog(conversation, "客服离线且超过最大转接次数");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查客服离线自动转接规则失败", e);
        }
    }

    /**
     * 检查顾客消息间隔规则
     * 每分钟执行一次
     */
    @Scheduled(fixedDelay = 60000)
    public void checkCustomerMessageInterval() {
        try {
            log.debug("开始检查顾客消息间隔规则");

            List<ImConversationVo> activeConversations = conversationService.findAllActiveConversations();

            for (ImConversationVo conversation : activeConversations) {
                ChannelDialogRulesDto rules = getDialogRules(conversation.getMerchantChannelId());

                if (rules.getCustomerMessageInterval() != null && rules.getCustomerMessageInterval() > 0) {
                    String key = getVisitorLastSendMessageRedisKey(conversation.getVisitorId());
                    String lastMessageStr = redisTemplate.opsForValue().get(key);

                    if (lastMessageStr != null) {
                        long lastMessageTime = Long.parseLong(lastMessageStr);
                        long secondsSinceLastMessage = (System.currentTimeMillis() - lastMessageTime) / 1000;

                        if (secondsSinceLastMessage >= rules.getCustomerMessageInterval()) {
                            int transferCount = getTransferCount(conversation.getConversationId());
                            if (transferCount < rules.getMaxTransferTimes()) {
                                transferToAnotherService(conversation, "顾客长时间未发送消息");
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查顾客消息间隔规则失败", e);
        }
    }

    /**
     * 记录顾客活跃状态
     */
    public void recordCustomerActivity(String visitorId) {
        String key = getVisitorActivityRedisKey(visitorId);
        redisTemplate.opsForValue().set(key, String.valueOf(System.currentTimeMillis()),
            Duration.ofHours(24));
    }

    private static String getVisitorActivityRedisKey(String visitorId) {
        return "visitor:last_active:" + visitorId;
    }

    /**
     * 记录顾客最后发送消息时间
     */
    public void recordCustomerMessage(String visitorId) {
        String key = getVisitorLastSendMessageRedisKey(visitorId);
        redisTemplate.opsForValue().set(key, String.valueOf(System.currentTimeMillis()),
            Duration.ofHours(24));
    }

    private static String getVisitorLastSendMessageRedisKey(String visitorId) {
        return "visitor:last_message:" + visitorId;
    }

    /**
     * 获取对话规则
     */
    private ChannelDialogRulesDto getDialogRules(Long channelId) {
        String cacheKey = "channel:dialog:rules:" + channelId;
        String cachedRules = redisTemplate.opsForValue().get(cacheKey);

        if (cachedRules != null) {
            try {
                return JsonUtils.parseObject(cachedRules, ChannelDialogRulesDto.class);
            } catch (Exception e) {
                log.warn("解析缓存的对话规则失败", e);
            }
        }

        // 从数据库获取
        ImMerchantChannelVo channel = channelService.queryById(channelId);
        ChannelDialogRulesDto rules = null;
        if (channel != null) {
            channel.buildChannelDialogRulesDto();
            rules = channel.getChannelDialogRulesDto();
        }
        return rules;
    }

    /**
     * 结束对话
     */
    private void endDialog(ImConversationVo conversation, String reason) {
        conversationService.updateConversationStatus(conversation.getConversationId(), "closed");
        sendEndDialogNotification(conversation, reason);
        log.info("自动结束对话 - conversationId: {}, reason: {}", conversation.getConversationId(), reason);
    }

    /**
     * 转接到其他客服
     */
    private void transferToAnotherService(ImConversationVo conversation, String reason) {
        // 查找可用的其他客服
        Long newAdminId = findAvailableAdmin(conversation.getMerchantId(),
            conversation.getMerchantChannelId(), conversation.getAdminId());

        if (newAdminId != null) {
            // 更新会话的客服
            conversationService.updateConversationAdmin(conversation.getConversationId(), newAdminId);

            // 记录转接次数
            incrementTransferCount(conversation.getConversationId());

            // 发送转接通知
            sendTransferNotification(conversation, newAdminId, reason);

            log.info("自动转接成功 - conversationId: {}, oldAdmin: {}, newAdmin: {}, reason: {}",
                conversation.getConversationId(), conversation.getAdminId(), newAdminId, reason);
        } else {
            log.warn("无可用客服进行转接 - conversationId: {}", conversation.getConversationId());
            endDialog(conversation, reason + "（无可用客服）");
        }
    }

    /**
     * 查找可用客服
     */
    private Long findAvailableAdmin(Long merchantId, Long channelId, Long excludeAdminId) {
        // TODO: 实现查找可用客服的逻辑
        // 可以根据客服的在线状态、当前会话数量等条件来选择
        return null;
    }

    /**
     * 检查客服在线状态
     */
    private boolean checkServiceOnlineStatus(Long adminId, Long merchantId) {
        String sessionKey = WebSocketMessageRouter.buildSessionKey(merchantId, adminId);
        return WebSocketSessionHolder.existSession(sessionKey);
    }

    /**
     * 获取转接次数
     */
    private int getTransferCount(Long conversationId) {
        String key = "conversation:transfer:count:" + conversationId;
        String countStr = redisTemplate.opsForValue().get(key);
        return countStr != null ? Integer.parseInt(countStr) : 0;
    }

    /**
     * 增加转接次数
     */
    private void incrementTransferCount(Long conversationId) {
        String key = "conversation:transfer:count:" + conversationId;
        redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, Duration.ofDays(1));
    }

    /**
     * 发送对话结束通知
     */
    private void sendEndDialogNotification(ImConversationVo conversation, String reason) {
        // 通知访客
        sendNotificationToVisitor(conversation.getVisitorId(), "对话已结束", reason);
        // 通知客服
        if (conversation.getAdminId() != null) {
            sendNotificationToService(conversation.getAdminId(), conversation.getMerchantId(), "对话已结束", reason);
        }
    }

    /**
     * 发送转接通知
     */
    private void sendTransferNotification(ImConversationVo conversation, Long newAdminId, String reason) {
        // 通知访客
        sendNotificationToVisitor(conversation.getVisitorId(), "对话已转接", "正在为您安排新的客服");

        // 通知原客服
        if (conversation.getAdminId() != null) {
            sendNotificationToService(conversation.getAdminId(), conversation.getMerchantId(), "对话已转出", reason);
        }

        // 通知新客服
        sendNotificationToService(newAdminId, conversation.getMerchantId(), "收到转接对话", reason);
    }

    /**
     * 发送通知给访客
     */
    private void sendNotificationToVisitor(String visitorId, String title, String message) {
        try {
            JSONObject data = new JSONObject();
            data.put("type", MessageType.SYSTEM_NOTIFICATION);
            data.put("title", title);
            data.put("content", message);
            data.put("messageSendTime", System.currentTimeMillis());

            JSONObject response = new JSONObject();
            response.put("event", MessageEvent.VISITOR_EVENT.getCode());
            response.put("data", data);

            String sessionKey = "visitor:" + visitorId;
            WebSocketUtils.sendMessage(sessionKey, response.toString());
        } catch (Exception e) {
            log.error("发送通知给访客失败 - visitorId: {}", visitorId, e);
        }
    }

    /**
     * 发送通知给客服
     */
    private void sendNotificationToService(Long adminId, Long merchantId, String title, String message) {
        try {
            JSONObject data = new JSONObject();
            data.put("type", MessageType.SYSTEM_NOTIFICATION);
            data.put("title", title);
            data.put("content", message);
            data.put("messageSendTime", System.currentTimeMillis());

            JSONObject response = new JSONObject();
            response.put("event", MessageEvent.SERVICE_EVENT.getCode());
            response.put("data", data);

            String sessionKey = "merchant:" + merchantId + ":admin:" + adminId;
            WebSocketUtils.sendMessage(sessionKey, response.toString());
        } catch (Exception e) {
            log.error("发送通知给客服失败 - adminId: {}", adminId, e);
        }
    }
}
