package org.dromara.im.utils.message;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.MessageEvent;
import org.dromara.im.constans.MessageType;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.dto.ConversationQueuesDto;
import org.dromara.im.domain.dto.message.ChatMessageDTO;
import org.dromara.im.domain.dto.message.ChatTemplateMessage;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.service.IImMerchantService;
import org.dromara.im.utils.SendMessageUtil;

@Slf4j
public class QueueMessageUtil extends SendMessageUtil {

    //====================================================== 对话排队消息 start ======================================================


    /**
     * 发送排队等待消息
     */
    public static void sendQueueWaitingMessage(ImConversationVo conversation, int queuePosition) {
        try {
            ConversationQueuesDto conversationQueues = getConversationQueuesDto(conversation);
            if (ConversationQueuesDto.enableQueue(conversationQueues)) {
                String queueWaitingMessage = conversationQueues.getQueueWaitingMessage();
                String finalMessage = queueWaitingMessage.replace("${queue}", String.valueOf(queuePosition));
                String visitorSessionKey = buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());
                sendQueueMessage(visitorSessionKey, MessageType.QUEUE_WAITING, finalMessage);
            }

        } catch (Exception e) {
            log.error("发送排队等待消息失败", e);
        }
    }


    /**
     * 发送排队已满消息
     */
    public static void sendQueueFullMessage(ImConversationVo conversation) {
        try {
            ConversationQueuesDto conversationQueues = getConversationQueuesDto(conversation);
            if (ConversationQueuesDto.enableQueue(conversationQueues)) {
                String visitorSessionKey = buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());

                sendQueueMessage(visitorSessionKey, MessageType.QUEUE_FULL, conversationQueues.getQueueFullMessage());
            }

        } catch (Exception e) {
            log.error("发送排队已满消息失败", e);
        }
    }


    private static void sendQueueMessage(String sessionKey, String messageType, String message) {
        ChatMessageDTO chat = ChatMessageDTO.buildChat();
        chat.setType(messageType);
        chat.setContent(message);
        ChatTemplateMessage chatTemplateMessage = ChatTemplateMessage.builder()
            .event(MessageEvent.VISITOR__TO_SERVICE_EVENT.getCode())
            .data(chat)
            .build();
        WebSocketUtils.sendMessage(sessionKey, JSONUtil.toJsonStr(chatTemplateMessage));
    }

    /**
     * 排队接待成功提示
     */
    public static void sendQueueSuccessToVisitor(ImConversationVo conversation) {
        try {
            ConversationQueuesDto conversationQueues = getConversationQueuesDto(conversation);
            if (ConversationQueuesDto.enableQueue(conversationQueues) && ConversationQueuesDto.enableQueueSuccessNotification(conversationQueues)) {
                String visitorSessionKey = buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());
                sendQueueMessage(visitorSessionKey, MessageType.QUEUE_SUCCESS, conversationQueues.getQueueSuccessMessage());
                log.info("发送分配成功消息给访客 - visitorId: {}", conversation.getVisitorId());
            }

        } catch (Exception e) {
            log.error("发送分配成功消息失败 - visitorId: {}", conversation.getVisitorId(), e);
        }
    }


    //====================================================== 对话排队消息 end ======================================================

    private static ConversationQueuesDto getConversationQueuesDto(ImConversationVo conversation) {
        IImMerchantService merchantService = SpringUtils.getBean(IImMerchantService.class);
        ImMerchant merchant = merchantService.queryImMerchant(conversation.getMerchantId());
        return merchantService.queryConversationQueues(merchant);
    }

}
