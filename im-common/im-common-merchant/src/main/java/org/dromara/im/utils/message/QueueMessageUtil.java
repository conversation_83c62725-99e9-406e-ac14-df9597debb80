package org.dromara.im.utils.message;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.MessageEvent;
import org.dromara.im.constans.MessageType;
import org.dromara.im.domain.dto.message.ChatMessageDTO;
import org.dromara.im.domain.dto.message.ChatTemplateMessage;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.utils.SendMessageUtil;

@Slf4j
public class QueueMessageUtil extends SendMessageUtil {

    //====================================================== 对话排队消息 start ======================================================

    /**
     * 排队接待成功提示
     */
    public static void sendQueueSuccessToVisitor(ImConversationVo conversation) {
        try {
            String visitorSessionKey = buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());
            sendQueueMessage(visitorSessionKey, MessageType.QUEUE_SUCCESS, "让您久等了，您已成功接入对话，客服正在为您提供服务，");

            log.info("发送分配成功消息给访客 - visitorId: {}", conversation.getVisitorId());
        } catch (Exception e) {
            log.error("发送分配成功消息失败 - visitorId: {}", conversation.getVisitorId(), e);
        }
    }

    /**
     * 发送排队等待消息
     */
    public static void sendQueueWaitingMessage(ImConversationVo conversation, String message, int queuePosition) {
        try {
            if (message == null || message.trim().isEmpty()) {
                message = "您前面还有 ${queue} 位访客在排队，请稍候...";
            }
            String finalMessage = message.replace("${queue}", String.valueOf(queuePosition));
            String visitorSessionKey = buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());
            sendQueueMessage(visitorSessionKey, MessageType.QUEUE_WAITING, finalMessage);
        } catch (Exception e) {
            log.error("发送排队等待消息失败", e);
        }
    }


    /**
     * 发送排队已满消息
     */
    public static void sendQueueFullMessage(ImConversationVo conversation, String message) {
        try {
            if (message == null || message.trim().isEmpty()) {
                message = "当前咨询人数较多，请稍后再试";
            }
            String visitorSessionKey = buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());
            sendQueueMessage(visitorSessionKey, MessageType.QUEUE_FULL, message);
        } catch (Exception e) {
            log.error("发送排队已满消息失败", e);
        }
    }


    private static void sendQueueMessage(String sessionKey, String messageType, String message) {
        ChatMessageDTO chat = ChatMessageDTO.buildChat();
        chat.setType(messageType);
        chat.setContent(message);
        ChatTemplateMessage chatTemplateMessage = ChatTemplateMessage.builder()
            .event(MessageEvent.VISITOR__TO_SERVICE_EVENT.getCode())
            .data(chat)
            .build();
        WebSocketUtils.sendMessage(sessionKey, JSONUtil.toJsonStr(chatTemplateMessage));
    }

    //====================================================== 对话排队消息 end ======================================================

}
