package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImMerchant;

/**
 * 商户业务对象 im_merchant
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImMerchant.class, reverseConvertGenerate = false)
public class ImMerchantBo extends BaseEntity {

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户编码
     */
    private String merchantCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 对话上限
     */
    private Long chatNumber;


    /**
     * 0->体验版;1->标准版;2->专业版;3->集团版
     */
    private String packageType;

    /**
     * 过期时间
     */
    private Long expiredTime;

    /**
     * 已使用量
     */
    private Long useTranslateNumber;

    /**
     * 总量
     */
    private Long translateNumber;

    /**
     * 渠道数量
     */
    private Long channelNumber;

    /**
     * 坐席数量
     */
    private Long seatsCount;


    public static ImMerchantBo initMerchant(String companyName) {
        ImMerchantBo merchantBo = new ImMerchantBo();
        merchantBo.setCompanyName(companyName);
        long expiredTime1 = System.currentTimeMillis();
        merchantBo.setMerchantCode("MERCHANT_" + expiredTime1);
        merchantBo.setPackageType("0");
        merchantBo.setExpiredTime(expiredTime1 + 7L * 24 * 60 * 60 * 1000);
        merchantBo.setTranslateNumber(10000L);
        merchantBo.setUseTranslateNumber(0L);
        merchantBo.setChannelNumber(2L);
        merchantBo.setChatNumber(2L);
        merchantBo.setSeatsCount(2L);
        return merchantBo;
    }

}
