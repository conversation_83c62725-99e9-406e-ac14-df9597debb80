package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.im.domain.ImConversation;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 会话关系视图对象 im_conversation
 *
 * <AUTHOR> Li
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImConversation.class)
public class ImConversationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会话唯一id
     */
    @ExcelProperty(value = "会话唯一id")
    private Long conversationId;

    /**
     * 游客ID
     */
    @ExcelProperty(value = "游客ID")
    private String visitorId;

    /**
     * 游客姓名
     */
    @ExcelProperty(value = "游客姓名")
    private String visitorName;

    /**
     * 客服ID
     */
    @ExcelProperty(value = "客服ID")
    private Long adminId;

    /**
     * 客服头像
     */
    private String visitorAvatar;

    /**
     * 客服名称
     */
    @ExcelProperty(value = "客服名称")
    private String adminName;

    /**
     * 客服头像
     */
    private String adminAvatar;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 渠道ID
     */
    @ExcelProperty(value = "渠道ID")
    private Long merchantChannelId;

    /**
     * 会话状态: waiting=等待分配, active=进行中, closed=已结束, transferred=已转接
     *
     * @see org.dromara.im.constans.ConversationConstants
     */
    @ExcelProperty(value = "会话状态: waiting=等待分配, active=进行中, closed=已结束, transferred=已转接")
    private String status;

    /**
     * 是否自动分配客服: 0=手动, 1=自动;3->转接
     */
    @ExcelProperty(value = "是否自动分配客服: 0=手动, 1=自动;3->转接")
    private String autoAssigned;

    /**
     * 满意度评分 1-5
     */
    @ExcelProperty(value = "满意度评分 1-5")
    private Long rating;

    /**
     * 结束原因
     */
    @ExcelProperty(value = "结束原因")
    private String closedReason;

    /**
     * 游客反馈
     */
    @ExcelProperty(value = "游客反馈")
    private String feedback;

    /**
     * 最后消息时间
     */
    @ExcelProperty(value = "最后消息时间")
    private Long lastMessageTime;

    /**
     * 最后一条消息内容（用于列表显示）
     */
    @ExcelProperty(value = "最后一条消息内容", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "用=于列表显示")
    private String lastMessageContent;

    /**
     * 消息总数
     */
    @ExcelProperty(value = "消息总数")
    private Long messageCount;

    /**
     * 未读消息数
     */
    @ExcelProperty(value = "未读消息数")
    private Long unreadCount;

    /**
     * 游客最后读取时间
     */
    @ExcelProperty(value = "游客最后读取时间")
    private Date visitorReadTime;

    /**
     * 客服最后读取时间
     */
    @ExcelProperty(value = "客服最后读取时间")
    private Date adminReadTime;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 评价时间
     */
    private Long evaluateTime;


    private Date createTime;


}
