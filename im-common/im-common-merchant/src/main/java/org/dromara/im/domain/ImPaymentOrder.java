package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 支付订单对象 im_payment_order
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_payment_order")
public class ImPaymentOrder extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道翻译id
     */
    @TableId(value = "payment_order_id")
    private Long paymentOrderId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 订单类型;0->商户订单;1->翻译订单
     */
    private String orderType;

    /**
     * 订单状态0->待支付;1->已支付;2->已失效
     */
    private String orderStatus;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付方式;0->USDT
     */
    private String paymentMethod;

    /**
     * 支付详情
     */
    private String orderDetail;

    /**
     * 扩展字段
     */
    private String extra;
    /**
     * 租户编号
     */
    private String tenantId;


}
