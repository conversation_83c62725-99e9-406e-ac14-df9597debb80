package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 渠道配置对象 im_channel_config
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@EqualsAndHashCode()
@TableName("im_channel_config")
public class ImChannelConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道配置ID
     */
    @TableId(value = "channel_config_id")
    private Long channelConfigId;

    /**
     * 渠道类型，例如 网页+APP、Telegram Bot
     */
    private String channelType;

    /**
     * 渠道编码，唯一标识，如 sa0KZ3
     */
    private String channelCode;

    /**
     * 渠道名称，如 bem渠道、tgben
     */
    private String channelName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;


}
