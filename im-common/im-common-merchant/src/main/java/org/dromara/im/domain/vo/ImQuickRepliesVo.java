package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImQuickReplies;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 团队快捷回复视图对象 im_quick_replies
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImQuickReplies.class)
public class ImQuickRepliesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 快捷回复分组id
     */
    @ExcelProperty(value = "快捷回复分组id")
    private Long quickRepliesId;

    /**
     * 管理员ID
     */
    private Long adminId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道id
     */
    @ExcelProperty(value = "商户渠道id")
    private Long merchantChannelId;

    /**
     * 商户渠道id
     */
    @ExcelProperty(value = "商户渠道id")
    private Long quickRepliesGroupId;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 快捷回复分组名称
     */
    private String quickRepliesGroupName;

    private Date createTime;

    private Date updateTime;

    /**
     * 0->个人;1->团队
     */
    private String type;


}
