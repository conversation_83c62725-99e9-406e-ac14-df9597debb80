package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.domain.dto.ConversationQueuesDto;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.im.mapper.ImMerchantMapper;
import org.dromara.im.service.IImMerchantService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商户Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImMerchantServiceImpl implements IImMerchantService {

    private final ImMerchantMapper baseMapper;

    /**
     * 查询商户
     *
     * @param merchantId 主键
     * @return 商户
     */
    @Override
    public ImMerchantVo queryById(Long merchantId) {
        return baseMapper.selectVoById(merchantId);
    }

    /**
     * 分页查询商户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户分页列表
     */
    @Override
    public TableDataInfo<ImMerchantVo> queryPageList(ImMerchantBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMerchant> lqw = buildQueryWrapper(bo);
        Page<ImMerchantVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商户列表
     *
     * @param bo 查询条件
     * @return 商户列表
     */
    @Override
    public List<ImMerchantVo> queryList(ImMerchantBo bo) {
        LambdaQueryWrapper<ImMerchant> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMerchant> buildQueryWrapper(ImMerchantBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMerchant> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImMerchant::getMerchantId);
        lqw.eq(StringUtils.isNotBlank(bo.getMerchantCode()), ImMerchant::getMerchantCode, bo.getMerchantCode());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), ImMerchant::getCompanyName, bo.getCompanyName());
        return lqw;
    }

    /**
     * 新增商户
     *
     * @param bo 商户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMerchantBo bo) {
        ImMerchant add = MapstructUtils.convert(bo, ImMerchant.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMerchantId(add.getMerchantId());
        }
        return flag;
    }

    /**
     * 修改商户
     *
     * @param bo 商户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMerchantBo bo) {
        ImMerchant update = MapstructUtils.convert(bo, ImMerchant.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMerchant entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除商户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void conversationQueues(Long merchantId, ConversationQueuesDto conversationQueuesDto) {
        baseMapper.update(new LambdaUpdateWrapper<>(ImMerchant.class)
            .set(ImMerchant::getConversationQueues, JsonUtils.toJsonString(conversationQueuesDto))
            .eq(ImMerchant::getMerchantId, merchantId)
        );
    }

    @Override
    public ConversationQueuesDto queryConversationQueues(Long merchantId) {
        ImMerchant imMerchant = baseMapper.selectById(merchantId);
        if (StringUtils.isNotBlank(imMerchant.getConversationQueues())) {
            return JsonUtils.parseObject(imMerchant.getConversationQueues(), ConversationQueuesDto.class);
        }
        return ConversationQueuesDto.getDefault();
    }
}
