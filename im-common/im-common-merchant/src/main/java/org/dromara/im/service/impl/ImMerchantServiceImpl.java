package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.exception.ImException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.constans.MerchantRoleType;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.domain.ImMerchantChannel;
import org.dromara.im.domain.ImMerchantChannelRelation;
import org.dromara.im.domain.bo.AddMerchantBo;
import org.dromara.im.domain.bo.ImMerchantAdministratorBo;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.domain.bo.ImPaymentOrderBo;
import org.dromara.im.domain.dto.*;
import org.dromara.im.domain.vo.ImChatPackageVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.im.domain.vo.ImTranslationPackageVo;
import org.dromara.im.mapper.*;
import org.dromara.im.service.IImMerchantService;
import org.dromara.im.service.IImPaymentOrderService;
import org.dromara.im.utils.Password;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImMerchantServiceImpl implements IImMerchantService {

    private final ImMerchantMapper baseMapper;

    private final ImMerchantAdministratorMapper merchantAdministratorMapper;

    private final ImMerchantChannelRelationMapper merchantChannelRelationMapper;


    private final ImMerchantChannelMapper merchantChannelMapper;

    private final ImTranslationPackageMapper translationPackageMapper;

    private final ImChatPackageMapper chatPackageMapper;

    private final IImPaymentOrderService paymentOrderService;
    /**
     * 查询商户
     *
     * @param merchantId 主键
     * @return 商户
     */
    @Override
    public ImMerchantVo queryById(Long merchantId) {
        return baseMapper.selectVoById(merchantId);
    }

    /**
     * 分页查询商户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户分页列表
     */
    @Override
    public TableDataInfo<ImMerchantVo> queryPageList(ImMerchantBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMerchant> lqw = buildQueryWrapper(bo);
        Page<ImMerchantVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<ImMerchantVo> records = result.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> merchantIdList = records.stream()
                .map(ImMerchantVo::getMerchantId)
                .toList();
            if (CollectionUtils.isNotEmpty(merchantIdList)) {
                LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(ImMerchantAdministrator::getMerchantId, merchantIdList);
                wrapper.eq(ImMerchantAdministrator::getRoleType, "1");
                List<ImMerchantAdministratorVo> imMerchantAdministratorVos = merchantAdministratorMapper.selectVoList(wrapper);
                Map<Long, ImMerchantAdministratorVo> administratorVoMap = imMerchantAdministratorVos.stream()
                    .collect(Collectors.toMap(ImMerchantAdministratorVo::getMerchantId, v -> v));
                records.forEach(record -> {
                    ImMerchantAdministratorVo imMerchantAdministratorVo = administratorVoMap.get(record.getMerchantId());
                    if (Objects.nonNull(imMerchantAdministratorVo)) {
                        record.setEmail(imMerchantAdministratorVo.getEmail());
                        record.setPassword(imMerchantAdministratorVo.getPassword());
                    }
                });
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商户列表
     *
     * @param bo 查询条件
     * @return 商户列表
     */
    @Override
    public List<ImMerchantVo> queryList(ImMerchantBo bo) {
        LambdaQueryWrapper<ImMerchant> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMerchant> buildQueryWrapper(ImMerchantBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMerchant> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImMerchant::getMerchantId);
        lqw.eq(StringUtils.isNotBlank(bo.getMerchantCode()), ImMerchant::getMerchantCode, bo.getMerchantCode());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), ImMerchant::getCompanyName, bo.getCompanyName());
        return lqw;
    }

    /**
     * 新增商户
     *
     * @param bo 商户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMerchantBo bo) {
        ImMerchant add = MapstructUtils.convert(bo, ImMerchant.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMerchantId(add.getMerchantId());
        }
        return flag;
    }

    /**
     * 修改商户
     *
     * @param bo 商户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMerchantBo bo) {
        ImMerchant update = MapstructUtils.convert(bo, ImMerchant.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMerchant entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除商户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void conversationQueues(Long merchantId, ConversationQueuesDto conversationQueuesDto) {
        baseMapper.update(new LambdaUpdateWrapper<>(ImMerchant.class)
            .set(ImMerchant::getConversationQueues, JsonUtils.toJsonString(conversationQueuesDto))
            .eq(ImMerchant::getMerchantId, merchantId)
        );
    }

    @Override
    public ConversationQueuesDto queryConversationQueues(ImMerchantVo imMerchant) {

        if (StringUtils.isNotBlank(imMerchant.getConversationQueues())) {
            return JsonUtils.parseObject(imMerchant.getConversationQueues(), ConversationQueuesDto.class);
        }
        return ConversationQueuesDto.getDefault();
    }

    @Override
    public ImMerchantVo queryImMerchant(Long merchantId) {
        return baseMapper.selectVoById(merchantId);
    }

    @Override
    public void add(AddMerchantBo bo) {
        try {
            LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ImMerchantAdministrator::getEmail, bo.getEmail());
            boolean existEmailNum = merchantAdministratorMapper.selectCount(wrapper) > 0;
            if (existEmailNum) {
                throw new ImException("邮箱已存在");
            }

            ImMerchantBo merchant = ImMerchantBo.initMerchant(bo.getCompanyName());
            String nickname = bo.getEmail().substring(0, bo.getEmail().indexOf("@"));
            ImMerchantAdministratorBo admin = ImMerchantAdministratorBo.create(bo.getEmail(), nickname,
                new Password(bo.getPassword()), null, MerchantRoleType.MERCHANT_ADMIN.getRoleId(), null);
            admin.setMessagePrompt(JsonUtils.toJsonString(MessagePromptDto.getDefault()));
            // 保存对应的商户&超级管理员
            ImMerchantAdministratorVo vo = save(merchant, admin);
            // 创建默认渠道
            ImMerchantChannel defaultMerchantChannel = createDefaultMerchantChannel(merchant);
            // 绑定超级管理员的默认渠道
            saveMerchantChannelRelation(vo.getAdminId(), defaultMerchantChannel.getMerchantChannelId());
            // 处理坐席位订单
            PayRequest payRequest = handlerChatPackage(bo, merchant);
            // 处理翻译订单
            handlerTranslatePackage(bo, payRequest, merchant);
        } catch (ImException e) {
            log.error("注册失败", e);
            throw new ImException("注册失败，请联系管理员");
        }

    }

    private void handlerTranslatePackage(AddMerchantBo bo, PayRequest payRequest, ImMerchantBo merchant) {
        PayRequest translateRequest = new PayRequest();
        translateRequest.setOrderType("1");
        translateRequest.setTranslatePackageId(bo.getTranslatePackageId());
        ImPaymentOrderBo translateOrderBo = paymentOrderService.create(payRequest, merchant.getMerchantId());
        paymentOrderService.paySuccess(translateOrderBo);
    }

    private PayRequest handlerChatPackage(AddMerchantBo bo, ImMerchantBo merchant) {
        PayRequest payRequest = new PayRequest();
        payRequest.setOrderType("0");
        payRequest.setChatPackageId(bo.getChatPackageId());
        payRequest.setSeatsCount(bo.getSeatsCount());
        ImPaymentOrderBo chatOrderBo = paymentOrderService.create(payRequest, merchant.getMerchantId());
        paymentOrderService.paySuccess(chatOrderBo);
        return payRequest;
    }

    /**
     * 根据计费周期和计费周期数量计算延长时间
     *
     * @param billingPeriod 计费周期数量
     * @param billingCycle 计费周期类型 (0-月付 2-年付)
     * @return 延长时间（毫秒）
     */
    private long calculateExtendTime(Integer billingPeriod, String billingCycle) {
        if (billingPeriod == null || billingPeriod <= 0) {
            log.warn("计费周期数量无效: {}", billingPeriod);
            return 0L;
        }

        if (billingCycle == null) {
            log.warn("计费周期类型为空，默认使用月付");
            billingCycle = "0";
        }

        long extendTime;

        switch (billingCycle) {
            case "0": // 月付
                // 每个计费周期 = 30天
                extendTime = billingPeriod * 30L * 24L * 60L * 60L * 1000L;
                log.info("月付计算：{} 个月 = {} 天", billingPeriod, billingPeriod * 30);
                break;
            case "1": // 月付
                // 每个计费周期 = 30天
                extendTime = billingPeriod * 30L * 24L * 60L * 60L * 1000L;
                log.info("月付计算：{} 个月 = {} 天", billingPeriod, billingPeriod * 30);
                break;
            case "2": // 年付
                // 每个计费周期 = 365天
                extendTime = billingPeriod * 365L * 24L * 60L * 60L * 1000L;
                log.info("年付计算：{} 年 = {} 天", billingPeriod, billingPeriod * 365);
                break;

            default:
                log.info("未知的计费周期类型: {}，默认使用月付", billingCycle);
                extendTime = billingPeriod * 30L * 24L * 60L * 60L * 1000L;
                break;
        }

        log.info("计费周期计算结果 - 周期数量: {}, 周期类型: {}, 延长时间: {} 毫秒", billingPeriod, billingCycle, extendTime);

        return extendTime;
    }


    public ImMerchantAdministratorVo save(ImMerchantBo merchant, ImMerchantAdministratorBo administratorBo) {
        ImMerchant merchantDO = MapstructUtils.convert(merchant, ImMerchant.class);

        if (merchant.getMerchantId() == null) {
            // 新增商户
            baseMapper.insert(merchantDO);
            assert merchantDO != null;
            merchant.setMerchantId(merchantDO.getMerchantId());
        }
        ImMerchantAdministrator adminDO = MapstructUtils.convert(administratorBo, ImMerchantAdministrator.class);
        assert adminDO != null;
        adminDO.setMerchantId(merchant.getMerchantId());
        merchantAdministratorMapper.insert(adminDO);
        administratorBo.setAdminId(adminDO.getAdminId());
        return merchantAdministratorMapper.selectVoById(adminDO.getAdminId());
    }

    private ImMerchantChannel createDefaultMerchantChannel(ImMerchantBo merchant) {
        ImMerchantChannel entity = new ImMerchantChannel();
        entity.setMerchantId(merchant.getMerchantId());
        entity.setMerchantChannelName("默认配置");
        entity.setDefaultChannel("1");
        entity.setAssignmentRules(JsonUtils.toJsonString(AssignmentRulesDto.getDefault()));
        entity.setDialogRules(JsonUtils.toJsonString(ChannelDialogRulesDto.getDefault()));
        merchantChannelMapper.insert(entity);
        return entity;
    }

    private void saveMerchantChannelRelation(Long adminId, Long merchantChannelId) {
        ImMerchantChannelRelation e = new ImMerchantChannelRelation();
        e.setAdminId(adminId);
        e.setMerchantChannelId(merchantChannelId);
        merchantChannelRelationMapper.insert(e);
    }
}
