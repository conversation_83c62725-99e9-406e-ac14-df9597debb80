package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.exception.ImException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.constans.MerchantRoleType;
import org.dromara.im.domain.*;
import org.dromara.im.domain.bo.AddMerchantBo;
import org.dromara.im.domain.bo.ImMerchantAdministratorBo;
import org.dromara.im.domain.bo.ImMerchantBo;
import org.dromara.im.domain.dto.AssignmentRulesDto;
import org.dromara.im.domain.dto.ChannelDialogRulesDto;
import org.dromara.im.domain.dto.ConversationQueuesDto;
import org.dromara.im.domain.dto.MessagePromptDto;
import org.dromara.im.domain.vo.ImChatPackageVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.im.domain.vo.ImTranslationPackageVo;
import org.dromara.im.mapper.*;
import org.dromara.im.service.IImMerchantService;
import org.dromara.im.utils.Password;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商户Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImMerchantServiceImpl implements IImMerchantService {

    private final ImMerchantMapper baseMapper;

    private final ImMerchantAdministratorMapper merchantAdministratorMapper;

    private final ImMerchantChannelRelationMapper merchantChannelRelationMapper;


    private final ImMerchantChannelMapper merchantChannelMapper;

    private final ImTranslationPackageMapper translationPackageMapper;

    private final ImChatPackageMapper chatPackageMapper;
    /**
     * 查询商户
     *
     * @param merchantId 主键
     * @return 商户
     */
    @Override
    public ImMerchantVo queryById(Long merchantId) {
        return baseMapper.selectVoById(merchantId);
    }

    /**
     * 分页查询商户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商户分页列表
     */
    @Override
    public TableDataInfo<ImMerchantVo> queryPageList(ImMerchantBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMerchant> lqw = buildQueryWrapper(bo);
        Page<ImMerchantVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<ImMerchantVo> records = result.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> merchantIdList = records.stream()
                .map(ImMerchantVo::getMerchantId)
                .toList();
            if (CollectionUtils.isNotEmpty(merchantIdList)) {
                LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(ImMerchantAdministrator::getMerchantId, merchantIdList);
                wrapper.eq(ImMerchantAdministrator::getRoleType, "1");
                List<ImMerchantAdministratorVo> imMerchantAdministratorVos = merchantAdministratorMapper.selectVoList(wrapper);
                Map<Long, ImMerchantAdministratorVo> administratorVoMap = imMerchantAdministratorVos.stream()
                    .collect(Collectors.toMap(ImMerchantAdministratorVo::getMerchantId, v -> v));
                records.forEach(record -> {
                    ImMerchantAdministratorVo imMerchantAdministratorVo = administratorVoMap.get(record.getMerchantId());
                    if (Objects.nonNull(imMerchantAdministratorVo)) {
                        record.setEmail(imMerchantAdministratorVo.getEmail());
                        record.setPassword(imMerchantAdministratorVo.getPassword());
                    }
                });
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商户列表
     *
     * @param bo 查询条件
     * @return 商户列表
     */
    @Override
    public List<ImMerchantVo> queryList(ImMerchantBo bo) {
        LambdaQueryWrapper<ImMerchant> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMerchant> buildQueryWrapper(ImMerchantBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMerchant> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImMerchant::getMerchantId);
        lqw.eq(StringUtils.isNotBlank(bo.getMerchantCode()), ImMerchant::getMerchantCode, bo.getMerchantCode());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), ImMerchant::getCompanyName, bo.getCompanyName());
        return lqw;
    }

    /**
     * 新增商户
     *
     * @param bo 商户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMerchantBo bo) {
        ImMerchant add = MapstructUtils.convert(bo, ImMerchant.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMerchantId(add.getMerchantId());
        }
        return flag;
    }

    /**
     * 修改商户
     *
     * @param bo 商户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMerchantBo bo) {
        ImMerchant update = MapstructUtils.convert(bo, ImMerchant.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMerchant entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除商户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void conversationQueues(Long merchantId, ConversationQueuesDto conversationQueuesDto) {
        baseMapper.update(new LambdaUpdateWrapper<>(ImMerchant.class)
            .set(ImMerchant::getConversationQueues, JsonUtils.toJsonString(conversationQueuesDto))
            .eq(ImMerchant::getMerchantId, merchantId)
        );
    }

    @Override
    public ConversationQueuesDto queryConversationQueues(ImMerchant imMerchant) {

        if (StringUtils.isNotBlank(imMerchant.getConversationQueues())) {
            return JsonUtils.parseObject(imMerchant.getConversationQueues(), ConversationQueuesDto.class);
        }
        return ConversationQueuesDto.getDefault();
    }

    @Override
    public ImMerchant queryImMerchant(Long merchantId) {
        return baseMapper.selectById(merchantId);
    }

    @Override
    public void add(AddMerchantBo bo) {
        try {
            LambdaQueryWrapper<ImMerchantAdministrator> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ImMerchantAdministrator::getEmail, bo.getEmail());
            boolean existEmailNum = merchantAdministratorMapper.selectCount(wrapper) > 0;
            if (existEmailNum) {
                throw new ImException("邮箱已存在");
            }

            ImMerchantBo merchant = ImMerchantBo.initMerchant(bo.getCompanyName());
            String nickname = bo.getEmail().substring(0, bo.getEmail().indexOf("@"));
            ImMerchantAdministratorBo admin = ImMerchantAdministratorBo.create(bo.getEmail(), nickname,
                new Password(bo.getPassword()), null, MerchantRoleType.MERCHANT_ADMIN.getRoleId(), null);
            admin.setMessagePrompt(JsonUtils.toJsonString(MessagePromptDto.getDefault()));
            // 保存对应的商户&超级管理员
            ImMerchantAdministratorVo vo = save(merchant, admin);
            // 创建默认渠道
            ImMerchantChannel defaultMerchantChannel = createDefaultMerchantChannel(merchant);
            // 绑定超级管理员的默认渠道
            saveMerchantChannelRelation(vo.getAdminId(), defaultMerchantChannel.getMerchantChannelId());
            // 处理会员的套餐
            handleChatPackage(bo, merchant);
            // 处理一下坐席套餐
            ImChatPackageVo chatPackageVo = chatPackageMapper.selectVoById(bo.getChatPackageId());
            // 处理翻译套餐
            ImTranslationPackageVo translationPackageVo = translationPackageMapper.selectVoById(bo.getTranslatePackageId());

            baseMapper.update(new LambdaUpdateWrapper<>(ImMerchant.class)
                // 对话数量
                .set(ImMerchant::getChatNumber, chatPackageVo.getChannelNumber())
                // 渠道数量
                .set(ImMerchant::getChannelNumber, chatPackageVo.getChatPackageId())
                // 翻译数量
                .set(ImMerchant::getTranslateNumber, translationPackageVo.getCharacterQuota())
                // 设置过期时间
                .set(ImMerchant::getExpiredTime, translationPackageVo.getCharacterQuota())
                .eq(ImMerchant::getMerchantId, merchant.getMerchantId())
            );

        } catch (ImException e) {
            log.error("注册失败", e);
            throw new ImException("注册失败，请联系管理员");
        }

    }

    private ImTranslationPackage handleTranslationPackage(AddMerchantBo bo) {
        return ;
    }

    private void handleChatPackage(AddMerchantBo bo, ImMerchantBo merchant) {

    }

    public ImMerchantAdministratorVo save(ImMerchantBo merchant, ImMerchantAdministratorBo administratorBo) {
        ImMerchant merchantDO = MapstructUtils.convert(merchant, ImMerchant.class);

        if (merchant.getMerchantId() == null) {
            // 新增商户
            baseMapper.insert(merchantDO);
            assert merchantDO != null;
            merchant.setMerchantId(merchantDO.getMerchantId());
        }
        ImMerchantAdministrator adminDO = MapstructUtils.convert(administratorBo, ImMerchantAdministrator.class);
        assert adminDO != null;
        adminDO.setMerchantId(merchant.getMerchantId());
        merchantAdministratorMapper.insert(adminDO);
        administratorBo.setAdminId(adminDO.getAdminId());
        return merchantAdministratorMapper.selectVoById(adminDO.getAdminId());
    }

    private ImMerchantChannel createDefaultMerchantChannel(ImMerchantBo merchant) {
        ImMerchantChannel entity = new ImMerchantChannel();
        entity.setMerchantId(merchant.getMerchantId());
        entity.setMerchantChannelName("默认配置");
        entity.setDefaultChannel("1");
        entity.setAssignmentRules(JsonUtils.toJsonString(AssignmentRulesDto.getDefault()));
        entity.setDialogRules(JsonUtils.toJsonString(ChannelDialogRulesDto.getDefault()));
        merchantChannelMapper.insert(entity);
        return entity;
    }

    private void saveMerchantChannelRelation(Long adminId, Long merchantChannelId) {
        ImMerchantChannelRelation e = new ImMerchantChannelRelation();
        e.setAdminId(adminId);
        e.setMerchantChannelId(merchantChannelId);
        merchantChannelRelationMapper.insert(e);
    }
}
