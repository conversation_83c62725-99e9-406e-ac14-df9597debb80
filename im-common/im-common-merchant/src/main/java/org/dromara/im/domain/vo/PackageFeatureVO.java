package org.dromara.im.domain.vo;

import lombok.Data;

@Data
public class PackageFeatureVO {

    /**
     * 应用上线功能
     */
    private boolean appOnline;

    /**
     * 在线对话功能
     */
    private boolean onlineChat;

    /**
     * 高级应用和智能工具
     */
    private boolean advancedTools;

    /**
     * 智能对话功能
     */
    private boolean smartDialogue;

    /**
     * 多坐席接入功能
     */
    private boolean multiSeatAccess;

    /**
     * 更多延展功能
     */
    private boolean extendedFeatures;

    /**
     * 内部对话功能
     */
    private boolean internalDialogue;

    /**
     * 内部转接功能
     */
    private boolean internalTransfer;

    /**
     * 智能翻译功能
     */
    private boolean smartTranslation;

    /**
     * 技术支持和服务
     */
    private boolean technicalSupport;

    /**
     * 团队协作功能
     */
    private boolean teamCollaboration;

    /**
     * 企业客户管理功能
     */
    private boolean customerManagement;

    /**
     * 多语言支持功能
     */
    private boolean multilingualSupport;

    /**
     * 中国大陆可用性
     */
    private boolean chinaMainlandAvailable;

    //坐席数量限制
    private Integer seatsCount;
    // 接待数据限制
    private Integer chatNumber;
    // 选择数据限制
    private Integer selectDataLimit;
    // 翻译字符限制
    private Integer translationCharLimit;
}
