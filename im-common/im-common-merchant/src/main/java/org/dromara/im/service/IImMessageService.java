package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.bo.ImMessageBo;
import org.dromara.im.domain.vo.ImMessageVo;

/**
 * 消息历史Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IImMessageService {

    /**
     * 查询消息历史
     */
    ImMessageVo queryById(Long id);

    /**
     * 根据消息ID查询消息
     */
    ImMessageVo queryByMessageId(String messageId);

    /**
     * 查询消息历史列表
     */
    TableDataInfo<ImMessageVo> queryPageList(ImMessageBo bo, PageQuery pageQuery);

    TableDataInfo<ImMessageVo> queryPageList2(String visitorId, Long aminId, PageQuery pageQuery);

    TableDataInfo<ImMessageVo> queryVisitorPageList(String visitorId, PageQuery pageQuery);

    /**
     * 新增消息历史
     */
    Boolean insertByBo(ImMessageBo bo);

    /**
     * ====== 核心业务方法 ======
     */

    /**
     * 发送消息（保存到数据库）
     *
     * @param conversationId 会话ID
     * @param senderType     发送者类型: visitor=游客, service=客服, system=系统
     * @param senderId       发送者ID
     * @param senderName     发送者姓名
     * @param receiverType   接收者类型: visitor=游客, service=客服
     * @param receiverId     接收者ID
     * @param messageType    消息类型: text=文本, image=图片, file=文件等
     * @param content        消息内容
     */
    void saveMessage(Long conversationId, String senderType, String senderId, String senderName,
                     String receiverType, String receiverId, String messageType, String content, String clientMsgId, String imageWidth, String imageHeight);



    /**
     * 批量标记访客与客服之间的消息为已读
     *
     * @param visitorId 访客ID
     * @param adminId   客服ID
     * @return 是否标记成功
     */
    Boolean markMessagesAsReadBetweenVisitorAndAdmin(String visitorId, Long adminId);

    /**
     * 获取访客发送给客服的未读消息数量
     *
     * @param visitorId 访客ID
     * @param adminId   客服ID
     * @return 未读消息数量
     */
    Long getUnreadCountFromVisitorToAdmin(String visitorId, Long adminId);

    /**
     * 获取会话的最后一条消息时间
     *
     * @param conversationId 会话ID
     * @return 最后消息时间，如果没有消息返回null
     */
    Long getLastMessageTime(Long conversationId);

    void updateMessageAdminId(String visitorId, Long conversationId, Long adminId);
}
