package org.dromara.im.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.constans.MessageSendType;
import org.dromara.im.domain.bo.ImMessageBo;
import org.dromara.im.domain.dto.ImMessageDto;
import org.dromara.im.domain.vo.ImMessageVo;

/**
 * 消息历史Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IImMessageService {

    /**
     * 查询消息历史
     */
    ImMessageVo queryById(Long id);

    /**
     * 根据消息ID查询消息
     */
    ImMessageVo queryByMessageId(String messageId);

    /**
     * 查询消息历史列表
     */
    TableDataInfo<ImMessageVo> queryPageList(ImMessageBo bo, PageQuery pageQuery);

    TableDataInfo<ImMessageVo> queryPageList2(String visitorId, Long aminId, PageQuery pageQuery);

    TableDataInfo<ImMessageVo> queryVisitorPageList(String visitorId, PageQuery pageQuery);

    /**
     * 新增消息历史
     */
    Boolean insertByBo(ImMessageBo bo);

    /**
     * ====== 核心业务方法 ======
     */

    /**
     * 保存消息到数据库
     */
    void saveMessage(ImMessageDto messageDto);
    /**
     * 批量标记访客与客服之间的消息为已读
     *
     * @param visitorId 访客ID
     * @param adminId   客服ID
     * @return 是否标记成功
     */
    Boolean markMessagesAsReadBetweenVisitorAndAdmin(String visitorId, Long adminId);

    /**
     * 获取访客发送给客服的未读消息数量
     *
     * @param visitorId 访客ID
     * @param adminId   客服ID
     * @return 未读消息数量
     */
    Long getUnreadCountFromVisitorToAdmin(String visitorId, Long adminId);

    /**
     * 获取会话的最后一条消息时间
     *
     * @param conversationId 会话ID
     * @return 最后消息时间，如果没有消息返回null
     */
    Long getLastMessageTime(Long conversationId);

    Long getFirstMessageTime(Long conversationId);

    void updateMessageAdminId(String visitorId, Long conversationId, Long adminId);
}
