package org.dromara.im.utils.message;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.im.domain.dto.AutoReplyDTO;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.service.IImAdminAutoReplyService;
import org.dromara.im.utils.SendMessageUtil;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 个人消息提示处理
 * 处理欢迎消息、结束消息、忙碌自动回复、客户无回复提醒等逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PersonMessageUtil {

    /**
     * 发送欢迎消息
     */
    public static void sendWelcomeMessage(ImConversationVo conversation) {
        AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);
        try {
            if (AutoReplyDTO.enableWelcomeMessageEnabled(autoReplyConfig)) {
                String welcomeMessage = autoReplyConfig.getWelcomeMessage();
                if (welcomeMessage != null && !welcomeMessage.trim().isEmpty()) {
                    SendMessageUtil.sendPersonTipMessage(conversation, autoReplyConfig.getWelcomeMessage());
                    log.info("发送欢迎消息 - conversationId: {}, visitorId: {}",
                        conversation.getConversationId(), conversation.getVisitorId());
                }
            }
        } catch (Exception e) {
            log.error("发送欢迎消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }


    /**
     * 发送结束消息
     */
    public static void sendEndMessage(ImConversationVo conversation) {
        AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);
        try {
            if (AutoReplyDTO.enableEndMessageEnabled(autoReplyConfig)) {
                String endMessage = autoReplyConfig.getEndMessage();
                if (endMessage != null && !endMessage.trim().isEmpty()) {
                    SendMessageUtil.sendPersonTipMessage(conversation, autoReplyConfig.getEndMessage());
                    log.info("发送结束消息 - conversationId: {}, visitorId: {}",
                        conversation.getConversationId(), conversation.getVisitorId());
                }
            }
        } catch (Exception e) {
            log.error("发送结束消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 检查并发送忙碌自动回复
     */
    public static void checkAndSendBusyAutoReply(ImConversationVo conversation) {
        try {
            AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);

            if (!AutoReplyDTO.enableBusyAutoReplyEnabled(autoReplyConfig)) {
                return;
            }

            Long adminId = conversation.getAdminId();
            if (adminId == null) {
                return;
            }

            // 检查客服是否忙碌（超过设定时间未活跃）
            String lastActiveKey = "service:last_active:" + adminId;
            Object lastActiveStr = RedisUtils.getCacheObject(lastActiveKey);

            if (lastActiveStr != null) {
                long lastActive = Long.parseLong(lastActiveStr.toString());
                long secondsSinceActive = (System.currentTimeMillis() - lastActive) / 1000;

                if (secondsSinceActive >= autoReplyConfig.getBusyTimeout()) {
                    // 检查是否已经发送过忙碌回复（避免重复发送）
                    String busyReplyKey = "busy_reply_sent:" + conversation.getConversationId();
                    String busyReplySent = RedisUtils.getCacheObject(busyReplyKey);

                    if (busyReplySent == null) {
                        // 发送忙碌自动回复
                        SendMessageUtil.sendPersonTipMessage(conversation, autoReplyConfig.getBusyAutoReplyMessage());
                        // 标记已发送，避免重复发送（5分钟内不再发送）
                        RedisUtils.setCacheObject(busyReplyKey, "1", Duration.ofMinutes(5));
                        log.info("发送忙碌自动回复 - conversationId: {}, adminId: {}, busySeconds: {}",
                            conversation.getConversationId(), adminId, secondsSinceActive);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查忙碌自动回复失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 检查并发送客户无回复提醒
     */
    public void checkAndSendCustomerNoReplyReminder(ImConversationVo conversation) {
        try {

            AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);

            if (!AutoReplyDTO.enableCustomerNoReplyEnabled(autoReplyConfig)) {
                return;
            }

            String visitorId = conversation.getVisitorId();
            Long conversationId = conversation.getConversationId();

            // 检查客户最后发送消息时间
            String lastMessageKey = "customer:last_message:" + visitorId + ":" + conversationId;
            String lastMessageStr = RedisUtils.getCacheObject(lastMessageKey);

            if (lastMessageStr != null) {
                long lastMessage = Long.parseLong(lastMessageStr);
                long secondsSinceMessage = (System.currentTimeMillis() - lastMessage) / 1000;

                if (secondsSinceMessage >= autoReplyConfig.getCustomerNoReplyTimeout()) {
                    // 检查是否已经发送过提醒（避免重复发送）
                    String reminderKey = "no_reply_reminder_sent:" + conversationId;
                    String reminderSent = RedisUtils.getCacheObject(reminderKey);

                    if (reminderSent == null) {
                        // 发送客户无回复提醒
                        String reminderMessage = autoReplyConfig.getCustomerNoReplyMessage();
                        SendMessageUtil.sendPersonTipMessage(conversation, reminderMessage);

                        // 标记已发送，避免重复发送（10分钟内不再发送）
                        RedisUtils.setCacheObject(reminderKey, "1", Duration.ofMinutes(10));

                        log.info("发送客户无回复提醒 - conversationId: {}, visitorId: {}, noReplySeconds: {}",
                            conversationId, visitorId, secondsSinceMessage);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查客户无回复提醒失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 批量检查忙碌自动回复
     * 用于定时任务调用
     */
    public void batchCheckBusyAutoReply(java.util.List<ImConversationVo> activeConversations) {
        if (activeConversations == null || activeConversations.isEmpty()) {
            return;
        }

        for (ImConversationVo conversation : activeConversations) {
            checkAndSendBusyAutoReply(conversation);
        }

        log.info("批量检查忙碌自动回复完成 - 处理数量: {}", activeConversations.size());
    }

    /**
     * 批量检查客户无回复提醒
     * 用于定时任务调用
     */
    public void batchCheckCustomerNoReplyReminder(java.util.List<ImConversationVo> activeConversations) {
        if (activeConversations == null || activeConversations.isEmpty()) {
            return;
        }

        for (ImConversationVo conversation : activeConversations) {
            checkAndSendCustomerNoReplyReminder(conversation);
        }

        log.info("批量检查客户无回复提醒完成 - 处理数量: {}", activeConversations.size());
    }


    private static AutoReplyDTO getAutoReplyDTO(ImConversationVo conversation) {
        IImAdminAutoReplyService imAdminAutoReplyService = SpringUtils.getBean(IImAdminAutoReplyService.class);
        return imAdminAutoReplyService.queryAutoReplyDTO(conversation.getAdminId(), conversation.getMerchantId(), conversation.getMerchantChannelId());
    }
}
