package org.dromara.im.utils.message;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.im.domain.dto.AutoReplyDTO;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.service.IImAdminAutoReplyService;
import org.dromara.im.utils.SendMessageUtil;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

/**
 * 个人消息提示处理
 * 处理欢迎消息、结束消息、忙碌自动回复、客户无回复提醒等逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PersonMessageUtil {

    /**
     * 发送欢迎消息
     */
    public static void sendWelcomeMessage(ImConversationVo conversation) {
        AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);
        try {
            if (AutoReplyDTO.enableWelcomeMessageEnabled(autoReplyConfig)) {
                String welcomeMessage = autoReplyConfig.getWelcomeMessage();
                if (welcomeMessage != null && !welcomeMessage.trim().isEmpty()) {
                    SendMessageUtil.sendPersonTipMessage(conversation, autoReplyConfig.getWelcomeMessage());
                    log.info("发送欢迎消息 - conversationId: {}, visitorId: {}",
                        conversation.getConversationId(), conversation.getVisitorId());
                }
            }
        } catch (Exception e) {
            log.error("发送欢迎消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }


    /**
     * 发送结束消息
     */
    public static void sendEndMessage(ImConversationVo conversation) {
        AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);
        try {
            if (AutoReplyDTO.enableEndMessageEnabled(autoReplyConfig)) {
                String endMessage = autoReplyConfig.getEndMessage();
                if (endMessage != null && !endMessage.trim().isEmpty()) {
                    SendMessageUtil.sendPersonTipMessage(conversation, autoReplyConfig.getEndMessage());
                    log.info("发送结束消息 - conversationId: {}, visitorId: {}",
                        conversation.getConversationId(), conversation.getVisitorId());
                }
            }
        } catch (Exception e) {
            log.error("发送结束消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 检查并发送忙碌自动回复
     */
    public static void checkAndSendBusyAutoReply(ImConversationVo conversation) {
        try {
            AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);

            if (!AutoReplyDTO.enableBusyAutoReplyEnabled(autoReplyConfig)) {
                return;
            }

            Long adminId = conversation.getAdminId();
            if (adminId == null) {
                return;
            }

            // 检查客服是否忙碌（超过设定时间未活跃）
            String lastActiveKey = getServiceLastActiveRedisKey(adminId);
            Long lastActiveStr = RedisUtils.getCacheObject(lastActiveKey);

            if (lastActiveStr != null) {
                long lastActive = Long.parseLong(lastActiveStr.toString());
                long secondsSinceActive = (System.currentTimeMillis() - lastActive);

                if (secondsSinceActive >= 1000L * autoReplyConfig.getBusyTimeout()) {
                    // 检查是否已经发送过忙碌回复（避免重复发送）
                    String busyReplyKey = getResetBusyReplyRedisKey(conversation.getConversationId());
                    String busyReplySent = RedisUtils.getCacheObject(busyReplyKey);

                    if (busyReplySent == null) {
                        // 发送忙碌自动回复
                        SendMessageUtil.sendPersonTipMessage(conversation, autoReplyConfig.getBusyAutoReplyMessage());
                        // 标记已发送，避免重复发送（5分钟内不再发送）
                        RedisUtils.setCacheObject(busyReplyKey, "1", Duration.ofMinutes(5));
                        log.info("发送忙碌自动回复 - conversationId: {}, adminId: {}, busySeconds: {}",
                            conversation.getConversationId(), adminId, secondsSinceActive);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查忙碌自动回复失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 检查并发送客户无回复提醒
     */
    public static void checkAndSendCustomerNoReplyReminder(ImConversationVo conversation) {
        try {

            AutoReplyDTO autoReplyConfig = getAutoReplyDTO(conversation);

            if (!AutoReplyDTO.enableCustomerNoReplyEnabled(autoReplyConfig)) {
                return;
            }

            String visitorId = conversation.getVisitorId();
            Long conversationId = conversation.getConversationId();

            // 检查客户最后发送消息时间
            String lastMessageKey = getLastMessageKey(visitorId, conversationId);
            Long lastMessage = RedisUtils.getCacheObject(lastMessageKey);

            if (lastMessage != null) {
                long secondsSinceMessage = (System.currentTimeMillis() - lastMessage) / 1000;

                if (secondsSinceMessage >= autoReplyConfig.getCustomerNoReplyTimeout()) {
                    // 检查是否已经发送过提醒（避免重复发送）
                    String reminderKey = getReminderKey(conversationId);
                    String reminderSent = RedisUtils.getCacheObject(reminderKey);

                    if (reminderSent == null) {
                        // 发送客户无回复提醒
                        String reminderMessage = autoReplyConfig.getCustomerNoReplyMessage();
                        SendMessageUtil.sendPersonTipMessage(conversation, reminderMessage);

                        // 标记已发送，避免重复发送（10分钟内不再发送）
                        RedisUtils.setCacheObject(reminderKey, "1", Duration.ofMinutes(10));

                        log.info("发送客户无回复提醒 - conversationId: {}, visitorId: {}, noReplySeconds: {}",
                            conversationId, visitorId, secondsSinceMessage);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查客户无回复提醒失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    private static String getLastMessageKey(String visitorId, Long conversationId) {
        return "customer:last_message:" + visitorId + ":" + conversationId;
    }

    /**
     * 批量检查忙碌自动回复
     * 用于定时任务调用
     */
    public void batchCheckBusyAutoReply(List<ImConversationVo> activeConversations) {
        if (activeConversations == null || activeConversations.isEmpty()) {
            return;
        }

        for (ImConversationVo conversation : activeConversations) {
            checkAndSendBusyAutoReply(conversation);
        }

        log.info("批量检查忙碌自动回复完成 - 处理数量: {}", activeConversations.size());
    }

    /**
     * 批量检查客户无回复提醒
     * 用于定时任务调用
     */
    public void batchCheckCustomerNoReplyReminder(List<ImConversationVo> activeConversations) {
        if (activeConversations == null || activeConversations.isEmpty()) {
            return;
        }

        for (ImConversationVo conversation : activeConversations) {
            checkAndSendCustomerNoReplyReminder(conversation);
        }

        log.info("批量检查客户无回复提醒完成 - 处理数量: {}", activeConversations.size());
    }


    /**
     * 记录客服最后活跃时间
     */
    public void recordServiceActivity(Long adminId) {
        try {
            String key = getServiceLastActiveRedisKey(adminId);
            RedisUtils.setCacheObject(key, System.currentTimeMillis(), Duration.ofHours(24));
            log.debug("记录客服活跃时间 - adminId: {}", adminId);
        } catch (Exception e) {
            log.error("记录客服活跃时间失败 - adminId: {}", adminId, e);
        }
    }

    private static String getServiceLastActiveRedisKey(Long adminId) {
        return "service:last_active:" + adminId;
    }

    /**
     * 记录客户最后发送消息时间
     */
    public void recordCustomerMessage(String visitorId, Long conversationId) {
        try {
            String key = getLastMessageKey(visitorId, conversationId);
            RedisUtils.setCacheObject(key, System.currentTimeMillis(), Duration.ofHours(24));
            log.debug("记录客户消息时间 - visitorId: {}, conversationId: {}", visitorId, conversationId);
        } catch (Exception e) {
            log.error("记录客户消息时间失败 - visitorId: {}, conversationId: {}", visitorId, conversationId, e);
        }
    }

    /**
     * 重置忙碌回复标记
     * 当客服重新活跃时调用
     */
    public void resetBusyReplyFlag(Long conversationId) {
        try {
            String busyReplyKey = getResetBusyReplyRedisKey(conversationId);
            RedisUtils.deleteObject(busyReplyKey);
            log.debug("重置忙碌回复标记 - conversationId: {}", conversationId);
        } catch (Exception e) {
            log.error("重置忙碌回复标记失败 - conversationId: {}", conversationId, e);
        }
    }

    private static String getResetBusyReplyRedisKey(Long conversationId) {
        return "busy_reply_sent:" + conversationId;
    }

    /**
     * 重置无回复提醒标记
     * 当客户发送新消息时调用
     */
    public void resetNoReplyReminderFlag(Long conversationId) {
        try {
            String reminderKey = getReminderKey(conversationId);
            RedisUtils.deleteObject(reminderKey);
            log.debug("重置无回复提醒标记 - conversationId: {}", conversationId);
        } catch (Exception e) {
            log.error("重置无回复提醒标记失败 - conversationId: {}", conversationId, e);
        }
    }

    private static String getReminderKey(Long conversationId) {
        return "no_reply_reminder_sent:" + conversationId;
    }


    private static AutoReplyDTO getAutoReplyDTO(ImConversationVo conversation) {
        IImAdminAutoReplyService imAdminAutoReplyService = SpringUtils.getBean(IImAdminAutoReplyService.class);
        return imAdminAutoReplyService.queryAutoReplyDTO(conversation.getAdminId(), conversation.getMerchantId(), conversation.getMerchantChannelId());
    }
}
