package org.dromara.im.utils.message;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.im.domain.dto.AutoReplyDTO;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.service.IImAdminAutoReplyService;
import org.dromara.im.service.QueueMessageService;
import org.dromara.im.utils.SendMessageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 个人消息提示处理
 * 处理欢迎消息、结束消息、忙碌自动回复、客户无回复提醒等逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PersonMessageUtil {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 发送欢迎消息
     */
    public static void sendWelcomeMessage(ImConversationVo conversation) {
        IImAdminAutoReplyService imAdminAutoReplyService = SpringUtils.getBean(IImAdminAutoReplyService.class);
        AutoReplyDTO autoReplyConfig = imAdminAutoReplyService.queryAutoReplyDTO(conversation.getAdminId(), conversation.getMerchantId(), conversation.getMerchantChannelId());
        try {

            if (AutoReplyDTO.enableWelcomeMessageEnabled(autoReplyConfig)) {
                String welcomeMessage = autoReplyConfig.getWelcomeMessage();
                if (welcomeMessage != null && !welcomeMessage.trim().isEmpty()) {
                    SendMessageUtil.sendStartServiceWarn(conversation, autoReplyConfig.getWelcomeMessage());
                    log.info("发送欢迎消息 - conversationId: {}, visitorId: {}",
                        conversation.getConversationId(), conversation.getVisitorId());
                }
            }
        } catch (Exception e) {
            log.error("发送欢迎消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 发送结束消息
     */
    public void sendEndMessage(ImConversationVo conversation, AutoReplyDTO autoReplyConfig) {
        try {
            if ("1".equals(autoReplyConfig.getEndMessageEnabled())) {
                String endMessage = autoReplyConfig.getEndMessage();
                if (endMessage != null && !endMessage.trim().isEmpty()) {
                    SendMessageUtil.sendEndServiceWarn(conversation);
                    log.info("发送结束消息 - conversationId: {}, visitorId: {}",
                        conversation.getConversationId(), conversation.getVisitorId());
                }
            }
        } catch (Exception e) {
            log.error("发送结束消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 记录客服最后活跃时间
     */
    public void recordServiceActivity(Long adminId) {
        try {
            String key = "service:last_active:" + adminId;
            redisTemplate.opsForValue().set(key, String.valueOf(System.currentTimeMillis()),
                Duration.ofHours(24));
            log.debug("记录客服活跃时间 - adminId: {}", adminId);
        } catch (Exception e) {
            log.error("记录客服活跃时间失败 - adminId: {}", adminId, e);
        }
    }

    /**
     * 检查并发送忙碌自动回复
     */
    public void checkAndSendBusyAutoReply(ImConversationVo conversation, AutoReplyDTO autoReplyConfig) {
        try {
            if (autoReplyConfig == null) {
                autoReplyConfig = getDefault();
            }

            if (!"1".equals(autoReplyConfig.getBusyAutoReplyEnabled())) {
                return;
            }

            Long adminId = conversation.getAdminId();
            if (adminId == null) {
                return;
            }

            // 检查客服是否忙碌（超过设定时间未活跃）
            String lastActiveKey = "service:last_active:" + adminId;
            String lastActiveStr = redisTemplate.opsForValue().get(lastActiveKey);

            if (lastActiveStr != null) {
                long lastActive = Long.parseLong(lastActiveStr);
                long secondsSinceActive = (System.currentTimeMillis() - lastActive) / 1000;

                if (secondsSinceActive >= autoReplyConfig.getBusyTimeout()) {
                    // 检查是否已经发送过忙碌回复（避免重复发送）
                    String busyReplyKey = "busy_reply_sent:" + conversation.getConversationId();
                    String busyReplySent = redisTemplate.opsForValue().get(busyReplyKey);

                    if (busyReplySent == null) {
                        // 发送忙碌自动回复
                        String busyMessage = autoReplyConfig.getBusyAutoReplyMessage();
                        queueMessageService.sendCustomQueueMessage(conversation, "busy_auto_reply", busyMessage);

                        // 标记已发送，避免重复发送（5分钟内不再发送）
                        redisTemplate.opsForValue().set(busyReplyKey, "1", Duration.ofMinutes(5));

                        log.info("发送忙碌自动回复 - conversationId: {}, adminId: {}, busySeconds: {}",
                            conversation.getConversationId(), adminId, secondsSinceActive);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查忙碌自动回复失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 记录客户最后发送消息时间
     */
    public void recordCustomerMessage(String visitorId, Long conversationId) {
        try {
            String key = "customer:last_message:" + visitorId + ":" + conversationId;
            redisTemplate.opsForValue().set(key, String.valueOf(System.currentTimeMillis()),
                Duration.ofHours(24));
            log.debug("记录客户消息时间 - visitorId: {}, conversationId: {}", visitorId, conversationId);
        } catch (Exception e) {
            log.error("记录客户消息时间失败 - visitorId: {}, conversationId: {}", visitorId, conversationId, e);
        }
    }

    /**
     * 检查并发送客户无回复提醒
     */
    public void checkAndSendCustomerNoReplyReminder(ImConversationVo conversation, AutoReplyDTO autoReplyConfig) {
        try {
            if (autoReplyConfig == null) {
                autoReplyConfig = getDefault();
            }

            if (!"1".equals(autoReplyConfig.getCustomerNoReplyEnabled())) {
                return;
            }

            String visitorId = conversation.getVisitorId();
            Long conversationId = conversation.getConversationId();

            // 检查客户最后发送消息时间
            String lastMessageKey = "customer:last_message:" + visitorId + ":" + conversationId;
            String lastMessageStr = redisTemplate.opsForValue().get(lastMessageKey);

            if (lastMessageStr != null) {
                long lastMessage = Long.parseLong(lastMessageStr);
                long secondsSinceMessage = (System.currentTimeMillis() - lastMessage) / 1000;

                if (secondsSinceMessage >= autoReplyConfig.getCustomerNoReplyTimeout()) {
                    // 检查是否已经发送过提醒（避免重复发送）
                    String reminderKey = "no_reply_reminder_sent:" + conversationId;
                    String reminderSent = redisTemplate.opsForValue().get(reminderKey);

                    if (reminderSent == null) {
                        // 发送客户无回复提醒
                        String reminderMessage = autoReplyConfig.getCustomerNoReplyMessage();
                        queueMessageService.sendCustomQueueMessage(conversation, "customer_no_reply_reminder", reminderMessage);

                        // 标记已发送，避免重复发送（10分钟内不再发送）
                        redisTemplate.opsForValue().set(reminderKey, "1", Duration.ofMinutes(10));

                        log.info("发送客户无回复提醒 - conversationId: {}, visitorId: {}, noReplySeconds: {}",
                            conversationId, visitorId, secondsSinceMessage);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查客户无回复提醒失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 批量检查忙碌自动回复
     * 用于定时任务调用
     */
    public void batchCheckBusyAutoReply(java.util.List<ImConversationVo> activeConversations, AutoReplyDTO autoReplyConfig) {
        if (activeConversations == null || activeConversations.isEmpty()) {
            return;
        }

        for (ImConversationVo conversation : activeConversations) {
            checkAndSendBusyAutoReply(conversation, autoReplyConfig);
        }

        log.debug("批量检查忙碌自动回复完成 - 处理数量: {}", activeConversations.size());
    }

    /**
     * 批量检查客户无回复提醒
     * 用于定时任务调用
     */
    public void batchCheckCustomerNoReplyReminder(java.util.List<ImConversationVo> activeConversations, AutoReplyDTO autoReplyConfig) {
        if (activeConversations == null || activeConversations.isEmpty()) {
            return;
        }

        for (ImConversationVo conversation : activeConversations) {
            checkAndSendCustomerNoReplyReminder(conversation, autoReplyConfig);
        }

        log.debug("批量检查客户无回复提醒完成 - 处理数量: {}", activeConversations.size());
    }

    /**
     * 获取客服忙碌状态
     */
    public boolean isServiceBusy(Long adminId, int busyTimeoutSeconds) {
        try {
            String key = "service:last_active:" + adminId;
            String lastActiveStr = redisTemplate.opsForValue().get(key);

            if (lastActiveStr != null) {
                long lastActive = Long.parseLong(lastActiveStr);
                long secondsSinceActive = (System.currentTimeMillis() - lastActive) / 1000;
                return secondsSinceActive >= busyTimeoutSeconds;
            }

            return true; // 没有活跃记录，认为是忙碌状态
        } catch (Exception e) {
            log.error("获取客服忙碌状态失败 - adminId: {}", adminId, e);
            return false;
        }
    }

    /**
     * 重置忙碌回复标记
     * 当客服重新活跃时调用
     */
    public void resetBusyReplyFlag(Long conversationId) {
        try {
            String busyReplyKey = "busy_reply_sent:" + conversationId;
            redisTemplate.delete(busyReplyKey);
            log.debug("重置忙碌回复标记 - conversationId: {}", conversationId);
        } catch (Exception e) {
            log.error("重置忙碌回复标记失败 - conversationId: {}", conversationId, e);
        }
    }

    /**
     * 重置无回复提醒标记
     * 当客户发送新消息时调用
     */
    public void resetNoReplyReminderFlag(Long conversationId) {
        try {
            String reminderKey = "no_reply_reminder_sent:" + conversationId;
            redisTemplate.delete(reminderKey);
            log.debug("重置无回复提醒标记 - conversationId: {}", conversationId);
        } catch (Exception e) {
            log.error("重置无回复提醒标记失败 - conversationId: {}", conversationId, e);
        }
    }
}
