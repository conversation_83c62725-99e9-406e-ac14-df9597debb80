package org.dromara.im.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 游客访问对象 im_visitor
 *
 * <AUTHOR> Li
 * @date 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_visitor")
public class ImVisitor extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 游客唯一标识
     */
    private String visitorId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * 游客显示名称
     */
    private String visitorName;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 访问渠道标识
     */
    private String channel;

    /**
     * 访客IP地址(支持IPv6)
     */
    private String ipAddress;

    /**
     * IP地理位置信息
     */
    private String locationInfo;

    /**
     * 来源页面URL
     */
    private String fromUrl;

    /**
     * 浏览器信息
     */
    private String userAgent;

    /**
     * 浏览器详细信息JSON格式
     */
    private String browserInfo;

    /**
     * 客户端语言
     */
    private String language;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 状态: 0=离线, 1=在线
     */
    private String onlineStatus;

    /**
     * 浏览器指纹
     */
    private String fingerprint;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 最后在线时间
     */
    private Long lastOnlineTime;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 服务排序
     */
    private Integer serviceSort;

    /**
     * 优先级客服排序
     */
    private Integer priorityServiceSort;


}
