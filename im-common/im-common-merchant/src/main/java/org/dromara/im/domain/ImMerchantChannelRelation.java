package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 渠道用户关系对象 im_merchant_channel_relation
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_merchant_channel_relation")
public class ImMerchantChannelRelation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "merchant_channel_relation_id")
    private Long merchantChannelRelationId;

    /**
     * 用户id
     */
    private Long adminId;

    /**
     * 商户渠道id
     */
    private Long merchantChannelId;

    /**
     * 租户编号
     */
    private String tenantId;


}
