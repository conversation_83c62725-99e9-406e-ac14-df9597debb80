package org.dromara.im.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 自动回复设置请求
 *
 * <AUTHOR>
 */
@Data
public class AutoReplyDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long adminAutoReplyId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * 欢迎语开关 0=关闭 1=开启
     */
    private String welcomeMessageEnabled;

    /**
     * 欢迎语内容
     */
    private String welcomeMessage;

    /**
     * 结束语开关 0=关闭 1=开启
     */
    private String endMessageEnabled;

    /**
     * 结束语内容
     */
    private String endMessage;

    /**
     * 忙碌时自动回复开关 0=关闭 1=开启
     */
    private String busyAutoReplyEnabled;

    /**
     * 忙碌时超时时间（秒）
     */
    private Integer busyTimeout;

    /**
     * 忙碌时自动回复内容
     */
    private String busyAutoReplyMessage;

    /**
     * 顾客不回复时自动回复开关 0=关闭 1=开启
     */
    private String customerNoReplyEnabled;

    /**
     * 顾客不回复超时时间（秒）
     */
    private Integer customerNoReplyTimeout;

    /**
     * 顾客不回复时自动回复内容
     */
    private String customerNoReplyMessage;


    public static AutoReplyDTO getDefault() {
        AutoReplyDTO autoReplyDTO = new AutoReplyDTO();
        autoReplyDTO.setWelcomeMessageEnabled("0");
        autoReplyDTO.setWelcomeMessage("很高兴为您服务，有什么可以为您效劳的吗？");
        autoReplyDTO.setEndMessageEnabled("0");
        autoReplyDTO.setEndMessage("您好，为了保证服务质量，我们已经结束了对话，期待再次为您服务");
        autoReplyDTO.setBusyAutoReplyEnabled("0");
        autoReplyDTO.setBusyTimeout(60);
        autoReplyDTO.setBusyAutoReplyMessage("您好，我正在忙碌，请稍等。");
        autoReplyDTO.setCustomerNoReplyEnabled("0");
        autoReplyDTO.setCustomerNoReplyTimeout(90);
        autoReplyDTO.setCustomerNoReplyMessage("您好，我还在等待您的消息，还需要我的帮助吗？");
        return autoReplyDTO;
    }

    public static boolean enableWelcomeMessageEnabled(AutoReplyDTO autoReplyDTO) {
        return "1".equals(autoReplyDTO.getWelcomeMessageEnabled());
    }

}
