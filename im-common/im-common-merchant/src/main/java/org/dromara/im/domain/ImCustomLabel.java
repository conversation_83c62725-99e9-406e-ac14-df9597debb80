package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 用户标签对象 im_custom_label
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_custom_label")
public class ImCustomLabel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户标签id
     */
    @TableId(value = "custom_label_id")
    private Long customLabelId;

    /**
     * 用户标签名称
     */
    private String customLabelName;

    /**
     * 商户渠道id
     */
    private Long merchantChannelId;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 商户id
     */
    private Long merchantId;


}
