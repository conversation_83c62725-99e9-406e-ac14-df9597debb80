package org.dromara.im.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.constans.MessageStatus;
import org.dromara.im.domain.ImMessage;
import org.dromara.im.domain.bo.ImMessageBo;
import org.dromara.im.domain.vo.ImMessageVo;
import org.dromara.im.mapper.ImMessageMapper;
import org.dromara.im.service.IImMessageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Objects;

/**
 * 消息历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ImMessageServiceImpl implements IImMessageService {

    private final ImMessageMapper baseMapper;

    /**
     * 查询消息历史
     */
    @Override
    public ImMessageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据消息ID查询消息
     */
    @Override
    public ImMessageVo queryByMessageId(String messageId) {
        LambdaQueryWrapper<ImMessage> lqw = Wrappers.lambdaQuery();
        lqw.eq(ImMessage::getMessageId, messageId);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 查询消息历史列表
     */
    @Override
    public TableDataInfo<ImMessageVo> queryPageList(ImMessageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMessage> lqw = buildQueryWrapper(bo);
        Page<ImMessageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<ImMessageVo> queryPageList2(String visitorId, Long adminId, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMessage> lqw = Wrappers.lambdaQuery();

        // 查询条件：(senderId=visitorId AND receiverId=adminId) OR (senderId=adminId AND receiverId=visitorId)
        lqw.and(wrapper -> wrapper
            // 访客发送给客服的消息
            .or(subWrapper -> subWrapper
                .eq(ImMessage::getSenderId, visitorId)
                .eq(ImMessage::getReceiverId, adminId.toString())
            )
            // 客服发送给访客的消息
            .or(subWrapper -> subWrapper
                .eq(ImMessage::getSenderId, adminId.toString())
                .eq(ImMessage::getReceiverId, visitorId)
            )
        );

        // 按消息ID倒序排列（最新消息在前）
        lqw.orderByDesc(ImMessage::getId);

        Page<ImMessageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<ImMessageVo> queryVisitorPageList(String visitorId, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMessage> lqw = Wrappers.lambdaQuery();

        // 查询条件：(senderId=visitorId AND receiverId=adminId) OR (senderId=adminId AND receiverId=visitorId)
        lqw.and(wrapper -> wrapper
            // 访客发送给客服的消息
            .or(subWrapper -> subWrapper
                .eq(ImMessage::getSenderId, visitorId)
            )
            // 客服发送给访客的消息
            .or(subWrapper -> subWrapper
                .eq(ImMessage::getReceiverId, visitorId)
            )
        );

        // 按消息ID倒序排列（最新消息在前）
        lqw.orderByDesc(ImMessage::getId);

        Page<ImMessageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);

    }


    private LambdaQueryWrapper<ImMessage> buildQueryWrapper(ImMessageBo bo) {
        LambdaQueryWrapper<ImMessage> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ImMessage::getId);
        lqw.eq(Objects.nonNull(bo.getConversationId()), ImMessage::getConversationId, bo.getConversationId());
        lqw.eq(StringUtils.isNotBlank(bo.getMessageId()), ImMessage::getMessageId, bo.getMessageId());
        lqw.eq(StringUtils.isNotBlank(bo.getSenderType()), ImMessage::getSenderType, bo.getSenderType());
        lqw.eq(StringUtils.isNotBlank(bo.getSenderId()), ImMessage::getSenderId, bo.getSenderId());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiverType()), ImMessage::getReceiverType, bo.getReceiverType());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiverId()), ImMessage::getReceiverId, bo.getReceiverId());
        lqw.eq(StringUtils.isNotBlank(bo.getMessageType()), ImMessage::getMessageType, bo.getMessageType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ImMessage::getStatus, bo.getStatus());
        lqw.eq(bo.getIsRecalled() != null, ImMessage::getIsRecalled, bo.getIsRecalled());
        return lqw;
    }

    /**
     * 新增消息历史
     */
    @Override
    public Boolean insertByBo(ImMessageBo bo) {
        ImMessage add = MapstructUtils.convert(bo, ImMessage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMessage entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    // ====== 核心业务方法实现 ======

    /**
     * 发送消息（保存到数据库）
     */
    @Override
    public void saveMessage(Long conversationId, String senderType, String senderId, String senderName,
                            String receiverType, String receiverId, String messageType, String content, String clientMsgId, String imageWidth, String imageHeight) {

        // 生成消息ID
        String messageId = "msg_" + IdUtil.fastSimpleUUID();

        // 创建消息记录
        ImMessageBo messageBo = new ImMessageBo();
        messageBo.setMessageId(messageId);
        messageBo.setConversationId(conversationId);
        messageBo.setSenderType(senderType);
        messageBo.setSenderId(senderId);
        messageBo.setSenderName(senderName);
        messageBo.setReceiverType(receiverType);
        messageBo.setReceiverId(receiverId);
        messageBo.setMessageType(StrUtil.isNotBlank(messageType) ? messageType : "text");
        messageBo.setContent(content);
        messageBo.setStatus(MessageStatus.sent);
        messageBo.setIsRecalled(0L);
        messageBo.setClientMsgId(clientMsgId);
        messageBo.setMessageSendTime(System.currentTimeMillis());
        if (StringUtils.isNotBlank(imageWidth) && StringUtils.isNotBlank(imageHeight)) {
            HashMap<Object, Object> extra = new HashMap<>();
            extra.put("imageWidth", imageWidth);
            extra.put("imageHeight", imageHeight);
            messageBo.setExtraData(JsonUtils.toJsonString(extra));
        }

        boolean saved = insertByBo(messageBo);
        if (saved) {
            log.info("消息保存成功: messageId={}, conversationId={}, senderType={}, senderId={}",
                messageId, conversationId, senderType, senderId);
            queryByMessageId(messageId);
        } else {
            log.error("消息保存失败: conversationId={}, senderType={}, senderId={}",
                conversationId, senderType, senderId);
            throw new RuntimeException("消息保存失败");
        }
    }

    /**
     * 批量标记访客与客服之间的消息为已读
     *
     * @param visitorId 访客ID
     * @param adminId   客服ID
     * @return 是否标记成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean markMessagesAsReadBetweenVisitorAndAdmin(String visitorId, Long adminId) {
        try {
            LambdaUpdateWrapper<ImMessage> updateWrapper = Wrappers.lambdaUpdate();

            // 只标记访客发送给客服的未读消息为已读
            updateWrapper.and(wrapper -> wrapper
                .eq(ImMessage::getSenderId, visitorId)
                .eq(ImMessage::getReceiverId, adminId.toString())
            );

            // 只更新未读的消息
            updateWrapper.ne(ImMessage::getStatus, MessageStatus.read);


            // 设置为已读状态
            updateWrapper.set(ImMessage::getStatus, MessageStatus.read);
            long now = System.currentTimeMillis();
            updateWrapper.set(ImMessage::getReadTime, now);
            int updatedCount = baseMapper.update(null, updateWrapper);

            log.info("批量标记消息已读完成 - visitorId: {}, adminId: {}, 更新数量: {}",
                visitorId, adminId, updatedCount);

            return true;

        } catch (Exception e) {
            log.error("批量标记消息已读失败 - visitorId: {}, adminId: {}", visitorId, adminId, e);
            return false;
        }
    }

    /**
     * 获取访客发送给客服的未读消息数量
     *
     * @param visitorId 访客ID
     * @param adminId   客服ID
     * @return 未读消息数量
     */
    public Long getUnreadCountFromVisitorToAdmin(String visitorId, Long adminId) {
        try {
            LambdaQueryWrapper<ImMessage> lqw = Wrappers.lambdaQuery();

            // 只统计访客发送给客服的消息
            lqw.eq(ImMessage::getSenderId, visitorId);
            lqw.eq(ImMessage::getReceiverId, adminId.toString());

            // 只统计未读消息
            lqw.ne(ImMessage::getStatus, MessageStatus.read);

            // 只统计未删除的消息
//            lqw.eq(ImMessage::getIsDeleted, 0);

            Long count = baseMapper.selectCount(lqw);

            log.info("查询未读消息数量 - visitorId: {}, adminId: {}, 未读数量: {}",
                visitorId, adminId, count);

            return count;

        } catch (Exception e) {
            log.error("查询未读消息数量失败 - visitorId: {}, adminId: {}", visitorId, adminId, e);
            return 0L;
        }
    }

    @Override
    public Long getLastMessageTime(Long conversationId) {
        try {
            LambdaQueryWrapper<ImMessage> wrapper = Wrappers.lambdaQuery(ImMessage.class)
                .eq(ImMessage::getConversationId, conversationId)
                .orderByDesc(ImMessage::getMessageSendTime)
                .last("LIMIT 1");

            ImMessage lastMessage = baseMapper.selectOne(wrapper);

            if (lastMessage != null) {
                log.info("获取最后消息时间 - conversationId: {}, lastMessageTime: {}",
                    conversationId, lastMessage.getCreateTime());
                return lastMessage.getMessageSendTime();
            } else {
                log.info("会话无消息记录 - conversationId: {}", conversationId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取最后消息时间失败 - conversationId: {}", conversationId, e);
            return null;
        }
    }

    @Override
    public Long getFirstMessageTime(Long conversationId) {
        try {
            LambdaQueryWrapper<ImMessage> wrapper = Wrappers.lambdaQuery(ImMessage.class)
                .eq(ImMessage::getConversationId, conversationId)
                .eq(ImMessage::getStatus, MessageStatus.sent)
                .orderByAsc(ImMessage::getMessageSendTime)
                .last("LIMIT 1");

            ImMessage lastMessage = baseMapper.selectOne(wrapper);

            if (lastMessage != null) {
                log.info("获取最后消息时间 - conversationId: {}, lastMessageTime: {}",
                    conversationId, lastMessage.getCreateTime());
                return lastMessage.getMessageSendTime();
            } else {
                log.info("会话无消息记录 - conversationId: {}", conversationId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取最后消息时间失败 - conversationId: {}", conversationId, e);
            return null;
        }
    }

    @Override
    public void updateMessageAdminId(String visitorId, Long conversationId, Long adminId) {
       baseMapper.update(new LambdaUpdateWrapper<>(ImMessage.class)
           .set(ImMessage::getReceiverId, adminId)
           .eq(ImMessage::getSenderId, visitorId)
           .eq(ImMessage::getConversationId, conversationId)
       );
    }
}
