package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 商户对象 im_merchant
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_merchant")
public class ImMerchant extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    @TableId(value = "merchant_id")
    private Long merchantId;

    /**
     * 商户编码
     */
    private String merchantCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 坐席数量
     */
    private Long seatsCount;

    /**
     * 对话数量
     */
    private Long chatNumber;

    /**
     * 渠道数量
     */
    private Long channelNumber;

    /**
     * 已使用量
     */
    private Long useTranslateNumber;

    /**
     * 总量
     */
    private Long translateNumber;


    /**
     * 过期时间
     */
    private Long expiredTime;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 对话排队配置
     */
    private String conversationQueues;

    /**
     * 0->体验版;1->标准版;2->专业版;3->集团版
     */
    private String packageType;

}
