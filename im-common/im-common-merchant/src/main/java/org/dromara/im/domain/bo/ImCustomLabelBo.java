package org.dromara.im.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.im.domain.ImCustomLabel;

/**
 * 用户标签业务对象 im_custom_label
 *
 * <AUTHOR> Li
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImCustomLabel.class, reverseConvertGenerate = false)
public class ImCustomLabelBo extends BaseEntity {

    /**
     * 用户标签id
     */
    private Long customLabelId;

    /**
     * 用户标签名称
     */
    @NotBlank(message = "用户标签名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String customLabelName;

    /**
     * 商户渠道id
     */
    @NotNull(message = "商户渠道id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long merchantChannelId;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 商户id
     */
    private Long merchantId;


}
