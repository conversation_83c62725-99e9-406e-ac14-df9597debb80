package org.dromara.im.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.im.domain.ImPaymentOrder;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 支付订单视图对象 im_payment_order
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImPaymentOrder.class)
public class ImPaymentOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道翻译id
     */
    @ExcelProperty(value = "渠道翻译id")
    private Long paymentOrderId;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * 订单类型;0->商户订单;1->翻译订单;2->套餐升级;3->翻译容量升级
     */
    @ExcelProperty(value = "订单类型;0->商户订单;1->翻译订单;2->套餐升级;3->翻译容量升级")
    private String orderType;

    /**
     * 订单状态0->待支付;1->已支付;2->已失效
     */
    @ExcelProperty(value = "订单状态0->待支付;1->已支付;2->已失效")
    private String orderStatus;

    /**
     * 原价
     */
    @ExcelProperty(value = "原价")
    private Long originalPrice;

    /**
     * 支付金额
     */
    @ExcelProperty(value = "支付金额")
    private Long paymentAmount;

    /**
     * 支付方式;0->USDT
     */
    @ExcelProperty(value = "支付方式;0->USDT")
    private String paymentMethod;

    /**
     * 支付详情
     */
    @ExcelProperty(value = "支付详情")
    private String orderDetail;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String extra;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


    private Date createTime;


}
