package org.dromara.im.websocket.service;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.domain.model.VisitorLoginUser;
import org.dromara.common.websocket.dto.WebSocketMessageDto;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.MessageDirection;
import org.dromara.im.constans.MessageEvent;
import org.dromara.im.constans.MessageType;
import org.dromara.im.domain.dto.message.TextMessageData;
import org.dromara.im.domain.dto.message.WebSocketTemplateMessage;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * WebSocket消息路由服务
 * 负责在访客和客服之间转发消息，解决循环依赖问题
 */
@Slf4j
@Service
public class WebSocketMessageRouter {

    public static String buildSessionKey(Long merchantId, Long adminId) {
        return merchantId + "-" + adminId;
    }

    public static String buildSessionKey(Long merchantId, String visitorId) {
        return merchantId + "-" + visitorId;
    }

    /**
     * 访客消息转发给客服
     */
    public void forwardVisitorMessageToService(VisitorLoginUser visitorVo, String messageType, Long adminId, String content) {
        try {
            Long merchantId = visitorVo.getMerchantId();
            String serviceSessionKey = buildSessionKey(merchantId, adminId);
            if (StringUtils.isNotBlank(serviceSessionKey)) {
                // 构造符合现有格式的消息
                TextMessageData chatData = new TextMessageData();
                chatData.setType(MessageType.CHAT_MESSAGE);
                chatData.setMessageType(messageType);
                chatData.setVisitorId(visitorVo.getVisitorId());
                chatData.setVisitorName(visitorVo.getVisitorName());
                chatData.setVisitorAvatar("ufImgServer/neweggtest-mengjiala/20250723/fadd94704a3e4288aaf932dfbd15ecff.png");
                chatData.setServiceId(adminId.toString());
                chatData.setServiceName("客服昵称");
                chatData.setServiceAvatar("ufImgServer/neweggtest-mengjiala/20250723/fadd94704a3e4288aaf932dfbd15ecff.png");
                chatData.setContent(content);
                chatData.setDirection("to_service");
                chatData.setBusinessId(merchantId);
                chatData.setMessageSendTime(System.currentTimeMillis());

                WebSocketTemplateMessage<TextMessageData> wsMsg = new WebSocketTemplateMessage<>();
                wsMsg.setEvent(MessageEvent.VISITOR_EVENT.getCode());  // 发给客服使用SERVICE_EVENT
                wsMsg.setData(chatData);
                // 创建WebSocket消息DTO对象
                WebSocketMessageDto webSocketMessageDto = new WebSocketMessageDto();
                webSocketMessageDto.setSessionKeys(List.of(serviceSessionKey));
                webSocketMessageDto.setMessage(JSONUtil.toJsonStr(wsMsg));
                WebSocketUtils.publishMessage(webSocketMessageDto);

                log.info("转发访客消息给客服成功 - visitorId: {}, content: {}",
                    adminId, content);

            } else {
                log.warn("客服会话不存在，无法转发访客消息 - visitorId: {}",
                    adminId);
            }

        } catch (Exception e) {
            log.error("转发访客消息给客服失败 - visitorId: {}", adminId, e);
        }
    }

    /**
     * 客服消息转发给访客
     */
    public void forwardServiceMessageToVisitor(String visitorId, Long merchantId, String serviceId, String content, String messageType) {
        try {

            String visitorSessionKey = buildSessionKey(merchantId, visitorId);
            if (StringUtils.isNotBlank(visitorSessionKey)) {
                // 构造符合现有格式的消息
                TextMessageData chatData = new TextMessageData();
                chatData.setType(MessageType.CHAT_MESSAGE);
                chatData.setMessageType(messageType);
                chatData.setContent(content);
                chatData.setMessageSendTime(System.currentTimeMillis());
                chatData.setVisitorId(visitorId);
                chatData.setVisitorName(visitorId);
                chatData.setVisitorAvatar("ufImgServer/neweggtest-mengjiala/20250723/fadd94704a3e4288aaf932dfbd15ecff.png");
                chatData.setServiceId(serviceId);
                chatData.setServiceName("客服昵称");
                chatData.setServiceAvatar("ufImgServer/neweggtest-mengjiala/20250723/fadd94704a3e4288aaf932dfbd15ecff.png");
                chatData.setDirection(MessageDirection.to_visitor.name());
                chatData.setBusinessId(merchantId);

                WebSocketTemplateMessage<TextMessageData> wsMsg = new WebSocketTemplateMessage<>();
                wsMsg.setEvent(MessageEvent.SERVICE_EVENT.getCode());  // 发给访客使用VISITOR_EVENT
                wsMsg.setData(chatData);

                WebSocketUtils.sendMessage(visitorSessionKey, JSONUtil.toJsonStr(wsMsg));

                log.info("转发客服消息给访客成功 - visitorId: {}, serviceId: {}, content: {}",
                    visitorId, serviceId, content);

            } else {
                log.warn("访客会话不存在，无法转发客服消息 - visitorId: {}, businessId: {}",
                    visitorId, null);
            }

        } catch (Exception e) {
            log.error("转发客服消息给访客失败 - visitorId: {}", visitorId, e);
        }
    }

    /**
     * 向访客发送分配成功消息
     */
    public void sendQueueSuccessToVisitor(Long merchantId, String visitorId, String message, Object serviceInfo) {
        try {
            String visitorSessionKey = buildSessionKey(merchantId, visitorId);

            // 构建分配成功消息
            TextMessageData data = new TextMessageData();
            data.setType(MessageType.QUEUE_SUCCESS);
            data.setContent(message);
            data.setServiceInfo(serviceInfo);
            data.setMessageSendTime(System.currentTimeMillis());

            WebSocketTemplateMessage<TextMessageData> statusMessage = new WebSocketTemplateMessage<>();
            statusMessage.setEvent(MessageEvent.VISITOR_EVENT.getCode());
            statusMessage.setData(data);

            WebSocketUtils.sendMessage(visitorSessionKey, JSONUtil.toJsonStr(statusMessage));

            log.info("发送分配成功消息给访客 - visitorId: {}", visitorId);

        } catch (Exception e) {
            log.error("发送分配成功消息失败 - visitorId: {}", visitorId, e);
        }
    }
}
