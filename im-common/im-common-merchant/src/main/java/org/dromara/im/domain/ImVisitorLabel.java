package org.dromara.im.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 访客标签对象 im_visitor_label
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_visitor_label")
public class ImVisitorLabel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 游客标签id
     */
    @TableId(value = "visitor_label_id")
    private Long visitorLabelId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 标签ID
     */
    private Long customLabelId;

    /**
     * 访客ID
     */
    private String visitorId;

    /**
     * 租户编号
     */
    private String tenantId;


}
