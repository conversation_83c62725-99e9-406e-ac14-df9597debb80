package org.dromara.im.service;

import org.dromara.im.domain.vo.ImVisitorLabelVo;
import org.dromara.im.domain.bo.ImVisitorLabelBo;
    import org.dromara.common.mybatis.core.page.TableDataInfo;
    import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 访客标签Service接口
 *
 * <AUTHOR> Li
 * @date 2025-07-20
 */
public interface IImVisitorLabelService {

    /**
     * 查询访客标签
     *
     * @param visitorLabelId 主键
     * @return 访客标签
     */
        ImVisitorLabelVo queryById(Long visitorLabelId);

        /**
         * 分页查询访客标签列表
         *
         * @param bo        查询条件
         * @param pageQuery 分页参数
         * @return 访客标签分页列表
         */
        TableDataInfo<ImVisitorLabelVo> queryPageList(ImVisitorLabelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的访客标签列表
     *
     * @param bo 查询条件
     * @return 访客标签列表
     */
    List<ImVisitorLabelVo> queryList(ImVisitorLabelBo bo);

    /**
     * 新增访客标签
     *
     * @param bo 访客标签
     * @return 是否新增成功
     */
    Boolean insertByBo(ImVisitorLabelBo bo);

    /**
     * 修改访客标签
     *
     * @param bo 访客标签
     * @return 是否修改成功
     */
    Boolean updateByBo(ImVisitorLabelBo bo);

    /**
     * 校验并批量删除访客标签信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增访客标签
     *
     * @param bos 访客标签列表
     * @return 是否新增成功
     */
    Boolean insertBatch(List<ImVisitorLabelBo> bos);

    List<ImVisitorLabelVo> queryVisitorLabels(List<String> visitorIds,Long merchantId);
}
