package org.dromara.im.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ImException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.im.domain.ImTranslationPackage;
import org.dromara.im.domain.bo.ImTranslationPackageBo;
import org.dromara.im.domain.vo.ImTranslationPackageVo;
import org.dromara.im.mapper.ImTranslationPackageMapper;
import org.dromara.im.service.IImTranslationPackageService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 翻译套餐Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImTranslationPackageServiceImpl implements IImTranslationPackageService {

    private final ImTranslationPackageMapper baseMapper;

    /**
     * 查询翻译套餐
     *
     * @param translationPackageId 主键
     * @return 翻译套餐
     */
    @Override
    public ImTranslationPackageVo queryById(Long translationPackageId) {
        return baseMapper.selectVoById(translationPackageId);
    }

    /**
     * 分页查询翻译套餐列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 翻译套餐分页列表
     */
    @Override
    public TableDataInfo<ImTranslationPackageVo> queryPageList(ImTranslationPackageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImTranslationPackage> lqw = buildQueryWrapper(bo);
        Page<ImTranslationPackageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的翻译套餐列表
     *
     * @param bo 查询条件
     * @return 翻译套餐列表
     */
    @Override
    public List<ImTranslationPackageVo> queryList(ImTranslationPackageBo bo) {
        LambdaQueryWrapper<ImTranslationPackage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImTranslationPackage> buildQueryWrapper(ImTranslationPackageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImTranslationPackage> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ImTranslationPackage::getSortOrder);
        lqw.eq(StringUtils.isNotBlank(bo.getPackageCode()), ImTranslationPackage::getPackageCode, bo.getPackageCode());
        lqw.like(StringUtils.isNotBlank(bo.getPackageName()), ImTranslationPackage::getPackageName, bo.getPackageName());
        lqw.eq(bo.getCharacterQuota() != null, ImTranslationPackage::getCharacterQuota, bo.getCharacterQuota());
        lqw.eq(bo.getPrice() != null, ImTranslationPackage::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencyType()), ImTranslationPackage::getCurrencyType, bo.getCurrencyType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), ImTranslationPackage::getPaymentMethod, bo.getPaymentMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getPackageType()), ImTranslationPackage::getPackageType, bo.getPackageType());
        lqw.eq(bo.getSortOrder() != null, ImTranslationPackage::getSortOrder, bo.getSortOrder());
        lqw.eq(bo.getStatus() != null, ImTranslationPackage::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImTranslationPackage::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 新增翻译套餐
     *
     * @param bo 翻译套餐
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImTranslationPackageBo bo) {
        ImTranslationPackage add = MapstructUtils.convert(bo, ImTranslationPackage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setTranslatePackageId(add.getTranslatePackageId());
        }
        return flag;
    }

    /**
     * 修改翻译套餐
     *
     * @param bo 翻译套餐
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImTranslationPackageBo bo) {
        ImTranslationPackage update = MapstructUtils.convert(bo, ImTranslationPackage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImTranslationPackage entity) {
        //TODO 做一些数据校验,如唯一约束
        Long l = baseMapper.selectCount(new LambdaQueryWrapper<>(ImTranslationPackage.class)
            .ne(Objects.nonNull(entity.getTranslatePackageId()), ImTranslationPackage::getTranslatePackageId, entity.getTranslatePackageId())
            .eq(ImTranslationPackage::getPackageName, entity.getPackageName())
        );
        if (l > 0) {
            throw new ImException("套餐名称已存在");
        }
    }

    /**
     * 校验并批量删除翻译套餐信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
