package org.dromara.im.websocket.handler;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.VisitorLoginUser;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.websocket.constant.WebSocketConstants;
import org.dromara.common.websocket.holder.WebSocketSessionHolder;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.*;
import org.dromara.im.domain.ImMerchant;
import org.dromara.im.domain.dto.ConversationQueuesDto;
import org.dromara.im.domain.dto.ImMessageDto;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImMerchantVo;
import org.dromara.im.domain.vo.ImVisitorVo;
import org.dromara.im.service.*;
import org.dromara.im.task.DialogRulesTask;
import org.dromara.im.utils.SendMessageUtil;
import org.dromara.im.utils.message.PersonMessageUtil;
import org.dromara.im.utils.message.QueueMessageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.IOException;
import java.util.Map;

/**
 * 访客WebSocket处理器
 * 处理第三方网站访客与客服系统的一对一聊天
 * 集成现有的消息格式和推送机制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VisitorWebSocketHandler extends AbstractWebSocketHandler {

    @Autowired
    private IImVisitorService visitorService;

    @Autowired
    private IImConversationService conversationService;

    @Autowired
    private IImMessageService messageService;

    @Autowired
    private IImAdminVisitorRelationService adminVisitorRelationService;

    @Autowired
    private IImMerchantService merchantService;

    @Autowired
    private DialogRulesTask dialogRulesTask;

    @Autowired
    private org.dromara.im.utils.message.PersonMessageUtil personMessageUtil;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws IOException {
        try {
            // 从拦截器传递的属性中获取登录用户信息
            VisitorLoginUser visitorLoginUser = (VisitorLoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);
            if (visitorLoginUser == null) {
                log.error("访客连接失败：无法获取登录用户信息");
                session.close(CloseStatus.BAD_DATA);
                return;
            }

            String visitorId = visitorLoginUser.getVisitorId();
            Long merchantId = visitorLoginUser.getMerchantId();

            // 获取访客信息
            ImVisitorVo visitorVo = visitorService.queryByVisitorIdAndBusinessId(visitorId, merchantId);
            if (visitorVo == null) {
                log.error("访客信息不存在 - visitorId: {}, merchantId: {}", visitorId, merchantId);
                session.close(CloseStatus.BAD_DATA);
                return;
            }

            // 创建或获取会话关系
            ImConversationVo conversation = createOrGetConversation(visitorVo);

            // 使用统一的会话管理器
            String sessionKey = SendMessageUtil.buildSessionKey(merchantId, visitorId);
            WebSocketSessionHolder.addSession(sessionKey, session);

            assert conversation != null;
            log.info("访客连接建立成功 - visitorId: {}, businessId: {}, conversationId: {}", visitorId, merchantId, conversation.getConversationId());

            // 客服连接成功以后更新一下用户状态-->更新用户状态为上线状态
            visitorService.handleVisitorOnlineStatus(visitorId, merchantId);

        } catch (Exception e) {
            log.error("访客连接建立失败", e);
            session.close(CloseStatus.SERVER_ERROR);
        }
    }


    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {

        VisitorLoginUser loginUser = (VisitorLoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);
        ImVisitorVo visitorVo = visitorService.queryByVisitorIdAndBusinessId(loginUser.getVisitorId(), loginUser.getMerchantId());
        ImConversationVo conversation = createOrGetConversation(visitorVo);

        String payload = message.getPayload();
        log.info("收到访客消息 - visitorId: {}, message: {}", visitorVo != null ? visitorVo.getVisitorId() : "unknown", payload);
        try {
            // 尝试解析JSON消息
            Map<String, Object> messageMap = JSONUtil.parseObj(payload);
            JSONObject data = JSONUtil.parseObj(messageMap.get("data"));
            String type = (String) data.get("type");
            if (MessageType.isChatMessage(type)) {
                handleVisitorChatMessage(visitorVo, conversation, data);
            }
        } catch (Exception e) {
            // JSON解析失败，按普通文本消息处理
            log.info("消息JSON解析失败，按普通文本处理: {}", payload);
        }
    }

    /**
     * 处理接收到的二进制消息
     *
     * @param session WebSocket会话
     * @param message 接收到的二进制消息
     * @throws Exception 处理消息过程中可能抛出的异常
     */
    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws Exception {
        super.handleBinaryMessage(session, message);
    }

    /**
     * 处理接收到的Pong消息（心跳监测）
     *
     * @param session WebSocket会话
     * @param message 接收到的Pong消息
     */
    @Override
    protected void handlePongMessage(WebSocketSession session, PongMessage message) {
        WebSocketUtils.sendPongMessage(session);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("[transport error] sessionId: {} , exception:{}", session.getId(), exception.getMessage());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        VisitorLoginUser visitorLoginUser = (VisitorLoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);
        if (visitorLoginUser != null) {

            String sessionKey = SendMessageUtil.buildSessionKey(visitorLoginUser.getMerchantId(), visitorLoginUser.getVisitorId());
            // 移除会话
            WebSocketSessionHolder.removeSession(sessionKey);

            // 更新访客离线状态
            visitorService.handleVisitorOffline(visitorLoginUser.getVisitorId(), visitorLoginUser.getMerchantId());

            log.info("访客连接关闭 - sessionKey:{}, visitorId: {}, visitorName: {}, closeStatus: {}",
                sessionKey, visitorLoginUser.getVisitorId(), visitorLoginUser.getVisitorName(), closeStatus);
        }
    }


    /**
     * 处理访客聊天消息（JSON格式）
     */
    private void handleVisitorChatMessage(ImVisitorVo visitorVo, ImConversationVo conversation, Map<String, Object> messageMap) {
        String content = (String) messageMap.get("content");
        String messageType = (String) messageMap.get("messageType");
        String clientMsgId = (String) messageMap.get("clientMsgId");
        String imageWidth = (String) messageMap.get("imageWidth");
        String imageHeight = (String) messageMap.get("imageHeight");

        try {
            if (content != null && conversation != null) {
                // 根据会话状态处理消息
                if (ConversationConstants.waiting.equals(conversation.getStatus())) {
                    // 排队中的访客发送消息
                    handleWaitingMessage(visitorVo, conversation, content, messageType, imageWidth, imageHeight, clientMsgId);
                } else if (ConversationConstants.active.equals(conversation.getStatus())) {
                    // 正常服务中的访客发送消息
                    handleTextMessage(conversation, content, messageType, imageWidth, imageHeight, clientMsgId);
                } else {
                    log.info("会话状态异常 - conversationId: {}, status: {}",
                        conversation.getConversationId(), conversation.getStatus());

                }
            } else if (conversation == null) {
                // 这种情况理论上不应该发生（系统异常）
                log.error("系统异常：访客没有会话对象 - visitorId: {}", visitorVo.getVisitorId());
            }

        } catch (Exception e) {
            log.error("处理访客聊天消息失败 - visitorId: {}", visitorVo.getVisitorId(), e);
            // 发送失败状态
        }
    }

    private void recordVisitorActivateAndMessageTime(String visitorId) {
        dialogRulesTask.recordCustomerMessage(visitorId);
        dialogRulesTask.recordCustomerActivity(visitorId);
    }

    /**
     * 处理排队中访客的消息
     */
    private void handleWaitingMessage(ImVisitorVo visitorVo, ImConversationVo conversation, String content,
                                      String messageType, String imageWidth, String imageHeight, String clientMsgId) {
        try {
            // 记录访客活跃状态和消息时间
            recordVisitorActivateAndMessageTime(visitorVo.getVisitorId());
            // 1. 保存消息（排队中的消息也要保存）
            ImMessageDto messageDto = buildVisitorImMessageDto(conversation, content, messageType, imageWidth, imageHeight, clientMsgId);
            messageService.saveMessage(messageDto);
            // 排在第一位，检查排队配置和接待容量后尝试分配客服
            queueCheckAndAssignService(conversation);
        } catch (Exception e) {
            log.error("处理排队消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextMessage(ImConversationVo conversation, String content, String messageType,
                                   String imageWidth, String imageHeight,String clientMsgId) {
        try {
            // 记录访客活跃状态和消息时间
            recordVisitorActivateAndMessageTime(conversation.getVisitorId());

            // 记录客户消息时间（用于无回复提醒判断）
            personMessageUtil.recordCustomerMessage(conversation.getVisitorId(), conversation.getConversationId());

            // 重置无回复提醒标记（客户发送了新消息）
            personMessageUtil.resetNoReplyReminderFlag(conversation.getConversationId());

            // 创建消息DTO
            ImMessageDto messageDto = buildVisitorImMessageDto(conversation, content, messageType, imageWidth, imageHeight, clientMsgId);

            // 保存消息到数据库
            saveMessageToDatabase(messageDto);

            // 更新会话信息
            updateConversationInfo(conversation, messageType, content);

            // 转发给客服
            forwardMessageToService(conversation, messageDto);

            // 更新访客活跃时间
            updateVisitorActivity(conversation);

            // 向访客发送消息发送成功状态
            String sessionKey = SendMessageUtil.buildSessionKey(conversation.getMerchantId(), conversation.getVisitorId());
            SendMessageUtil.sendMessageStatus(sessionKey, conversation.getVisitorId(), MessageStatus.sent, clientMsgId, messageType, MessageEvent.VISITOR__TO_SERVICE_EVENT);

            log.info("处理访客文本消息完成 - visitorId: {},adminId:{}, conversationId: {}, content: {}", conversation.getVisitorId(), conversation.getAdminId(), conversation.getConversationId(), content);

        } catch (Exception e) {
            log.error("处理访客文本消息失败 - visitorId: {}", conversation.getVisitorId(), e);
            // 发送失败状态
        }
    }

    private static ImMessageDto buildVisitorImMessageDto(ImConversationVo conversation, String content,
                                                         String messageType, String imageWidth, String imageHeight, String clientMsgId) {
        ImMessageDto messageDto = new ImMessageDto();
        messageDto.setMerchantId(conversation.getMerchantId());
        messageDto.setConversationId(conversation.getConversationId());
        messageDto.setSenderType(MessageSendType.visitor.name());
        messageDto.setSenderId(conversation.getVisitorId());
        messageDto.setSenderName(conversation.getVisitorName());
        messageDto.setSenderAvatar(conversation.getVisitorAvatar());
        messageDto.setReceiverType(MessageSendType.service.name());
        messageDto.setReceiverId(conversation.getAdminId() != null ? conversation.getAdminId().toString() : null);
        messageDto.setReceiverName(conversation.getAdminName());
        messageDto.setReceiverAvatar(conversation.getAdminAvatar());
        messageDto.setMessageType(messageType);
        messageDto.setContent(content);
        messageDto.setDirection(MessageDirection.to_service.name());
        messageDto.setTimestamp(System.currentTimeMillis());
        messageDto.setClientMsgId(clientMsgId);
        if (StringUtils.isNotBlank(imageHeight) && StringUtils.isNotBlank(imageWidth)) {
            messageDto.setImageHeight(imageHeight);
            messageDto.setImageWidth(imageWidth);
        }
        return messageDto;
    }


    /**
     * 创建或获取会话关系
     */
    /**
     * 创建或获取会话关系（优化版）
     * 确保所有访客都有会话对象，即使排队已满
     */
    private ImConversationVo createOrGetConversation(ImVisitorVo visitorVo) {
        try {
            String visitorId = visitorVo.getVisitorId();
            Long merchantId = visitorVo.getMerchantId();

            // 1. 检查是否已存在活跃会话
            ImConversationVo existingConversation = conversationService.queryActiveByVisitorAndBusiness(visitorId, merchantId);
            if (existingConversation != null) {
                log.info("使用现有会话 - conversationId: {}, visitorId: {}, status: {}",
                    existingConversation.getConversationId(), visitorId, existingConversation.getStatus());
                return existingConversation;
            }

            // 2. 检查是否在排队中
            ImConversationVo queuedConversation = conversationService.findQueuedConversation(visitorId, merchantId);
            if (queuedConversation != null) {
                log.info("访客已在排队中 - conversationId: {}, visitorId: {}", queuedConversation.getConversationId(), visitorId);

                // 尝试为排队中的访客分配客服
                return queueCheckAndAssignService(queuedConversation);
            }

            // 3. 创建新会话（无论排队是否已满都创建）
            ImConversationVo newConversation = conversationService.createConversationInWaitingState(visitorVo);

            if (newConversation == null) {
                log.error("创建会话失败 - visitorId: {}, merchantId: {}", visitorId, merchantId);
                return null;
            }

            log.info("创建新会话成功 - conversationId: {}, visitorId: {}, status: waiting", newConversation.getConversationId(), visitorId);

            // 4. 检查排队配置，决定是否可以立即分配客服
            return queueCheckAndAssignService(newConversation);

        } catch (Exception e) {
            log.error("创建或获取会话异常 - visitorId: {}", visitorVo.getVisitorId(), e);
            return null;
        }
    }

    private ImConversationVo queueCheckAndAssignService(ImConversationVo newConversation) {
        boolean canAssignImmediately = checkQueueConfigBeforeCreate(newConversation);
        if (canAssignImmediately) {
            // 可以立即尝试分配客服
            return handleNewConversationAssignment(newConversation);
        } else {
            // 排队已满，但仍然创建会话，只是不分配客服
            QueueMessageUtil.sendQueueFullMessage(newConversation);
            log.info("排队已满，会话已创建但暂不分配客服 - conversationId: {}, visitorId: {}", newConversation.getConversationId(), newConversation.getVisitorId());
            return newConversation;
        }
    }


    /**
     * 处理新会话的客服分配
     */
    private ImConversationVo handleNewConversationAssignment(ImConversationVo newConversation) {
        String visitorId = newConversation.getVisitorId();
        int queuePosition = conversationService.getQueuePosition(visitorId, newConversation.getMerchantId());
        if (queuePosition == 1) {
            // 立即尝试分配客服
            Long merchantId = newConversation.getMerchantId();
            Long merchantChannelId = newConversation.getMerchantChannelId();
            boolean assigned = tryAssignService(newConversation, merchantId, merchantChannelId);
            if (assigned) {
                // 分配成功，更新状态为active
                conversationService.updateConversationStatus(newConversation.getConversationId(), ConversationConstants.active);
                newConversation.setStatus(ConversationConstants.active);
                // 处理分配成功以后如果有关系则重新起到关系
                adminVisitorRelationService.createOrUpdateGetAdminVisitorRelation(newConversation.getAdminId(), visitorId, merchantId, merchantChannelId);
                // 更新所有消息的接受者id
                messageService.updateMessageAdminId(visitorId, newConversation.getConversationId(), newConversation.getAdminId());
                // 通知客服
                SendMessageUtil.notifyServiceNewVisitor(visitorId, newConversation.getAdminId());
                // 发送分配成功消息
                PersonMessageUtil.sendWelcomeMessage(newConversation);
                log.info("新会话创建并分配客服成功 - conversationId: {}, visitorId: {}", newConversation.getConversationId(), visitorId);
            }
        } else {
            // 分配失败，保持waiting状态，发送排队消息
            QueueMessageUtil.sendQueueWaitingMessage(newConversation, queuePosition);
            log.info("新会话创建但需排队 - conversationId: {}, visitorId: {}, position: {}", newConversation.getConversationId(), visitorId, queuePosition);
        }

        return newConversation;
    }


    /**
     * 保存消息到数据库
     */
    private void saveMessageToDatabase(ImMessageDto messageDto) {
        try {
            if (StringUtils.equals(messageDto.getMessageType(), "evaluate")) {
                return;
            }
            messageService.saveMessage(messageDto);
            log.info("消息保存成功 - conversationId: {}, senderId: {}, messageType: {}",
                messageDto.getConversationId(), messageDto.getSenderId(), messageDto.getMessageType());
        } catch (Exception e) {
            log.error("消息保存失败 - conversationId: {}, senderId: {}",
                messageDto.getConversationId(), messageDto.getSenderId(), e);
        }
    }

    /**
     * 更新会话信息
     */
    private void updateConversationInfo(ImConversationVo conversation, String messageType, String lastMessage) {
        try {
            if (StringUtils.equals(messageType, "evaluate")) {
                return;
            }
            conversationService.updateLastMessage(conversation.getConversationId(), lastMessage);
            conversationService.incrementMessageCount(conversation.getConversationId());
        } catch (Exception e) {
            log.error("更新会话信息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 转发消息给客服
     */
    private void forwardMessageToService(ImConversationVo conversation, ImMessageDto messageDto) {
        if (conversation.getAdminId() != null) {
            try {
                SendMessageUtil.forwardVisitorMessageToService(conversation, messageDto);
                log.info("消息转发给客服成功 - visitorId: {}, adminId: {}", conversation.getVisitorId(), conversation.getAdminId());
            } catch (Exception e) {
                log.error("消息转发失败 - visitorId: {}, adminId: {}", conversation.getVisitorId(), conversation.getAdminId(), e);
            }
        } else {
            log.info("会话尚未分配客服，消息已保存 - conversationId: {}, visitorId: {}", conversation.getConversationId(), conversation.getVisitorId());
        }
    }

    /**
     * 更新访客活跃时间
     */
    private void updateVisitorActivity(ImConversationVo conversation) {
        try {
            visitorService.updateVisitorMessageTime(conversation.getVisitorId(), conversation.getMerchantId());
        } catch (Exception e) {
            log.error("更新访客消息时间失败 - visitorId: {}", conversation.getVisitorId(), e);
        }
    }

    /**
     * 在创建会话前检查排队配置
     */
    private boolean checkQueueConfigBeforeCreate(ImConversationVo conversation) {
        Long merchantId = conversation.getMerchantId();
        try {
            // 1. 获取商户的排队配置
            ImMerchantVo merchant = merchantService.queryImMerchant(merchantId);
            ConversationQueuesDto queueConfig = merchantService.queryConversationQueues(merchant);
            log.info("获取排队配置 - merchantId: {}, config: {}", merchantId, queueConfig);

            // 2. 检查是否开启排队
            if (queueConfig == null || !ConversationQueuesDto.enableQueue(queueConfig)) {
                log.info("排队功能未开启，直接创建会话 - merchantId: {}, enableQueue: {}",
                    merchantId, queueConfig != null ? queueConfig.getEnableQueue() : "null");
                return true;
            }

            // 3. 开启了排队，需要检查排队容量
            log.info("排队功能已开启，检查排队容量 - merchantId: {}", merchantId);

            // 4. 检查当前活跃会话数量（排队中 + 接待中）
            int activeConversations = getCurrentQueueSize(merchantId);
            // 写死最大活跃会话数为5，便于测试
            int maxActiveConversations = merchant.getConversationQueues() == null ? 5 : ImMerchantVo.getCurrentPackage(merchant).getChatNumber();
            log.info("活跃会话检查 - merchantId: {}, active: {} (waiting+serving), max: {} (写死)", merchantId, activeConversations, maxActiveConversations);

            if (activeConversations >= maxActiveConversations) {
                // 活跃会话已满，发送排队满提示，不创建会话
                QueueMessageUtil.sendQueueFullMessage(conversation);
                log.info("活跃会话已满，拒绝新访客，不创建会话 - active: {}, max: {}", activeConversations, maxActiveConversations);
                return false;
            }

            // 5. 活跃会话未满，可以创建会话
            log.info("活跃会话未满，允许创建会话 - active: {}, max: {}", activeConversations, maxActiveConversations);
            return true;

        } catch (Exception e) {
            log.error("排队配置检查失败 - merchantId: {}", merchantId, e);
            // 异常情况下，允许创建会话
            return true;
        }
    }

    /**
     * 尝试分配客服（优化版）
     * 确保分配成功时同步更新数据库中的adminId
     */
    private boolean tryAssignService(ImConversationVo conversation, Long merchantId, Long merchantChannelId) {
        try {
            ImMerchantAdministratorVo assignedService = conversationService.autoAssignService(
                conversation.getConversationId(), merchantId, merchantChannelId, null);

            if (assignedService != null) {
                // 更新内存对象
                conversation.setAdminId(assignedService.getAdminId());
                conversation.setAdminName(assignedService.getNickname());
                conversation.setAdminAvatar(assignedService.getAvatar());

                // 同步更新数据库中的adminId
                boolean updated = conversationService.updateConversationAdmin(conversation.getConversationId(), assignedService);

                if (!updated) {
                    log.info("更新会话客服ID失败，但分配仍然有效 - conversationId: {}, adminId: {}",
                        conversation.getConversationId(), assignedService.getAdminId());
                }

                // 发送分配成功通知
                try {
                    ImMerchantVo merchant = merchantService.queryImMerchant(conversation.getMerchantId());
                    ConversationQueuesDto queueConfig = merchantService.queryConversationQueues(merchant);
                    if (queueConfig != null && "1".equals(queueConfig.getQueueSuccessNotification())) {
                        QueueMessageUtil.sendQueueSuccessToVisitor(conversation);
                    }
                } catch (Exception e) {
                    log.info("发送分配成功通知失败", e);
                }

                log.info("客服分配成功 - conversationId: {}, serviceId: {}, adminName: {}",
                    conversation.getConversationId(), assignedService.getAdminId(), assignedService.getNickname());
                return true;
            } else {
                log.info("暂无可用客服 - conversationId: {}", conversation.getConversationId());
                return false;
            }
        } catch (Exception e) {
            log.error("客服分配异常 - conversationId: {}", conversation.getConversationId(), e);
            return false;
        }
    }

    /**
     * 获取当前活跃会话数量（排队中 + 接待中）
     * 用于判断是否还能接受新的访客连接
     */
    private int getCurrentQueueSize(Long merchantId) {
        try {
            // 查询当前活跃会话数量（waiting + active）
            return conversationService.countWaitingConversations(merchantId);
        } catch (Exception e) {
            log.error("获取活跃会话数量失败 - merchantId: {}", merchantId, e);
            return 0;
        }
    }


}
