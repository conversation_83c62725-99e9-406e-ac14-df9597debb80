package org.dromara.im.websocket.handler;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.VisitorLoginUser;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.websocket.constant.WebSocketConstants;
import org.dromara.common.websocket.holder.WebSocketSessionHolder;
import org.dromara.common.websocket.utils.WebSocketUtils;
import org.dromara.im.constans.*;
import org.dromara.im.domain.dto.ConversationQueuesDto;
import org.dromara.im.domain.dto.ImMessageDto;
import org.dromara.im.domain.vo.ImConversationVo;
import org.dromara.im.domain.vo.ImMerchantAdministratorVo;
import org.dromara.im.domain.vo.ImVisitorVo;
import org.dromara.im.service.*;
import org.dromara.im.utils.SendMessageUtil;
import org.dromara.im.websocket.service.WebSocketMessageRouter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 访客WebSocket处理器
 * 处理第三方网站访客与客服系统的一对一聊天
 * 集成现有的消息格式和推送机制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VisitorWebSocketHandler extends AbstractWebSocketHandler {

    @Autowired
    private IImVisitorService visitorService;

    @Autowired
    private IImConversationService conversationService;

    @Autowired
    private IImMessageService messageService;

    @Autowired
    private WebSocketMessageRouter messageRouter;

    @Autowired
    private IImAdminVisitorRelationService imAdminVisitorRelationService;

    @Autowired
    private IImMerchantService merchantService;

    @Autowired
    private org.dromara.im.task.DialogRulesTask dialogRulesTask;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws IOException {
        try {
            // 从拦截器传递的属性中获取登录用户信息
            VisitorLoginUser visitorLoginUser = (VisitorLoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);
            if (visitorLoginUser == null) {
                log.error("访客连接失败：无法获取登录用户信息");
                session.close(CloseStatus.BAD_DATA);
                return;
            }

            String visitorId = visitorLoginUser.getVisitorId();
            Long merchantId = visitorLoginUser.getMerchantId();

            // 获取访客信息
            ImVisitorVo visitorVo = visitorService.queryByVisitorIdAndBusinessId(visitorId, merchantId);
            if (visitorVo == null) {
                log.error("访客信息不存在 - visitorId: {}, merchantId: {}", visitorId, merchantId);
                session.close(CloseStatus.BAD_DATA);
                return;
            }

            // 创建或获取会话关系
            ImConversationVo conversation = createOrGetConversation(visitorVo, session);
            // conversation 现在始终不为null（除非系统异常），已移除null检查

            // 创建客服--游客关系
            if (Objects.nonNull(conversation.getAdminId())) {
                createOrGetAdminVisitorRelation(conversation, visitorVo.getMerchantId(), visitorVo.getMerchantChannelId());
            }

            String sessionKey = WebSocketMessageRouter.buildSessionKey(merchantId, visitorId);
            // 将相关信息存储到session属性中
            session.getAttributes().put("conversation", conversation);

            // 使用统一的会话管理器
            WebSocketSessionHolder.addSession(sessionKey, session);

            log.info("访客连接建立成功 - visitorId: {}, businessId: {}, conversationId: {}", visitorId, merchantId, conversation.getConversationId());

            // 客服连接成功以后更新一下用户状态-->更新用户状态为上线状态
            visitorService.handleVisitorOnlineStatus(visitorId, merchantId);

        } catch (Exception e) {
            log.error("访客连接建立失败", e);
            session.close(CloseStatus.SERVER_ERROR);
        }
    }


    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {

        VisitorLoginUser visitorVo = (VisitorLoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);
        ImConversationVo conversation = (ImConversationVo) session.getAttributes().get("conversation");

        String payload = message.getPayload();
        log.info("收到访客消息 - visitorId: {}, message: {}", visitorVo != null ? visitorVo.getVisitorId() : "unknown", payload);
        try {
            // 尝试解析JSON消息
            Map<String, Object> messageMap = JSONUtil.parseObj(payload);
            JSONObject data = JSONUtil.parseObj(messageMap.get("data"));
            String type = (String) data.get("type");
            if (MessageType.CHAT_MESSAGE.equals(type)) {
                handleVisitorChatMessage(session, visitorVo, conversation, data);
            }
        } catch (Exception e) {
            // JSON解析失败，按普通文本消息处理
            log.info("消息JSON解析失败，按普通文本处理: {}", payload);
        }
    }

    /**
     * 处理接收到的二进制消息
     *
     * @param session WebSocket会话
     * @param message 接收到的二进制消息
     * @throws Exception 处理消息过程中可能抛出的异常
     */
    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws Exception {
        super.handleBinaryMessage(session, message);
    }

    /**
     * 处理接收到的Pong消息（心跳监测）
     *
     * @param session WebSocket会话
     * @param message 接收到的Pong消息
     */
    @Override
    protected void handlePongMessage(WebSocketSession session, PongMessage message) {
        WebSocketUtils.sendPongMessage(session);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("[transport error] sessionId: {} , exception:{}", session.getId(), exception.getMessage());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        VisitorLoginUser visitorLoginUser = (VisitorLoginUser) session.getAttributes().get(WebSocketConstants.LOGIN_USER_KEY);

        if (visitorLoginUser != null) {

            String sessionKey = WebSocketMessageRouter.buildSessionKey(visitorLoginUser.getMerchantId(), visitorLoginUser.getVisitorId());
            // 移除会话
            WebSocketSessionHolder.removeSession(sessionKey);

            // 更新访客离线状态
            visitorService.handleVisitorOffline(visitorLoginUser.getVisitorId(), visitorLoginUser.getMerchantId());

            log.info("访客连接关闭 - sessionKey:{}, visitorId: {}, visitorName: {}, closeStatus: {}",
                sessionKey, visitorLoginUser.getVisitorId(), visitorLoginUser.getVisitorName(), closeStatus);
        }
    }


    private void createOrGetAdminVisitorRelation(ImConversationVo conversation, Long merchantId, Long merchantChannelId) {
        imAdminVisitorRelationService.createOrGetAdminVisitorRelation(conversation.getAdminId(), conversation.getVisitorId(), merchantId, merchantChannelId);
    }

    /**
     * 处理访客聊天消息（JSON格式）
     */
    private void handleVisitorChatMessage(WebSocketSession session, VisitorLoginUser visitorVo,
                                          ImConversationVo conversation, Map<String, Object> messageMap) {
        String content = (String) messageMap.get("content");
        String messageType = (String) messageMap.get("messageType");
        String clientMsgId = (String) messageMap.get("clientMsgId");
        String imageWidth = (String) messageMap.get("imageWidth");
        String imageHeight = (String) messageMap.get("imageHeight");

        try {
            if (content != null && conversation != null) {
                // 根据会话状态处理消息
                if (ConversationConstants.waiting.equals(conversation.getStatus())) {
                    // 排队中的访客发送消息
                    handleWaitingMessage(session, visitorVo, conversation, content, clientMsgId, messageType, imageWidth, imageHeight);
                } else if (ConversationConstants.active.equals(conversation.getStatus())) {
                    // 正常服务中的访客发送消息
                    handleTextMessage(visitorVo, conversation, content, clientMsgId, messageType, imageWidth, imageHeight);
                } else {
                    log.warn("会话状态异常 - conversationId: {}, status: {}",
                        conversation.getConversationId(), conversation.getStatus());

                }
            } else if (conversation == null) {
                // 这种情况理论上不应该发生（系统异常）
                log.error("系统异常：访客没有会话对象 - visitorId: {}", visitorVo.getVisitorId());
            }

        } catch (Exception e) {
            log.error("处理访客聊天消息失败 - visitorId: {}", visitorVo.getVisitorId(), e);
            // 发送失败状态
        }
    }

    /**
     * 处理排队中访客的消息
     */
    private void handleWaitingMessage(WebSocketSession session, VisitorLoginUser visitorVo, ImConversationVo conversation,
                                      String content, String clientMsgId, String messageType, String imageWidth, String imageHeight) {
        try {
            // 记录访客活跃状态和消息时间
            String visitorId = visitorVo.getVisitorId();
            Long merchantId = visitorVo.getMerchantId();
            dialogRulesTask.recordCustomerActivity(visitorId);
            dialogRulesTask.recordCustomerMessage(visitorId);
            // 1. 保存消息（排队中的消息也要保存）
            ImMessageDto messageDto = new ImMessageDto();
            messageDto.setConversationId(conversation.getConversationId());
            messageDto.setMerchantId(merchantId);
            messageDto.setSenderId(visitorId);
            messageDto.setSenderName(visitorVo.getVisitorName());
            messageDto.setSenderType(MessageSendType.visitor.name());
            messageDto.setReceiverId(conversation.getAdminId() != null ? conversation.getAdminId().toString() : null);
            messageDto.setReceiverType(MessageSendType.service.name());
            messageDto.setMessageType(messageType);
            messageDto.setContent(content);
            messageDto.setDirection(MessageDirection.to_service.name());
            messageDto.setTimestamp(System.currentTimeMillis());

            messageService.saveMessage(messageDto.getConversationId(), MessageSendType.visitor.name(), messageDto.getSenderId(),
                messageDto.getSenderName(), MessageSendType.service.name(), messageDto.getReceiverId(), messageDto.getMessageType(),
                messageDto.getContent(), messageDto.getClientMsgId(), imageWidth, imageHeight);

            // 2. 检查排队顺序，只有排在第一位的访客才能尝试分配客服
            int queuePosition = conversationService.getQueuePosition(visitorId, merchantId);
            boolean assigned = false;

            if (queuePosition == 1) {
                // 排在第一位，检查排队配置和接待容量后尝试分配客服
                boolean canAssignImmediately = checkQueueConfigBeforeCreate(merchantId, session);

                if (canAssignImmediately) {
                    Long merchantChannelId = conversation.getMerchantChannelId();
                    assigned = tryAssignService(conversation, merchantId, merchantChannelId, session);
                    log.debug("排队第一位访客尝试分配客服 - visitorId: {}, assigned: {}", visitorId, assigned);
                } else {
                    log.debug("排队配置检查未通过，暂不分配客服 - visitorId: {}, position: {}", visitorId, queuePosition);
                }
            } else {
                // 不是第一位，不能分配客服
                log.debug("访客不在排队第一位，暂不分配客服 - visitorId: {}, position: {}", visitorId, queuePosition);
            }

            if (assigned) {
                // 分配成功，更新状态并转发消息
                conversationService.updateConversationStatus(conversation.getConversationId(), ConversationConstants.active);
                conversation.setStatus(ConversationConstants.active);

                // 更新所有消息的接受者id
                messageService.updateMessageAdminId(visitorId, conversation.getConversationId(), conversation.getAdminId());

                // 通知客服
                SendMessageUtil.notifyServiceNewVisitor(visitorId, conversation.getAdminId());
                // 发送分配成功消息
                SendMessageUtil.sendServiceAssignedMessage(session, conversation);

                log.info("排队访客发送消息后成功分配客服 - conversationId: {}, adminId: {}", conversation.getConversationId(), conversation.getAdminId());
            } else {
                // 仍需排队，发送排队状态（使用之前计算的排队位置）
                sendMessageStatusToVisitor(visitorVo, "queued",
                    "您的消息已收到，当前排队位置：第" + queuePosition + "位", clientMsgId);

                log.info("排队访客发送消息，消息已保存但仍需等待 - conversationId: {}, position: {}",
                    conversation.getConversationId(), queuePosition);
            }

        } catch (Exception e) {
            log.error("处理排队消息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextMessage(VisitorLoginUser visitorVo, ImConversationVo conversation, String content,
                                   String clientMsgId, String messageType, String imageWidth, String imageHeight) {
        try {
            // 记录访客活跃状态和消息时间
            dialogRulesTask.recordCustomerActivity(visitorVo.getVisitorId());
            dialogRulesTask.recordCustomerMessage(visitorVo.getVisitorId());
            // 创建消息DTO
            ImMessageDto messageDto = buildImMessageDto(visitorVo, conversation, content, messageType, clientMsgId);

            // 设置额外信息
            messageDto.setMerchantId(conversation.getMerchantId());
            messageDto.setDirection(MessageDirection.to_service.name());
            messageDto.setSenderType(MessageSendType.visitor.name());
            messageDto.setReceiverType(MessageSendType.service.name());
            messageDto.setImageWidth(imageWidth);
            messageDto.setImageHeight(imageHeight);

            // 保存消息到数据库
            saveMessageToDatabase(messageDto);

            // 更新会话信息
            updateConversationInfo(conversation, messageType, content);

            // 转发给客服
            forwardMessageToService(visitorVo, conversation, messageDto);

            // 更新访客活跃时间
            updateVisitorActivity(visitorVo, conversation);

            // 向访客发送消息发送成功状态
            sendMessageStatusToVisitor(visitorVo, MessageStatus.sent, "send success", clientMsgId);

            log.info("处理访客文本消息完成 - visitorId: {}, conversationId: {}, content: {}",
                visitorVo.getVisitorId(), conversation.getConversationId(), content);

        } catch (Exception e) {
            log.error("处理访客文本消息失败 - visitorId: {}", visitorVo.getVisitorId(), e);
            // 发送失败状态
        }
    }

    private static ImMessageDto buildImMessageDto(VisitorLoginUser visitorVo, ImConversationVo conversation,
                                                  String content, String messageType, String clientMsgId) {
        return ImMessageDto.builder()
            .messageType(messageType)
            .content(content)
            .senderId(visitorVo.getVisitorId())
            .senderName(visitorVo.getVisitorName())
            .receiverId(conversation.getAdminId() != null ? conversation.getAdminId().toString() : null)
            .conversationId(conversation.getConversationId())
            .status(MessageStatus.sent)
            .timestamp(System.currentTimeMillis())
            .clientMsgId(clientMsgId)
            .build();
    }


    /**
     * 创建或获取会话关系
     */
    /**
     * 创建或获取会话关系（优化版）
     * 确保所有访客都有会话对象，即使排队已满
     */
    private ImConversationVo createOrGetConversation(ImVisitorVo visitorVo, WebSocketSession session) {
        try {
            String visitorId = visitorVo.getVisitorId();
            Long merchantId = visitorVo.getMerchantId();

            // 1. 检查是否已存在活跃会话
            ImConversationVo existingConversation = conversationService.queryActiveByVisitorAndBusiness(visitorId, merchantId);
            if (existingConversation != null) {
                log.info("使用现有会话 - conversationId: {}, visitorId: {}, status: {}",
                    existingConversation.getConversationId(), visitorId, existingConversation.getStatus());
                return existingConversation;
            }

            // 2. 检查是否在排队中
            ImConversationVo queuedConversation = conversationService.findQueuedConversation(visitorId, merchantId);
            if (queuedConversation != null) {
                log.info("访客已在排队中 - conversationId: {}, visitorId: {}", queuedConversation.getConversationId(), visitorId);

                // 尝试为排队中的访客分配客服
                return handleQueuedConversation(queuedConversation, visitorId, merchantId, visitorVo.getMerchantChannelId(), session);
            }

            // 3. 创建新会话（无论排队是否已满都创建）
            ImConversationVo newConversation = conversationService.createConversationInWaitingState(visitorId, visitorVo.getVisitorName(), merchantId, visitorVo.getMerchantChannelId());

            if (newConversation == null) {
                log.error("创建会话失败 - visitorId: {}, merchantId: {}", visitorId, merchantId);
                return null;
            }

            log.info("创建新会话成功 - conversationId: {}, visitorId: {}, status: waiting",
                newConversation.getConversationId(), visitorId);

            // 4. 检查排队配置，决定是否可以立即分配客服
            boolean canAssignImmediately = checkQueueConfigBeforeCreate(merchantId, session);

            if (canAssignImmediately) {
                // 可以立即尝试分配客服
                return handleNewConversationAssignment(newConversation, visitorId, merchantId, visitorVo.getMerchantChannelId(), session);
            } else {
                // 排队已满，但仍然创建会话，只是不分配客服
                sendQueueFullMessage(session, "当前排队人数已满，您已加入等待队列");
                log.info("排队已满，会话已创建但暂不分配客服 - conversationId: {}, visitorId: {}",
                    newConversation.getConversationId(), visitorId);
                return newConversation;
            }

        } catch (Exception e) {
            log.error("创建或获取会话异常 - visitorId: {}", visitorVo.getVisitorId(), e);
            return null;
        }
    }

    /**
     * 处理排队中的会话
     */
    private ImConversationVo handleQueuedConversation(ImConversationVo queuedConversation, String visitorId,
                                                      Long merchantId, Long channelId, WebSocketSession session) {
        // 检查排队位置，只有排在第一位的访客才能尝试分配客服
        int queuePosition = conversationService.getQueuePosition(visitorId, merchantId);
        boolean assigned = false;

        if (queuePosition == 1) {
            // 排在第一位，检查排队配置后尝试分配客服
            boolean canAssignImmediately = checkQueueConfigBeforeCreate(merchantId, session);

            if (canAssignImmediately) {
                assigned = tryAssignService(queuedConversation, merchantId, channelId, session);
                log.debug("排队第一位访客尝试分配客服 - visitorId: {}, assigned: {}", visitorId, assigned);
            } else {
                log.debug("排队配置检查未通过，暂不分配客服 - visitorId: {}, position: {}", visitorId, queuePosition);
            }
        } else {
            // 不是第一位，不能分配客服
            log.debug("访客不在排队第一位，暂不分配客服 - visitorId: {}, position: {}", visitorId, queuePosition);
        }

        if (assigned) {
            // 分配成功，更新状态为active
            conversationService.updateConversationStatus(queuedConversation.getConversationId(), ConversationConstants.active);
            queuedConversation.setStatus(ConversationConstants.active);
            // 同事客服新的访客
            SendMessageUtil.notifyServiceNewVisitor(visitorId, queuedConversation.getAdminId());
            // 通知访客分配成功
            SendMessageUtil.sendServiceAssignedMessage(session, queuedConversation);
            log.info("排队访客分配客服成功 - conversationId: {}, visitorId: {}",
                queuedConversation.getConversationId(), visitorId);
        } else {
            // 仍需排队，发送排队位置（使用之前计算的位置）
            sendQueueWaitingMessage(session, null, queuePosition);
            log.info("排队访客仍需等待 - conversationId: {}, visitorId: {}, position: {}",
                queuedConversation.getConversationId(), visitorId, queuePosition);
        }

        return queuedConversation;
    }

    /**
     * 处理新会话的客服分配
     */
    private ImConversationVo handleNewConversationAssignment(ImConversationVo newConversation, String visitorId,
                                                             Long merchantId, Long channelId, WebSocketSession session) {
        // 立即尝试分配客服
        boolean assigned = tryAssignService(newConversation, merchantId, channelId, session);

        if (assigned) {
            // 分配成功，更新状态为active
            conversationService.updateConversationStatus(newConversation.getConversationId(), ConversationConstants.active);
            newConversation.setStatus(ConversationConstants.active);
            SendMessageUtil.notifyServiceNewVisitor(visitorId, newConversation.getAdminId());
            SendMessageUtil.sendServiceAssignedMessage(session, newConversation);
            log.info("新会话创建并分配客服成功 - conversationId: {}, visitorId: {}",
                newConversation.getConversationId(), visitorId);
        } else {
            // 分配失败，保持waiting状态，发送排队消息
            int queuePosition = conversationService.getQueuePosition(visitorId, merchantId);
            sendQueueWaitingMessage(session, null, queuePosition);
            log.info("新会话创建但需排队 - conversationId: {}, visitorId: {}, position: {}",
                newConversation.getConversationId(), visitorId, queuePosition);
        }

        return newConversation;
    }


    /**
     * 保存消息到数据库
     */
    private void saveMessageToDatabase(ImMessageDto messageDto) {
        try {
            if (StringUtils.equals(messageDto.getMessageType(), "evaluate")) {
                return;
            }
            messageService.saveMessage(messageDto.getConversationId(), MessageSendType.visitor.name(), messageDto.getSenderId(),
                messageDto.getSenderName(), MessageSendType.service.name(), messageDto.getReceiverId(), messageDto.getMessageType(),
                messageDto.getContent(), messageDto.getClientMsgId(), messageDto.getImageWidth(), messageDto.getImageHeight());

            log.info("消息保存成功 - conversationId: {}, senderId: {}, messageType: {}",
                messageDto.getConversationId(), messageDto.getSenderId(), messageDto.getMessageType());
        } catch (Exception e) {
            log.error("消息保存失败 - conversationId: {}, senderId: {}",
                messageDto.getConversationId(), messageDto.getSenderId(), e);
        }
    }

    /**
     * 更新会话信息
     */
    private void updateConversationInfo(ImConversationVo conversation, String messageType, String lastMessage) {
        try {
            if (StringUtils.equals(messageType, "evaluate")) {
                return;
            }
            conversationService.updateLastMessage(conversation.getConversationId(), lastMessage);
            conversationService.incrementMessageCount(conversation.getConversationId());
        } catch (Exception e) {
            log.error("更新会话信息失败 - conversationId: {}", conversation.getConversationId(), e);
        }
    }

    /**
     * 转发消息给客服
     */
    private void forwardMessageToService(VisitorLoginUser visitorVo, ImConversationVo conversation,
                                         ImMessageDto messageDto) {
        if (conversation.getAdminId() != null) {
            try {
                messageRouter.forwardVisitorMessageToService(visitorVo, messageDto.getMessageType(), conversation.getAdminId(),
                    messageDto.getContent() != null ? messageDto.getContent() : "[媒体消息]"
                );
                log.info("消息转发给客服成功 - visitorId: {}, adminId: {}",
                    visitorVo.getVisitorId(), conversation.getAdminId());
            } catch (Exception e) {
                log.error("消息转发失败 - visitorId: {}, adminId: {}",
                    visitorVo.getVisitorId(), conversation.getAdminId(), e);
            }
        } else {
            log.info("会话尚未分配客服，消息已保存 - conversationId: {}, visitorId: {}",
                conversation.getConversationId(), visitorVo.getVisitorId());
        }
    }

    /**
     * 更新访客活跃时间
     */
    private void updateVisitorActivity(VisitorLoginUser visitorVo, ImConversationVo conversation) {
        try {
            visitorService.updateVisitorMessageTime(visitorVo.getVisitorId(), conversation.getMerchantId());
        } catch (Exception e) {
            log.error("更新访客消息时间失败 - visitorId: {}", visitorVo.getVisitorId(), e);
        }
    }

    /**
     * 向访客发送消息状态确认
     */
    private void sendMessageStatusToVisitor(VisitorLoginUser visitorVo, String status, String message, String clientMsgId) {
        try {
            String visitorSessionKey = WebSocketMessageRouter.buildSessionKey(visitorVo.getMerchantId(), visitorVo.getVisitorId());

            SendMessageUtil.sendMessageStatus(visitorSessionKey, visitorVo.getVisitorId(), status, message, clientMsgId, MessageEvent.VISITOR_EVENT);
            log.info("发送消息状态给访客 - visitorId: {}, status: {}",
                visitorVo.getVisitorId(), status);

        } catch (Exception e) {
            log.error("发送消息状态失败 - visitorId: {}",
                visitorVo.getVisitorId(), e);
        }
    }

    /**
     * 在创建会话前检查排队配置
     */
    private boolean checkQueueConfigBeforeCreate(Long merchantId, WebSocketSession session) {
        try {
            // 1. 获取商户的排队配置
            ConversationQueuesDto queueConfig = merchantService.queryConversationQueues(merchantId);
            log.info("获取排队配置 - merchantId: {}, config: {}", merchantId, queueConfig);

            // 2. 检查是否开启排队
            if (queueConfig == null || !"1".equals(queueConfig.getEnableQueue())) {
                log.info("排队功能未开启，直接创建会话 - merchantId: {}, enableQueue: {}",
                    merchantId, queueConfig != null ? queueConfig.getEnableQueue() : "null");
                return true;
            }

            // 3. 开启了排队，需要检查排队容量
            log.info("排队功能已开启，检查排队容量 - merchantId: {}", merchantId);

            // 4. 检查当前活跃会话数量（排队中 + 接待中）
            int activeConversations = getCurrentQueueSize(merchantId);
            // 写死最大活跃会话数为5，便于测试
            int maxActiveConversations = 5;

            log.info("活跃会话检查 - merchantId: {}, active: {} (waiting+serving), max: {} (写死)",
                merchantId, activeConversations, maxActiveConversations);

            if (activeConversations >= maxActiveConversations) {
                // 活跃会话已满，发送排队满提示，不创建会话
                sendQueueFullMessage(session, queueConfig.getQueueFullMessage());
                log.warn("活跃会话已满，拒绝新访客，不创建会话 - active: {}, max: {}",
                    activeConversations, maxActiveConversations);
                return false;
            }

            // 5. 活跃会话未满，可以创建会话
            log.info("活跃会话未满，允许创建会话 - active: {}, max: {}",
                activeConversations, maxActiveConversations);
            return true;

        } catch (Exception e) {
            log.error("排队配置检查失败 - merchantId: {}", merchantId, e);
            // 异常情况下，允许创建会话
            return true;
        }
    }

    /**
     * 尝试分配客服（优化版）
     * 确保分配成功时同步更新数据库中的adminId
     */
    private boolean tryAssignService(ImConversationVo conversation, Long merchantId, Long merchantChannelId, WebSocketSession session) {
        try {
            ImMerchantAdministratorVo assignedService = conversationService.autoAssignService(
                conversation.getConversationId(), merchantId, merchantChannelId);

            if (assignedService != null) {
                // 更新内存对象
                conversation.setAdminId(assignedService.getAdminId());
                conversation.setAdminName(assignedService.getNickname());

                // 同步更新数据库中的adminId
                boolean updated = conversationService.updateConversationAdmin(conversation.getConversationId(), assignedService.getAdminId());

                if (!updated) {
                    log.warn("更新会话客服ID失败，但分配仍然有效 - conversationId: {}, adminId: {}",
                        conversation.getConversationId(), assignedService.getAdminId());
                }

                // 发送分配成功通知
                try {
                    ConversationQueuesDto queueConfig = merchantService.queryConversationQueues(merchantId);
                    if (queueConfig != null && "1".equals(queueConfig.getQueueSuccessNotification())) {
                        sendQueueSuccessMessage(session, queueConfig.getQueueSuccessMessage());
                    }
                } catch (Exception e) {
                    log.warn("发送分配成功通知失败", e);
                }

                log.info("客服分配成功 - conversationId: {}, serviceId: {}, adminName: {}",
                    conversation.getConversationId(), assignedService.getAdminId(), assignedService.getNickname());
                return true;
            } else {
                log.info("暂无可用客服 - conversationId: {}", conversation.getConversationId());
                return false;
            }
        } catch (Exception e) {
            log.error("客服分配异常 - conversationId: {}", conversation.getConversationId(), e);
            return false;
        }
    }

    /**
     * 获取当前活跃会话数量（排队中 + 接待中）
     * 用于判断是否还能接受新的访客连接
     */
    private int getCurrentQueueSize(Long merchantId) {
        try {
            // 查询当前活跃会话数量（waiting + active）
            return conversationService.countWaitingConversations(merchantId);
        } catch (Exception e) {
            log.error("获取活跃会话数量失败 - merchantId: {}", merchantId, e);
            return 0;
        }
    }

    /**
     * 硬编码发送消息给客服
     */
    private void sendMessageToService(Long adminId, VisitorLoginUser visitorVo, String messageType, String content, Long conversationId) {
        try {
            // 构建发送给客服的消息
            JSONObject data = new JSONObject();
            data.put("type", MessageType.CHAT_MESSAGE);
            data.put("messageType", messageType);
            data.put("content", content);
            data.put("visitorId", visitorVo.getVisitorId());
            data.put("visitorName", visitorVo.getVisitorName());
            data.put("conversationId", conversationId);
            data.put("messageSendTime", System.currentTimeMillis());

            JSONObject response = new JSONObject();
            response.put("event", MessageEvent.SERVICE_EVENT.getCode());
            response.put("data", data);

            // 发送给指定客服
            String serviceSessionKey = "merchant:" + visitorVo.getMerchantId() + ":admin:" + adminId;
            WebSocketUtils.sendMessage(serviceSessionKey, response.toString());

            log.info("硬编码转发消息给客服 - adminId: {}, visitorId: {}", adminId, visitorVo.getVisitorId());
        } catch (Exception e) {
            log.error("硬编码转发消息给客服失败 - adminId: {}, visitorId: {}", adminId, visitorVo.getVisitorId(), e);
        }
    }

    /**
     * 发送排队等待消息
     */
    private void sendQueueWaitingMessage(WebSocketSession session, String message, int queuePosition) {
        try {
            if (message == null || message.trim().isEmpty()) {
                message = "您前面还有 ${queue} 位访客在排队，请稍候...";
            }

            String finalMessage = message.replace("${queue}", String.valueOf(queuePosition));

            JSONObject data = new JSONObject();
            data.put("type", MessageType.QUEUE_WAITING);
            data.put("content", finalMessage);
            data.put("queuePosition", queuePosition);
            data.put("messageSendTime", System.currentTimeMillis());

            JSONObject response = new JSONObject();
            response.put("event", MessageEvent.VISITOR_EVENT.getCode());
            response.put("data", data);

            session.sendMessage(new TextMessage(response.toString()));

        } catch (Exception e) {
            log.error("发送排队等待消息失败", e);
        }
    }

    /**
     * 发送排队已满消息
     */
    private void sendQueueFullMessage(WebSocketSession session, String message) {
        try {
            if (message == null || message.trim().isEmpty()) {
                message = "当前咨询人数较多，请稍后再试";
            }

            JSONObject data = new JSONObject();
            data.put("type", MessageType.QUEUE_FULL);
            data.put("content", message);
            data.put("messageSendTime", System.currentTimeMillis());

            JSONObject response = new JSONObject();
            response.put("event", MessageEvent.VISITOR_EVENT.getCode());
            response.put("data", data);

            session.sendMessage(new TextMessage(response.toString()));

        } catch (Exception e) {
            log.error("发送排队已满消息失败", e);
        }
    }

    /**
     * 发送排队成功消息
     */
    private void sendQueueSuccessMessage(WebSocketSession session, String message) {
        try {
            if (message == null || message.trim().isEmpty()) {
                message = "客服已为您接入，开始为您服务";
            }

            JSONObject data = new JSONObject();
            data.put("type", MessageType.QUEUE_SUCCESS);
            data.put("content", message);
            data.put("messageSendTime", System.currentTimeMillis());

            JSONObject response = new JSONObject();
            response.put("event", MessageEvent.VISITOR_EVENT.getCode());
            response.put("data", data);

            session.sendMessage(new TextMessage(response.toString()));

        } catch (Exception e) {
            log.error("发送排队成功消息失败", e);
        }
    }

}
