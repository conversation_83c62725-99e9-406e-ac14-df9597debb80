package org.dromara.im.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.im.constans.ConversationConstants;
import org.dromara.im.domain.ImConversation;
import org.dromara.im.domain.ImMerchantAdministrator;
import org.dromara.im.domain.ImMerchantChannel;
import org.dromara.im.domain.bo.ImConversationBo;
import org.dromara.im.domain.dto.AssignmentRulesDto;
import org.dromara.im.domain.vo.*;
import org.dromara.im.mapper.ImConversationMapper;
import org.dromara.im.mapper.ImMemberGroupMapper;
import org.dromara.im.mapper.ImMerchantAdministratorMapper;
import org.dromara.im.mapper.ImMerchantChannelMapper;
import org.dromara.im.service.IImAdminVisitorRelationService;
import org.dromara.im.service.IImConversationService;
import org.dromara.im.service.IImQueueService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会话关系Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ImConversationServiceImpl implements IImConversationService {

    private final ImConversationMapper baseMapper;

    private final ImMerchantAdministratorMapper merchantAdministratorMapper;

    private final ImMerchantChannelMapper merchantChannelMapper;

    private final IImAdminVisitorRelationService imAdminVisitorRelationService;

    private final IImQueueService queueService;

    private final ImMemberGroupMapper memberGroupMapper;

    /**
     * 查询会话关系
     *
     * @param id 主键
     * @return 会话关系
     */
    @Override
    public ImConversationVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public ImConversationVo queryByConversationId(Long conversationId) {
        LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
            .eq(ImConversation::getConversationId, conversationId);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public ImConversationVo queryActiveByVisitorAndBusiness(String visitorId, Long merchantId) {
        LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
            .eq(ImConversation::getVisitorId, visitorId)
            .eq(ImConversation::getMerchantId, merchantId)
            .in(ImConversation::getStatus, ConversationConstants.waiting, ConversationConstants.active)
            .orderByDesc(ImConversation::getCreateTime)
            .last("LIMIT 1");
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 分页查询会话关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 会话关系分页列表
     */
    @Override
    public TableDataInfo<ImConversationVo> queryPageList(ImConversationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImConversation> lqw = buildQueryWrapper(bo);
        Page<ImConversationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<ImConversationVo> records = result.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> adminIdList = records.stream()
                .map(ImConversationVo::getAdminId)
                .toList();
            if (CollectionUtils.isNotEmpty(adminIdList)) {
                List<ImMerchantAdministratorVo> adminVoList = merchantAdministratorMapper.selectVoByIds(adminIdList);
                if (CollectionUtils.isNotEmpty(adminVoList)) {
                    List<Long> memberGroupList = adminVoList.stream()
                        .map(ImMerchantAdministratorVo::getMemberGroupId)
                        .toList();
                    if (CollectionUtils.isNotEmpty(memberGroupList)) {
                        List<ImMemberGroupVo> imMemberGroupVos = memberGroupMapper.selectVoByIds(memberGroupList);
                        Map<Long, ImMemberGroupVo> memberGroupMap = imMemberGroupVos.stream()
                            .collect(Collectors.toMap(ImMemberGroupVo::getMemberGroupId, v -> v));
                        for (ImMerchantAdministratorVo vo : adminVoList) {
                            ImMemberGroupVo imMemberGroupVo = memberGroupMap.get(vo.getMemberGroupId());
                            if (Objects.nonNull(imMemberGroupVo)) {
                                vo.setMemberGroupName(imMemberGroupVo.getGroupName());
                            }
                        }
                    }
                    Map<Long, ImMerchantAdministratorVo> collect = adminVoList.stream()
                        .collect(Collectors.toMap(ImMerchantAdministratorVo::getAdminId, v -> v));

                    records.forEach(record -> {
                        ImMerchantAdministratorVo imMerchantAdministratorVo = collect.get(record.getAdminId());
                        record.setMemberGroupName(imMerchantAdministratorVo.getMemberGroupName());
                    });
                }
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的会话关系列表
     *
     * @param bo 查询条件
     * @return 会话关系列表
     */
    @Override
    public List<ImConversationVo> queryList(ImConversationBo bo) {
        LambdaQueryWrapper<ImConversation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImConversation> buildQueryWrapper(ImConversationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImConversation> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ImConversation::getConversationId);
        lqw.eq(Objects.nonNull(bo.getConversationId()), ImConversation::getConversationId, bo.getConversationId());
        lqw.eq(StringUtils.isNotBlank(bo.getVisitorId()), ImConversation::getVisitorId, bo.getVisitorId());
        lqw.eq(bo.getAdminId() != null, ImConversation::getAdminId, bo.getAdminId());
        lqw.eq(bo.getMerchantId() != null, ImConversation::getMerchantId, bo.getMerchantId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ImConversation::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getVisitorName()), ImConversation::getVisitorName, bo.getVisitorName());
        lqw.like(StringUtils.isNotBlank(bo.getAdminName()), ImConversation::getAdminName, bo.getAdminName());
        lqw.eq(bo.getAutoAssigned() != null, ImConversation::getAutoAssigned, bo.getAutoAssigned());
        lqw.eq(StringUtils.isNotBlank(bo.getClosedReason()), ImConversation::getClosedReason, bo.getClosedReason());
        lqw.eq(bo.getRating() != null, ImConversation::getRating, bo.getRating());
        lqw.eq(StringUtils.isNotBlank(bo.getFeedback()), ImConversation::getFeedback, bo.getFeedback());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), ImConversation::getTenantId, bo.getTenantId());
        lqw.ge(Objects.nonNull(bo.getUnreadCount()), ImConversation::getUnreadCount, bo.getUnreadCount());
        return lqw;
    }

    /**
     * 新增会话关系
     *
     * @param bo 会话关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImConversationBo bo) {
        ImConversation add = MapstructUtils.convert(bo, ImConversation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setConversationId(add.getConversationId());
        }
        return flag;
    }

    /**
     * 修改会话关系
     *
     * @param bo 会话关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImConversationBo bo) {
        ImConversation update = MapstructUtils.convert(bo, ImConversation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImConversation entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除会话关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean assignServiceToConversation(Long conversationId, Long adminId, String nickname) {
        try {
            ImConversationVo conversation = queryByConversationId(conversationId);
            if (conversation == null) {
                log.info("会话不存在: {}", conversationId);
                return false;
            }

            // 如果已经分配了客服，先检查是否需要更新
            if (conversation.getAdminId() != null && conversation.getAdminId().equals(adminId)) {
                log.info("会话{}已分配给客服{}", conversationId, adminId);
                return true;
            }

            // 更新会话分配信息
            ImConversationBo updateBo = new ImConversationBo();
            updateBo.setConversationId(conversation.getConversationId());
            updateBo.setAdminId(adminId);
            updateBo.setStatus(ConversationConstants.active); // 更新为活跃状态
            updateBo.setAdminName(nickname);
            boolean success = updateByBo(updateBo);
            if (success) {
                log.info("成功分配客服 - conversationId: {}, adminId: {}", conversationId, adminId);
            }
            return success;
        } catch (Exception e) {
            log.error("分配客服异常 - conversationId: {}, adminId: {}", conversationId, adminId, e);
            return false;
        }
    }

    @Override
    public ImMerchantAdministratorVo autoAssignService(Long conversationId, Long merchantId, Long merchantChannelId, Long adminId) {
        try {
            // 获取会话信息
            ImConversationVo conversation = queryByConversationId(conversationId);
            if (conversation == null) {
                log.info("会话不存在 - conversationId: {}", conversationId);
                return null;
            }

            // 获取商户渠道的分配规则
            ImMerchantChannelVo merchantChannelVo;
            if (Optional.ofNullable(merchantChannelId).isPresent()) {
                merchantChannelVo = merchantChannelMapper.selectVoById(merchantChannelId);
            } else {
                merchantChannelVo = merchantChannelMapper.selectVoOne(new LambdaQueryWrapper<>(ImMerchantChannel.class)
                    .eq(ImMerchantChannel::getMerchantId, merchantId)
                    .eq(ImMerchantChannel::getDefaultChannel, "1")
                    .last("limit 1")
                );
            }
            if (merchantChannelVo == null) {
                log.info("商户渠道不存在 - merchantChannelId: {}", merchantChannelId);
                return null;
            }

            // 构建分配规则DTO
            merchantChannelVo.buildAssignmentRulesDto();
            AssignmentRulesDto assignmentRules = merchantChannelVo.getAssignmentRulesDto();
            if (assignmentRules == null) {
                // 使用默认分配规则
                assignmentRules = AssignmentRulesDto.getDefault();
                log.info("使用默认分配规则 - conversationId: {}", conversationId);
            }

            // 获取可用客服列表
            List<ImMerchantAdministratorVo> availableServices = getAvailableServices(merchantId, adminId);
            if (availableServices.isEmpty()) {
                log.info("没有可用客服 - merchantId: {}", merchantId);
                return null;
            }

            // 根据分配规则选择客服
            ImMerchantAdministratorVo selectedService = selectServiceByAssignmentRules(
                conversation.getVisitorId(), merchantId, availableServices, assignmentRules);

            if (selectedService != null) {
                // 分配客服到会话
                boolean success = assignServiceToConversation(conversationId, selectedService.getAdminId(), selectedService.getNickname());
                if (success) {
                    log.info("自动分配客服成功 - conversationId: {}, serviceId: {}, assignmentType: {}",
                        conversationId, selectedService.getAdminId(), assignmentRules.getAssignmentType());
                    return selectedService;
                } else {
                    log.error("分配客服失败 - conversationId: {}, serviceId: {}",
                        conversationId, selectedService.getAdminId());
                }
            }

            log.info("自动分配客服失败 - conversationId: {}, merchantId: {}", conversationId, merchantId);
            return null;
        } catch (Exception e) {
            log.error("自动分配客服异常 - conversationId: {}, merchantId: {}", conversationId, merchantId, e);
            return null;
        }
    }

    @Override
    public Boolean transferConversation(Long conversationId, Long fromServiceId, Long toServiceId, String transferReason) {
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate(ImConversation.class)
                .set(ImConversation::getAdminId, toServiceId)
                .set(ImConversation::getStatus, ConversationConstants.transferred)
                .set(ImConversation::getUpdateTime, new Date())
                .eq(ImConversation::getConversationId, conversationId)
                .eq(ImConversation::getAdminId, fromServiceId); // 确保只有原客服能转接

            int updated = baseMapper.update(null, updateWrapper);
            boolean success = updated > 0;

            if (success) {
                log.info("转接会话 - conversationId: {}, from: {}, to: {}, reason: {}",
                    conversationId, fromServiceId, toServiceId, transferReason);
            }
            return success;
        } catch (Exception e) {
            log.error("转接会话异常 - conversationId: {}, from: {}, to: {}",
                conversationId, fromServiceId, toServiceId, e);
            return false;
        }
    }

    @Override
    public void updateLastMessage(Long conversationId, String lastMessageContent) {
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate(ImConversation.class)
                .eq(ImConversation::getConversationId, conversationId)
                .set(ImConversation::getLastMessageContent, lastMessageContent)
                .set(ImConversation::getLastMessageTime, System.currentTimeMillis());
            int updated = baseMapper.update(null, updateWrapper);
            boolean success = updated > 0;

            if (success) {
                log.info("更新会话最后消息 - conversationId: {}", conversationId);
            }
        } catch (Exception e) {
            log.error("更新会话最后消息异常 - conversationId: {}", conversationId, e);
        }
    }

    @Override
    public void incrementMessageCount(Long conversationId) {
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate(ImConversation.class)
                .setSql("message_count = message_count + 1")
                .setSql("unread_count = unread_count + 1")

                .set(ImConversation::getUpdateTime, new Date())
                .eq(ImConversation::getConversationId, conversationId);

            int updated = baseMapper.update(null, updateWrapper);
            boolean success = updated > 0;

            if (success) {
                log.info("增加会话消息计数 - conversationId: {}", conversationId);
            }
        } catch (Exception e) {
            log.error("增加会话消息计数异常 - conversationId: {}", conversationId, e);
        }
    }


    @Override
    public ImConversationVo getConversationId(String visitorId, Long serviceId, Long merchantId) {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getVisitorId, visitorId)
                .eq(ImConversation::getMerchantId, merchantId);

            if (serviceId != null) {
                wrapper.eq(ImConversation::getAdminId, serviceId);
            }

            wrapper.eq(ImConversation::getStatus, ConversationConstants.active)
                .orderByDesc(ImConversation::getCreateTime)
                .last("LIMIT 1");

            ImConversationVo conversation = baseMapper.selectVoOne(wrapper);
            return conversation;
        } catch (Exception e) {
            log.error("获取会话ID异常 - visitorId: {}, serviceId: {}, businessId: {}",
                visitorId, serviceId, merchantId, e);
            return null;
        }
    }


    /**
     * 获取可用客服列表
     */
    private List<ImMerchantAdministratorVo> getAvailableServices(Long merchantId, Long adminId) {
        try {
            LambdaQueryWrapper<ImMerchantAdministrator> wrapper = Wrappers.lambdaQuery(ImMerchantAdministrator.class)
                .eq(ImMerchantAdministrator::getMerchantId, merchantId)
                .eq(ImMerchantAdministrator::getStatus, "1") // 启用状态
                .ne(Objects.nonNull(adminId), ImMerchantAdministrator::getAdminId, adminId)
                .orderByAsc(ImMerchantAdministrator::getOnlineStatus); // 按排序字段升序

            return merchantAdministratorMapper.selectVoList(wrapper);
        } catch (Exception e) {
            log.error("获取可用客服列表异常 - merchantId: {}", merchantId, e);
            return List.of();
        }
    }

    /**
     * 根据分配规则选择客服
     */
    private ImMerchantAdministratorVo selectServiceByAssignmentRules(String visitorId, Long merchantId,
                                                                     List<ImMerchantAdministratorVo> availableServices, AssignmentRulesDto assignmentRules) {

        // 处理回头客分配
        if ("1".equals(assignmentRules.getReturningCustomerAssignment())) {
            ImMerchantAdministratorVo returningCustomerService = handleReturningCustomer(visitorId, merchantId, availableServices, assignmentRules);
            if (returningCustomerService != null) {
                return returningCustomerService;
            }
        }

        // 根据分配方式选择客服
        String assignmentType = assignmentRules.getAssignmentType();
        return switch (assignmentType) {
            case "1" -> // 接待量平均分配
                selectByAverageLoad(availableServices, merchantId);
            case "2" -> // 按顺序依次分配
                selectBySequence(availableServices, merchantId);
            case "3" -> // 按优先顺序分配
                selectByPriority(availableServices);
            case "4" -> // 按随机分配
                selectByRandom(availableServices);
            default -> {
                log.info("未知的分配方式: {}, 使用默认平均分配", assignmentType);
                yield selectByAverageLoad(availableServices, merchantId);
            }
        };
    }

    /**
     * 处理回头客分配
     */
    private ImMerchantAdministratorVo handleReturningCustomer(String visitorId, Long merchantId,
                                                              List<ImMerchantAdministratorVo> availableServices, AssignmentRulesDto assignmentRules) {

        // 查询访客的历史客服
        Long lastServiceId = getVisitorLastServiceId(visitorId, merchantId);
        if (lastServiceId == null) {
            log.info("访客{}没有历史客服记录", visitorId);
            return null;
        }

        // 查找历史客服是否在可用列表中
        Optional<ImMerchantAdministratorVo> lastService = availableServices.stream()
            .filter(service -> service.getAdminId().equals(lastServiceId))
            .findFirst();

        if (lastService.isPresent()) {
            log.info("回头客分配给历史客服 - visitorId: {}, serviceId: {}", visitorId, lastServiceId);
            return lastService.get();
        } else {
            // 历史客服不可用，根据策略处理
            if ("0".equals(assignmentRules.getReturningCustomerStrategy())) {
                // 给客服留言 - 这里可以实现留言逻辑
                log.info("历史客服不可用，为访客{}创建留言给客服{}", visitorId, lastServiceId);
                // TODO: 实现留言功能
                return null;
            } else {
                // 分配给其他客服 - 继续正常分配流程
                log.info("历史客服不可用，为访客{}分配其他客服", visitorId);
                return null;
            }
        }
    }

    /**
     * 获取访客的最后服务客服ID
     */
    private Long getVisitorLastServiceId(String visitorId, Long merchantId) {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getVisitorId, visitorId)
                .eq(ImConversation::getMerchantId, merchantId)
                .isNotNull(ImConversation::getAdminId)
                .orderByDesc(ImConversation::getCreateTime)
                .last("LIMIT 1");

            ImConversationVo lastConversation = baseMapper.selectVoOne(wrapper);
            return lastConversation != null ? lastConversation.getAdminId() : null;
        } catch (Exception e) {
            log.error("获取访客历史客服异常 - visitorId: {}, merchantId: {}", visitorId, merchantId, e);
            return null;
        }
    }

    /**
     * 接待量平均分配 - 选择当前接待访客数量最少的客服
     */
    private ImMerchantAdministratorVo selectByAverageLoad(List<ImMerchantAdministratorVo> availableServices, Long merchantId) {
        return availableServices.stream()
            .min(Comparator.comparingInt(service -> getCurrentVisitorCount(service.getAdminId(), merchantId)))
            .orElse(null);
    }

    /**
     * 按顺序依次分配 - 轮询分配
     */
    private ImMerchantAdministratorVo selectBySequence(List<ImMerchantAdministratorVo> availableServices, Long merchantId) {
        if (availableServices.isEmpty()) {
            return null;
        }

        // 使用Redis存储轮询索引
        String key = "service:round_robin:" + merchantId;
        Integer currentIndex = RedisUtils.getCacheObject(key);
        if (currentIndex == null) {
            currentIndex = 0;
        }

        int nextIndex = (currentIndex + 1) % availableServices.size();
        RedisUtils.setCacheObject(key, nextIndex, Duration.ofHours(24));

        return availableServices.get(currentIndex);
    }

    /**
     * 按优先顺序分配 - 根据客服的排序字段选择优先级最高的
     */
    private ImMerchantAdministratorVo selectByPriority(List<ImMerchantAdministratorVo> availableServices) {
        // 客服列表已经按sort字段排序，直接返回第一个
        return availableServices.isEmpty() ? null : availableServices.get(0);
    }

    /**
     * 按随机分配
     */
    private ImMerchantAdministratorVo selectByRandom(List<ImMerchantAdministratorVo> availableServices) {
        if (availableServices.isEmpty()) {
            return null;
        }

        Random random = new Random();
        int randomIndex = random.nextInt(availableServices.size());
        return availableServices.get(randomIndex);
    }

    /**
     * 获取客服当前接待的访客数量
     */
    private int getCurrentVisitorCount(Long adminId, Long merchantId) {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getAdminId, adminId)
                .eq(ImConversation::getMerchantId, merchantId)
                .in(ImConversation::getStatus, ConversationConstants.waiting, ConversationConstants.active);

            Long count = baseMapper.selectCount(wrapper);
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("获取客服接待数量异常 - adminId: {}, merchantId: {}", adminId, merchantId, e);
            return 0;
        }
    }

    @Override
    public ImConversationVo queryLatestByVisitorAndAdmin(String visitorId, Long adminId, Long merchantId) {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getVisitorId, visitorId)
                .eq(ImConversation::getAdminId, adminId)
                .eq(ImConversation::getMerchantId, merchantId)
                .orderByDesc(ImConversation::getCreateTime)
                .last("LIMIT 1");

            ImConversationVo latestConversation = baseMapper.selectVoOne(wrapper);

            if (latestConversation != null) {
                log.info("查询最新会话成功 - visitorId: {}, adminId: {}, conversationId: {}",
                    visitorId, adminId, latestConversation.getConversationId());
            } else {
                log.info("未找到会话记录 - visitorId: {}, adminId: {}", visitorId, adminId);
            }

            return latestConversation;
        } catch (Exception e) {
            log.error("查询最新会话异常 - visitorId: {}, adminId: {}, merchantId: {}",
                visitorId, adminId, merchantId, e);
            return null;
        }
    }

    @Override
    public List<ImConversationVo> queryLatestByVisitorIdsAndAdmin(List<String> visitorIds, Long adminId, Long merchantId) {
        try {
            if (visitorIds == null || visitorIds.isEmpty()) {
                return new ArrayList<>();
            }

            // 使用子查询找到每个访客的最新会话
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .in(ImConversation::getVisitorId, visitorIds)
                .eq(ImConversation::getAdminId, adminId)
                .eq(ImConversation::getMerchantId, merchantId)
                .orderByDesc(ImConversation::getCreateTime);

            List<ImConversationVo> allConversations = baseMapper.selectVoList(wrapper);

            // 按访客ID分组，每组取最新的一条
            Map<String, ImConversationVo> latestConversationMap = allConversations.stream()
                .collect(Collectors.toMap(
                    ImConversationVo::getVisitorId,
                    conversation -> conversation,
                    (existing, replacement) -> {
                        // 如果有重复，保留创建时间更晚的
                        return existing.getCreateTime().after(replacement.getCreateTime()) ? existing : replacement;
                    }
                ));

            List<ImConversationVo> result = new ArrayList<>(latestConversationMap.values());

            log.info("批量查询最新会话成功 - 查询访客数: {}, 返回会话数: {}", visitorIds.size(), result.size());
            return result;

        } catch (Exception e) {
            log.error("批量查询最新会话异常 - visitorIds: {}, adminId: {}, merchantId: {}",
                visitorIds, adminId, merchantId, e);
            return new ArrayList<>();
        }
    }


    @Override
    public Boolean closeConversationAndAdminVisitorRelationAndProcessQueue(String visitorId, Long adminId, Long merchantId) {
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate(ImConversation.class)
                .set(ImConversation::getStatus, ConversationConstants.closed)
                .set(ImConversation::getUpdateTime, new Date())
                .eq(ImConversation::getVisitorId, visitorId)
                .eq(ImConversation::getAdminId, adminId)
                .eq(ImConversation::getMerchantId, merchantId)
                .in(ImConversation::getStatus, ConversationConstants.waiting, ConversationConstants.active); // 只关闭等待中或进行中的会话

            baseMapper.update(null, updateWrapper);
            imAdminVisitorRelationService.updateEndService(merchantId, visitorId, adminId);
            log.info("结束会话成功 - visitorId: {}, adminId: {}", visitorId, adminId);
            queueService.processQueueOnServiceEnd(merchantId, adminId);
            return true;

        } catch (Exception e) {
            log.error("结束会话失败 - visitorId: {}, adminId: {}", visitorId, adminId, e);
            return false;
        }
    }


    /**
     * 更新会话评价（Long版本）
     */
    public Boolean updateConversationRating(ImConversationVo conversationVo, Long rating, String feedback) {
        Long conversationId = conversationVo.getConversationId();
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate(ImConversation.class)
                .set(ImConversation::getRating, rating)
                .set(ImConversation::getFeedback, feedback)
                .set(ImConversation::getEvaluateTime, System.currentTimeMillis())
                .eq(ImConversation::getConversationId, conversationId);

            int updated = baseMapper.update(null, updateWrapper);

            closeConversationAndAdminVisitorRelationAndProcessQueue(conversationVo.getVisitorId(), conversationVo.getAdminId(), conversationVo.getMerchantId());

            if (updated > 0) {
                log.info("更新会话评价成功 - conversationId: {}, rating: {}", conversationId, rating);
                return true;
            } else {
                log.info("未找到会话记录 - conversationId: {}", conversationId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新会话评价失败 - conversationId: {}, rating: {}", conversationId, rating, e);
            return false;
        }
    }

    @Override
    public void clearUnreadCount(Long adminId, String visitorId) {
        baseMapper.update(new ImConversation(), Wrappers.<ImConversation>lambdaUpdate()
            .set(ImConversation::getUnreadCount, 0L)
            .eq(ImConversation::getAdminId, adminId)
            .eq(ImConversation::getVisitorId, visitorId)
            .eq(ImConversation::getStatus, ConversationConstants.active)
        );

    }

    /**
     * 统计活跃会话数量（排队中 + 接待中）
     * 用于排队逻辑判断，统计所有占用客服资源的会话
     */
    public int countWaitingConversations(Long merchantId) {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getMerchantId, merchantId)
                .in(ImConversation::getStatus, ConversationConstants.waiting, ConversationConstants.active)  // 排队中 + 接待中
                ;

            Long count = baseMapper.selectCount(wrapper);
            int result = count != null ? count.intValue() : 0;

            log.info("统计活跃会话数量 - merchantId: {}, count: {} (waiting + active)", merchantId, result);
            return result;
        } catch (Exception e) {
            log.error("统计活跃会话数量失败 - merchantId: {}", merchantId, e);
            return 0;
        }
    }

    /**
     * 获取所有有排队访客的商户ID列表
     */
    public List<Long> getMerchantsWithWaitingConversations() {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getStatus, ConversationConstants.waiting)
                .groupBy(ImConversation::getMerchantId);

            List<ImConversation> conversations = baseMapper.selectList(wrapper);

            return conversations.stream()
                .map(ImConversation::getMerchantId)
                .distinct()
                .collect(java.util.stream.Collectors.toList());

        } catch (Exception e) {
            log.error("获取有排队访客的商户ID列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public ImConversationVo findQueuedConversation(String visitorId, Long merchantId) {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getMerchantId, merchantId)
                .eq(ImConversation::getVisitorId, visitorId)
                .eq(ImConversation::getStatus, ConversationConstants.waiting)  // 只查找等待状态的会话
                .orderByDesc(ImConversation::getCreateTime)
                .last("LIMIT 1");

            ImConversation conversation = baseMapper.selectOne(wrapper);
            if (conversation != null) {
                log.info("找到排队会话 - conversationId: {}, visitorId: {}", conversation.getConversationId(), visitorId);
                return BeanUtil.toBean(conversation, ImConversationVo.class);
            }
            return null;
        } catch (Exception e) {
            log.error("查找排队会话失败 - visitorId: {}, merchantId: {}", visitorId, merchantId, e);
            return null;
        }
    }

    @Override
    public int getQueuePosition(String visitorId, Long merchantId) {
        try {
            // 查找访客的会话
            ImConversationVo visitorConversation = findQueuedConversation(visitorId, merchantId);
            if (visitorConversation == null) {
                return 0;
            }

            // 统计在该访客之前创建的等待会话数量
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getMerchantId, merchantId)
                .eq(ImConversation::getStatus, ConversationConstants.waiting)
                .lt(ImConversation::getCreateTime, visitorConversation.getCreateTime());

            Long count = baseMapper.selectCount(wrapper);
            int position = count != null ? count.intValue() + 1 : 1;

            log.info("计算排队位置 - visitorId: {}, position: {}", visitorId, position);
            return position;
        } catch (Exception e) {
            log.error("计算排队位置失败 - visitorId: {}, merchantId: {}", visitorId, merchantId, e);
            return 0;
        }
    }

    @Override
    public ImConversationVo createConversationInWaitingState(ImVisitorVo visitorVo) {
        String visitorId = visitorVo.getVisitorId();
        Long merchantId = visitorVo.getMerchantId();
        try {
            ImConversation conversation = new ImConversation();
            conversation.setVisitorId(visitorId);
            conversation.setMerchantId(merchantId);
            conversation.setVisitorName(visitorVo.getVisitorName());
            conversation.setVisitorAvatar(visitorVo.getAvatarUrl());
            conversation.setMerchantChannelId(visitorVo.getMerchantChannelId());
            conversation.setStatus(ConversationConstants.waiting);  // 初始状态为等待
            conversation.setUnreadCount(0L);
            conversation.setMessageCount(0L);

            boolean success = baseMapper.insert(conversation) > 0;
            if (success) {
                log.info("创建等待状态会话成功 - conversationId: {}, visitorId: {}", conversation.getConversationId(), visitorId);
                return BeanUtil.toBean(conversation, ImConversationVo.class);
            }
            return null;
        } catch (Exception e) {
            log.error("创建等待状态会话失败 - visitorId: {}, merchantId: {}", visitorId, merchantId, e);
            return null;
        }
    }

    @Override
    public void updateConversationStatus(Long conversationId, String status) {
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(ImConversation::getStatus, status)
                .eq(ImConversation::getConversationId, conversationId);

            int updated = baseMapper.update(null, updateWrapper);
            if (updated > 0) {
                log.info("更新会话状态成功 - conversationId: {}, status: {}", conversationId, status);
            } else {
                log.info("更新会话状态失败，会话不存在 - conversationId: {}", conversationId);
            }
        } catch (Exception e) {
            log.error("更新会话状态失败 - conversationId: {}, status: {}", conversationId, status, e);
        }
    }

    @Override
    public boolean updateConversationAdmin(Long conversationId, ImMerchantAdministratorVo assignedService) {
        Long adminId = assignedService.getAdminId();
        try {
            LambdaUpdateWrapper<ImConversation> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper
                .set(ImConversation::getAdminId, adminId)
                .set(ImConversation::getAdminAvatar, assignedService.getAvatar())
                .set(ImConversation::getAdminName, assignedService.getNickname())
                .set(ImConversation::getUpdateTime, LocalDateTime.now())
                .eq(ImConversation::getConversationId, conversationId);

            int updated = baseMapper.update(null, updateWrapper);
            if (updated > 0) {
                log.info("更新会话客服成功 - conversationId: {}, adminId: {}", conversationId, adminId);
                return true;
            } else {
                log.info("更新会话客服失败，会话不存在 - conversationId: {}", conversationId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新会话客服失败 - conversationId: {}, adminId: {}", conversationId, adminId, e);
            return false;
        }
    }

    @Override
    public List<ImConversationVo> findAllActiveConversations() {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getStatus, "active")
                .orderByDesc(ImConversation::getUpdateTime);

            List<ImConversation> conversations = baseMapper.selectList(wrapper);
            return conversations.stream()
                .map(conversation -> BeanUtil.toBean(conversation, ImConversationVo.class))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查找所有活跃会话失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ImConversationVo> findWaitingConversations() {
        try {
            LambdaQueryWrapper<ImConversation> wrapper = Wrappers.lambdaQuery(ImConversation.class)
                .eq(ImConversation::getStatus, "waiting")
                .orderByAsc(ImConversation::getCreateTime); // 按创建时间升序，先创建的先处理

            List<ImConversation> conversations = baseMapper.selectList(wrapper);
            return conversations.stream()
                .map(conversation -> BeanUtil.toBean(conversation, ImConversationVo.class))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查找所有等待会话失败", e);
            return Collections.emptyList();
        }
    }
}
