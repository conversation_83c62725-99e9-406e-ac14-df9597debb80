package org.dromara.common.core.domain.model;

import lombok.Data;

@Data
public class VisitorLoginUser extends LoginUser {

    /**
     * 游客唯一标识
     */
    private String visitorId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户渠道ID
     */
    private Long merchantChannelId;

    /**
     * 游客显示名称
     */
    private String visitorName;

    /**
     * 游客真实姓名
     */
    private String realName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 访问渠道标识
     */
    private String channel;

    /**
     * 访客IP地址(支持IPv6)
     */
    private String ipAddress;

    /**
     * IP地理位置信息
     */
    private String locationInfo;

    /**
     * 来源页面URL
     */
    private String fromUrl;

    /**
     * 浏览器信息
     */
    private String userAgent;

    /**
     * 设备信息JSON格式
     */
    private String deviceInfo;

    /**
     * 浏览器详细信息JSON格式
     */
    private String browserInfo;

    /**
     * 客户端语言
     */
    private String language;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 状态: 0=离线, 1=在线
     */
    private Integer status;

    /**
     * 是否被拉黑: 0=正常, 1=已拉黑
     */
    private String isBlocked;

    /**
     * 是否置顶: 0=否, 1=是
     */
    private String isTop;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * Cookie标识
     */
    private String cookieId;

    /**
     * 浏览器指纹
     */
    private String fingerprint;

    /**
     * UTM来源
     */
    private String utmSource;

    /**
     * UTM媒介
     */
    private String utmMedium;

    /**
     * UTM广告系列
     */
    private String utmCampaign;

    /**
     * UTM内容
     */
    private String utmContent;

    /**
     * UTM关键词
     */
    private String utmTerm;

    /**
     * 自定义字段JSON格式
     */
    private String customFields;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;


}
