## 使用声明

1. 该仓库是 [im-server](https://gitee.com/dromara/im-server) 的无租户分支（删除了租户相关代码，非关闭租户形式）
2. 原项目支持关闭多租户，如有多租户的需求，请使用原项目 [im-server](https://gitee.com/dromara/im-server)
3. 不保证稳定性，生产使用请谨慎
4. 定期同步原项目Bug修复和新功能
5. 本项目不接受PR，PR请到原项目提 [im-server](https://gitee.com/dromara/im-server)
6. 本项目**不维护**原项目的`版本SQL变更`,仅**维护**原项目`主SQL`文件。版本升级请留意原项目`版本SQL变更`
   记录。[点击查看](https://gitee.com/dromara/im-server/tree/5.X/script/sql/update)

## 分支说明

- `5.X`分支

> 该分支对应 [im-server 5.X](https://gitee.com/dromara/im-server/tree/5.X/) 分支，**移除**了`多租户`相关代码

- `dev`分支

> 该分支对应 [im-server dev](https://gitee.com/dromara/im-server/tree/dev/) 分支，**移除**了`多租户`相关代码

- `unworkflow`分支

> 该分支对应 [im-server dev](https://gitee.com/dromara/im-server/tree/dev/) 分支，**移除**了`多租户`和`工作流`相关代码

## 部署使用文档

> 部署使用流程与原项目一致，具体可查看 [im-server](https://gitee.com/dromara/im-server)
> 和 [plus-doc](https://plus-doc.dromara.org)