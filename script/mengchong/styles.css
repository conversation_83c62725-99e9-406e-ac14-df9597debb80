* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 80px;
}

.nav-logo h2 {
    color: #ff6b6b;
    font-weight: 700;
    font-size: 24px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    font-size: 14px;
    padding: 10px 0;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #ff6b6b;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 20px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-content a {
    display: block;
    padding: 10px 20px;
    color: #666;
    font-size: 14px;
}

.dropdown-content a:hover {
    background: #f8f9fa;
    color: #ff6b6b;
}

.nav-actions {
    display: flex;
    gap: 15px;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #333;
    border: 2px solid #e9ecef;
    padding: 10px 22px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.btn-large {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
    border: none;
    padding: 18px 36px;
    border-radius: 30px;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.btn-outline {
    background: transparent;
    color: #333;
    border: 2px solid #333;
    padding: 16px 34px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #333;
    color: white;
}

.btn-link {
    background: none;
    border: none;
    color: #ff6b6b;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    text-decoration: underline;
    transition: opacity 0.3s ease;
}

.btn-link:hover {
    opacity: 0.8;
}

/* 主要内容区域 */
main {
    margin-top: 80px;
}

/* 英雄区域 */
.hero {
    display: flex;
    align-items: center;
    min-height: 600px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    gap: 60px;
}

.hero-content {
    flex: 1;
}

.hero-title {
    font-size: 64px;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #333, #666);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: #666;
    margin-bottom: 40px;
    font-weight: 400;
}

.hero-actions {
    display: flex;
    gap: 20px;
}

.hero-image {
    flex: 1;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
}

/* 特色宠物展示 */
.featured-pets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    max-width: 1200px;
    margin: 100px auto;
    padding: 0 20px;
}

.pet-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.pet-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.pet-card.special {
    position: relative;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
}

.pet-card.special .pet-info {
    color: white;
}

.pet-card.special .special-label {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 12px;
    display: inline-block;
    margin-bottom: 15px;
}

.pet-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.pet-info {
    padding: 30px;
}

.pet-info h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
}

.pet-info p {
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
}

.pet-card.special .pet-info p {
    color: rgba(255, 255, 255, 0.9);
}

.pet-info button {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* 情感连接区域 */
.emotion-section {
    background: #f8f9fa;
    padding: 100px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.emotion-content {
    flex: 1;
    padding: 0 20px;
}

.emotion-content h2 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 30px;
    color: #333;
}

.emotion-video {
    flex: 1;
    padding: 0 20px;
}

.emotion-video img {
    width: 100%;
    height: auto;
    border-radius: 20px;
}

/* 核心服务 */
.core-services {
    padding: 100px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
    padding: 0 20px;
}

.section-header h2 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #333;
}

.section-header p {
    font-size: 18px;
    color: #666;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    padding: 0 20px;
}

.service-card {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.1);
}

.service-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 20px;
}

.service-card h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

/* 萌宠中心 */
.pet-center {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
    padding: 100px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 30px;
}

.center-content {
    flex: 1;
    padding: 0 40px;
}

.center-content h2 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
}

.center-content p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.center-image {
    flex: 1;
    padding: 0 40px;
}

.center-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
}

/* App下载区域 */
.app-section {
    background: #1a1a1a;
    color: white;
    padding: 100px 0;
}

.app-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    margin-bottom: 60px;
}

.app-info h3 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
}

.app-info p {
    font-size: 18px;
    opacity: 0.8;
}

.app-download img {
    width: 300px;
    height: auto;
}

.download-section {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.qr-code {
    text-align: center;
}

.qr-code img {
    width: 150px;
    height: 150px;
    margin-bottom: 15px;
}

.company-info p {
    margin-bottom: 10px;
    opacity: 0.8;
}

.links a {
    color: white;
    text-decoration: none;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.links a:hover {
    opacity: 1;
}

/* 页脚 */
.footer {
    background: #f8f9fa;
    padding: 60px 0 30px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
    justify-content: center;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ff6b6b;
}

.footer-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
}

.copyright p {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.legal-info {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

.legal-info a {
    color: #999;
    text-decoration: none;
}

.social-media {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    justify-content: center;
    max-width: 600px;
}

.social-item {
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    display: block;
}

.social-item:hover {
    text-decoration: none;
    color: #ff6b6b;
}

.social-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 auto 10px;
}

/* 各平台特色颜色 */
.social-icon.wechat {
    background: linear-gradient(135deg, #07C160, #00D100);
    color: white;
}

.social-icon.wechat-group {
    background: linear-gradient(135deg, #07C160, #00D100);
    color: white;
}

.social-icon.weibo {
    background: linear-gradient(135deg, #E6162D, #FF8200);
    color: white;
}

.social-icon.douyin {
    background: linear-gradient(135deg, #FE2C55, #25F4EE);
    color: white;
}

.social-icon.video {
    background: linear-gradient(135deg, #576B95, #9BB7E7);
    color: white;
}

.social-icon.xiaohongshu {
    background: linear-gradient(135deg, #FF2442, #FF6B6B);
    color: white;
}

.social-icon:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.social-item p {
    font-size: 12px;
    color: #666;
}

.contact p {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        gap: 40px;
    }

    .hero-title {
        font-size: 42px;
    }

    .featured-pets {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .emotion-section {
        flex-direction: column;
        gap: 40px;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .pet-center {
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }

    .app-content {
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }

    .download-section {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .social-media {
        justify-content: center;
        gap: 20px;
    }

    .social-item {
        flex: 0 0 auto;
        min-width: 80px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content,
.pet-card,
.service-card {
    animation: fadeInUp 0.8s ease-out;
}

/* 滚动平滑效果 */
html {
    scroll-behavior: smooth;
}
