// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function () {
    // 导航栏滚动效果
    const navbar = document.querySelector('.navbar');

    window.addEventListener('scroll', function () {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });

    // 平滑滚动到锚点
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 卡片悬停效果增强
    const petCards = document.querySelectorAll('.pet-card');
    petCards.forEach(card => {
        card.addEventListener('mouseenter', function () {
            this.style.transform = 'translateY(-15px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function () {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 服务卡片动画
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function () {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 20px 50px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function () {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // 按钮点击效果
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', function (e) {
            // 创建涟漪效果
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            // 移除涟漪效果
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // 滚动显示动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll('.pet-card, .service-card, .emotion-section, .pet-center');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        observer.observe(el);
    });

    // 模拟按钮功能（可以根据实际需求修改）
    const bookingButtons = document.querySelectorAll('.btn-primary');
    bookingButtons.forEach(button => {
        if (button.textContent.includes('预约')) {
            button.addEventListener('click', function () {
                alert('预约功能开发中，敬请期待！');
            });
        }
    });

    const detailButtons = document.querySelectorAll('.btn-secondary');
    detailButtons.forEach(button => {
        if (button.textContent.includes('了解详情')) {
            button.addEventListener('click', function () {
                alert('详情页面开发中，敬请期待！');
            });
        }
    });

    // 英雄区域文字打字机效果
    const heroTitle = document.querySelector('.hero-title');
    const heroSubtitle = document.querySelector('.hero-subtitle');

    if (heroTitle && heroSubtitle) {
        const titleText = heroTitle.textContent;
        const subtitleText = heroSubtitle.textContent;

        heroTitle.textContent = '';
        heroSubtitle.textContent = '';

        // 打字机效果函数
        function typeWriter(element, text, speed = 100) {
            return new Promise((resolve) => {
                let i = 0;
                const timer = setInterval(() => {
                    if (i < text.length) {
                        element.textContent += text.charAt(i);
                        i++;
                    } else {
                        clearInterval(timer);
                        resolve();
                    }
                }, speed);
            });
        }

        // 执行打字机效果
        setTimeout(() => {
            typeWriter(heroTitle, titleText, 150).then(() => {
                return typeWriter(heroSubtitle, subtitleText, 80);
            });
        }, 500);
    }

    // 响应式导航菜单（移动端）
    const createMobileMenu = () => {
        const navContainer = document.querySelector('.nav-container');
        const navMenu = document.querySelector('.nav-menu');

        // 创建汉堡菜单按钮
        const hamburger = document.createElement('button');
        hamburger.classList.add('hamburger');
        hamburger.innerHTML = '☰';
        hamburger.style.display = 'none';
        hamburger.style.background = 'none';
        hamburger.style.border = 'none';
        hamburger.style.fontSize = '24px';
        hamburger.style.cursor = 'pointer';

        navContainer.appendChild(hamburger);

        // 移动端菜单切换
        hamburger.addEventListener('click', () => {
            navMenu.classList.toggle('mobile-active');
        });

        // 检查屏幕尺寸
        const checkScreenSize = () => {
            if (window.innerWidth <= 768) {
                hamburger.style.display = 'block';
                navMenu.style.display = navMenu.classList.contains('mobile-active') ? 'flex' : 'none';
            } else {
                hamburger.style.display = 'none';
                navMenu.style.display = 'flex';
            }
        };

        window.addEventListener('resize', checkScreenSize);
        checkScreenSize();
    };

    createMobileMenu();

    // 社交媒体链接功能
    const socialItems = document.querySelectorAll('.social-item');
    socialItems.forEach(item => {
        item.addEventListener('click', function (e) {
            e.preventDefault();
            const title = this.getAttribute('title');
            const icon = this.querySelector('.social-icon');

            if (icon.classList.contains('wechat') || icon.classList.contains('wechat-group')) {
                showQRCodeModal(title, '请使用微信扫描二维码');
            } else if (icon.classList.contains('weibo')) {
                // 实际使用时替换为真实的微博链接
                window.open('https://weibo.com/mengchong', '_blank');
            } else if (icon.classList.contains('douyin')) {
                showQRCodeModal(title, '请使用抖音扫描二维码关注');
            } else if (icon.classList.contains('video')) {
                showQRCodeModal(title, '请使用微信扫描二维码关注视频号');
            } else if (icon.classList.contains('xiaohongshu')) {
                showQRCodeModal(title, '请使用小红书扫描二维码关注');
            }
        });
    });

    // 显示二维码模态框
    function showQRCodeModal(title, description) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'qr-modal';
        modal.innerHTML = `
            <div class="qr-modal-content">
                <div class="qr-modal-header">
                    <h3>${title}</h3>
                    <button class="qr-modal-close">&times;</button>
                </div>
                <div class="qr-modal-body">
                    <div class="qr-placeholder">
                        <div class="qr-icon">📱</div>
                        <p>${description}</p>
                        <small>请将对应的二维码图片放置在images/social/文件夹中</small>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 显示模态框
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // 关闭模态框
        const closeBtn = modal.querySelector('.qr-modal-close');
        closeBtn.addEventListener('click', closeModal);
        modal.addEventListener('click', function (e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        function closeModal() {
            modal.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        }
    }
});

// CSS 样式添加（涟漪效果）
const style = document.createElement('style');
style.textContent = `
    button {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .mobile-active {
        display: flex !important;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 20px;
        border-radius: 0 0 15px 15px;
    }

    @media (max-width: 768px) {
        .nav-menu {
            display: none;
        }

        .nav-actions {
            display: none;
        }
    }

    /* 二维码模态框样式 */
    .qr-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .qr-modal.show {
        opacity: 1;
        visibility: visible;
    }

    .qr-modal-content {
        background: white;
        border-radius: 15px;
        max-width: 400px;
        width: 90%;
        max-height: 80vh;
        overflow: hidden;
        transform: scale(0.7);
        transition: transform 0.3s ease;
    }

    .qr-modal.show .qr-modal-content {
        transform: scale(1);
    }

    .qr-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #eee;
    }

    .qr-modal-header h3 {
        margin: 0;
        color: #333;
        font-size: 18px;
    }

    .qr-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #999;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .qr-modal-close:hover {
        background: #f5f5f5;
        color: #333;
    }

    .qr-modal-body {
        padding: 30px 20px;
    }

    .qr-placeholder {
        text-align: center;
    }

    .qr-icon {
        font-size: 48px;
        margin-bottom: 15px;
    }

    .qr-placeholder p {
        font-size: 16px;
        color: #333;
        margin-bottom: 10px;
    }

    .qr-placeholder small {
        color: #999;
        font-size: 12px;
    }
`;
document.head.appendChild(style);
