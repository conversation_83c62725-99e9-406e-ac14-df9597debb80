CREATE TABLE `im_admin_visitor_relation`
(
    `admin_visitor_relation_id` bigint                                  NOT NULL AUTO_INCREMENT COMMENT '成员分组id',
    `merchant_id`               bigint                                  NOT NULL COMMENT '商户ID',
    `admin_id`                  bigint                                  NOT NULL COMMENT '客服ID',
    `visitor_id`                varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '访客ID',
    `merchant_channel_id`       bigint                                                         DEFAULT NULL COMMENT '商户渠道ID',
    `merchant_channel_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '商户渠道',
    `is_top`                    char(1) COLLATE utf8mb4_general_ci      NOT NULL               DEFAULT '0' COMMENT '是否置顶',
    `end_service`               char(1) COLLATE utf8mb4_general_ci                             DEFAULT NULL COMMENT '服务是否结束;0->否;1->已结束',
    `is_blocked`                char(1) COLLATE utf8mb4_general_ci      NOT NULL               DEFAULT '0' COMMENT '是否拉黑 (0=否, 1=是)',
    `chat_remark`               varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '聊天备注',
    `create_time`               timestamp NULL DEFAULT NULL COMMENT '创建日期',
    `update_time`               timestamp NULL DEFAULT NULL COMMENT '更新日期',
    `tenant_id`                 varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   DEFAULT NULL COMMENT '租户编号',
    `create_dept`               bigint                                                         DEFAULT NULL COMMENT '创建部门',
    `create_by`                 bigint                                                         DEFAULT NULL COMMENT '创建人',
    `update_by`                 bigint                                                         DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`admin_visitor_relation_id`) USING BTREE,
    UNIQUE KEY `uk_admin_visitor` (`merchant_id`,`admin_id`,`visitor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1947580968542449666 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客服与访客服务关系表';


CREATE TABLE `im_visitor`
(
    `id`                  bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `visitor_id`          varchar(100)                                                 NOT NULL COMMENT '游客唯一标识',
    `merchant_id`         bigint                                                       NOT NULL COMMENT '商户ID',
    `merchant_channel_id` bigint                                                                DEFAULT NULL COMMENT '商户渠道ID',
    `visitor_name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT '' COMMENT '游客显示名称',
    `avatar_url`          varchar(500)                                                          DEFAULT 'ufImgServer/neweggtest-mengjiala/20250723/fadd94704a3e4288aaf932dfbd15ecff.png' COMMENT '头像URL',
    `channel`             varchar(200)                                                 NOT NULL COMMENT '访问渠道标识',
    `ip_address`          varchar(45)                                                  NOT NULL COMMENT '访客IP地址(支持IPv6)',
    `location_info`       varchar(200)                                                          DEFAULT NULL COMMENT 'IP地理位置信息',
    `from_url`            varchar(1000)                                                         DEFAULT NULL COMMENT '来源页面URL',
    `user_agent`          text COMMENT '浏览器信息',
    `device_info`         json                                                                  DEFAULT NULL COMMENT '设备信息JSON格式',
    `browser_info`        json                                                                  DEFAULT NULL COMMENT '浏览器详细信息JSON格式',
    `language`            varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'en-US' COMMENT '客户端语言',
    `timezone`            varchar(50)                                                           DEFAULT NULL COMMENT '时区',
    `status`              tinyint                                                      NOT NULL DEFAULT '1' COMMENT '状态: 0=离线, 1=在线',
    `cookie_id`           varchar(100)                                                          DEFAULT NULL COMMENT 'Cookie标识',
    `fingerprint`         varchar(64)                                                           DEFAULT NULL COMMENT '浏览器指纹',
    `utm_source`          varchar(100)                                                          DEFAULT NULL COMMENT '访问来源',
    `utm_medium`          varchar(100)                                                          DEFAULT NULL COMMENT '访问媒介',
    `utm_campaign`        varchar(100)                                                          DEFAULT NULL COMMENT '广告系列',
    `utm_content`         varchar(100)                                                          DEFAULT NULL COMMENT 'UTM内容',
    `utm_term`            varchar(100)                                                          DEFAULT NULL COMMENT 'UTM关键词',
    `custom_fields`       json                                                                  DEFAULT NULL COMMENT '自定义字段JSON格式',
    `remark`              varchar(500)                                                          DEFAULT NULL COMMENT '备注信息',
    `create_time`         timestamp NULL DEFAULT NULL COMMENT '创建日期',
    `update_time`         timestamp NULL DEFAULT NULL COMMENT '更新日期',
    `tenant_id`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '租户编号',
    `create_dept`         bigint                                                                DEFAULT NULL COMMENT '创建部门',
    `create_by`           bigint                                                                DEFAULT NULL COMMENT '创建人',
    `update_by`           bigint                                                                DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_visitor_business` (`visitor_id`,`merchant_id`),
    KEY                   `idx_business_id` (`merchant_id`),
    KEY                   `idx_visitor_id` (`visitor_id`),
    KEY                   `idx_ip_address` (`ip_address`),
    KEY                   `idx_status` (`status`),
    KEY                   `idx_create_time` (`create_time`),
    KEY                   `idx_visitor_status` (`visitor_id`,`merchant_id`,`status`),
    KEY                   `idx_business_online` (`merchant_id`,`status`),
    KEY                   `idx_cookie_fingerprint` (`cookie_id`,`fingerprint`)
) ENGINE=InnoDB AUTO_INCREMENT=1947580961189834755 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='游客访问表';


CREATE TABLE `im_visitor_label`
(
    `visitor_label_id`    bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '游客标签id',
    `merchant_id`         bigint                                                        NOT NULL COMMENT '商户ID',
    `merchant_channel_id` bigint                                                       DEFAULT NULL COMMENT '商户渠道ID',
    `custom_label_id`     bigint                                                        NOT NULL COMMENT '标签ID',
    `visitor_id`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '访客ID',
    `create_time`         timestamp NULL DEFAULT NULL COMMENT '创建日期',
    `update_time`         timestamp NULL DEFAULT NULL COMMENT '更新日期',
    `tenant_id`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户编号',
    `create_dept`         bigint                                                       DEFAULT NULL COMMENT '创建部门',
    `create_by`           bigint                                                       DEFAULT NULL COMMENT '创建人',
    `update_by`           bigint                                                       DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`visitor_label_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1947265890076864514 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='访客标签表';


CREATE TABLE `im_custom_label` (
                                   `custom_label_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户标签id',
                                   `custom_label_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户标签名称',
                                   `merchant_id` bigint DEFAULT NULL COMMENT '商户ID',
                                   `merchant_channel_id` bigint DEFAULT NULL COMMENT '商户渠道id',
                                   `create_time` timestamp NULL DEFAULT NULL COMMENT '创建日期',
                                   `update_time` timestamp NULL DEFAULT NULL COMMENT '更新日期',
                                   `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户编号',
                                   `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
                                   `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                   `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                   PRIMARY KEY (`custom_label_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1944042892914429955 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户标签表';


-- 四张表关联查询：以 im_visitor 为主表，关联客服关系和标签信息
-- 方案1：使用 GROUP_CONCAT 将多个标签名合并为一个字符串
SELECT
    v.id,
    v.visitor_id,
    v.visitor_name,
    v.merchant_id,
    v.merchant_channel_id,
    v.avatar_url,
    v.channel,
    v.ip_address,
    v.location_info,
    v.status,
    v.create_time,
    avr.admin_id,
    avr.is_top,
    avr.end_service,
    avr.is_blocked,
    avr.chat_remark,
    GROUP_CONCAT(cl.custom_label_name SEPARATOR ',') AS label_names
FROM im_visitor v
INNER JOIN im_admin_visitor_relation avr ON v.visitor_id = avr.visitor_id AND v.merchant_id = avr.merchant_id
LEFT JOIN im_visitor_label vl ON v.visitor_id = vl.visitor_id AND v.merchant_id = vl.merchant_id
LEFT JOIN im_custom_label cl ON vl.custom_label_id = cl.custom_label_id
WHERE avr.admin_id = ? -- 替换为具体的admin_id值
GROUP BY v.id, v.visitor_id, v.visitor_name, v.merchant_id, v.merchant_channel_id,
         v.avatar_url, v.channel, v.ip_address, v.location_info, v.status, v.create_time,
         avr.admin_id, avr.is_top, avr.end_service, avr.is_blocked, avr.chat_remark;


-- 方案2：如果需要在应用层处理List，可以不使用GROUP_CONCAT，返回多行数据
SELECT
    v.id,
    v.visitor_id,
    v.visitor_name,
    v.merchant_id,
    v.merchant_channel_id,
    v.avatar_url,
    v.channel,
    v.ip_address,
    v.location_info,
    v.status,
    v.create_time,
    avr.admin_id,
    avr.is_top,
    avr.end_service,
    avr.is_blocked,
    avr.chat_remark,
    cl.custom_label_id,
    cl.custom_label_name
FROM im_visitor v
INNER JOIN im_admin_visitor_relation avr ON v.visitor_id = avr.visitor_id AND v.merchant_id = avr.merchant_id
LEFT JOIN im_visitor_label vl ON v.visitor_id = vl.visitor_id AND v.merchant_id = vl.merchant_id
LEFT JOIN im_custom_label cl ON vl.custom_label_id = cl.custom_label_id
WHERE avr.admin_id = ? -- 替换为具体的admin_id值
ORDER BY v.id, cl.custom_label_id;
