-- 将 billing_cycle 字段改为数字代码表示
-- 0->月付(monthly), 1->季付(quarterly), 2->年付(annually), 3->终身(lifetime), 4->自定义(custom)

-- 1. 修改 billing_cycle 字段类型
ALTER TABLE im_chat_package
    MODIFY COLUMN billing_cycle TINYINT DEFAULT 0 COMMENT '计费周期(0-月付 1-季付 2-年付 3-终身 4-自定义)';

-- 2. 更新现有数据（如果有的话）
UPDATE im_chat_package
SET billing_cycle = CASE
                        WHEN billing_cycle = 'monthly' OR billing_cycle = '0' THEN 0
                        WHEN billing_cycle = 'quarterly' OR billing_cycle = '1' THEN 1
                        WHEN billing_cycle = 'annually' OR billing_cycle = '2' THEN 2
                        WHEN billing_cycle = 'lifetime' OR billing_cycle = '3' THEN 3
                        WHEN billing_cycle = 'custom' OR billing_cycle = '4' THEN 4
                        ELSE 0 -- 默认为月付
    END;

-- 3. 更新或重新创建计算过期时间的函数
DROP FUNCTION IF EXISTS calculate_subscription_end_date;

DELIMITER
//
CREATE FUNCTION calculate_subscription_end_date(
    start_date DATETIME,
    billing_cycle TINYINT,
    billing_period INT
) RETURNS DATETIME
    READS SQL DATA
    DETERMINISTIC
BEGIN
    DECLARE
end_date DATETIME;

CASE billing_cycle
        WHEN 0 THEN  -- monthly
            SET end_date = DATE_ADD(start_date, INTERVAL billing_period MONTH);
WHEN 1 THEN  -- quarterly
            SET end_date = DATE_ADD(start_date, INTERVAL (billing_period * 3) MONTH);
WHEN 2 THEN  -- annually
            SET end_date = DATE_ADD(start_date, INTERVAL billing_period YEAR);
WHEN 3 THEN  -- lifetime
            SET end_date = DATE_ADD(start_date, INTERVAL 100 YEAR); -- 设置为100年后
WHEN 4 THEN  -- custom (默认按月计算)
            SET end_date = DATE_ADD(start_date, INTERVAL billing_period MONTH);
ELSE
            SET end_date = DATE_ADD(start_date, INTERVAL billing_period MONTH);
END
CASE;

RETURN end_date;
END
//
DELIMITER ;

-- 4. 插入套餐数据示例（使用数字代码）
INSERT INTO im_chat_package (package_name, package_type, price, billing_cycle, billing_period, description,
                             feature_config, sort_order)
VALUES
-- 标准版 - 不同计费周期
('标准版-月付', '1', 150.00, 0, 1, '标准版月付套餐，适合小型初创企业',
 JSON_OBJECT(
     'onlineChat', true, -- 在线对话功能
     'teamCollaboration', true, -- 团队协作功能
     'multiSeatAccess', true, -- 多坐席接入功能
     'smartTranslation', true, -- 智能翻译功能
     'chinaMainlandAvailable', false, -- 中国大陆可用性
     'seatsCount', 1, -- 坐席数量限制
     'selectDataLimit', 3, -- 选择数据限制
     'receptionDataLimit', 25, -- 接待数据限制
     'translationCharLimit', 100000 -- 翻译字符限制
 ), 10),

('标准版-季付', '1', 420.00, 0, 3, '标准版季付套餐，享受7%折扣',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'chinaMainlandAvailable', false,
     'seatsCount', 1,
     'selectDataLimit', 3,
     'receptionDataLimit', 25,
     'translationCharLimit', 100000,
     'discountRate', 0.07 -- 折扣率
 ), 11),

('标准版-半年付', '1', 810.00, 0, 6, '标准版半年付套餐，享受10%折扣',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'chinaMainlandAvailable', false,
     'seatsCount', 1,
     'selectDataLimit', 3,
     'receptionDataLimit', 25,
     'translationCharLimit', 100000,
     'discountRate', 0.10
 ), 12),

('标准版-年付', '1', 1440.00, 2, 1, '标准版年付套餐，享受20%折扣',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'chinaMainlandAvailable', false,
     'seatsCount', 1,
     'selectDataLimit', 3,
     'receptionDataLimit', 25,
     'translationCharLimit', 100000,
     'discountRate', 0.20
 ), 13),

('标准版-两年付', '1', 2700.00, 2, 2, '标准版两年付套餐，享受25%折扣',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'chinaMainlandAvailable', false,
     'seatsCount', 1,
     'selectDataLimit', 3,
     'receptionDataLimit', 25,
     'translationCharLimit', 100000,
     'discountRate', 0.25
 ), 14),

('标准版-终身版', '1', 3000.00, 3, 0, '标准版终身使用，一次付费永久使用',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'chinaMainlandAvailable', false,
     'seatsCount', 1,
     'selectDataLimit', 3,
     'receptionDataLimit', 25,
     'translationCharLimit', 100000,
     'lifetimeAccess', true
 ), 15),

-- 专业版 - 不同计费周期
('专业版-月付', '2', 250.00, 0, 1, '专业版月付套餐，适合中小型企业',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'advancedTools', true,
     'chinaMainlandAvailable', true,
     'seatsCount', 1,
     'selectDataLimit', 5,
     'receptionDataLimit', 50,
     'translationCharLimit', 300000
 ), 20),

('专业版-年付', '2', 2460.00, 2, 1, '专业版年付套餐，享受18%折扣',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'advancedTools', true,
     'chinaMainlandAvailable', true,
     'seatsCount', 1,
     'selectDataLimit', 5,
     'receptionDataLimit', 50,
     'translationCharLimit', 300000,
     'discountRate', 0.18
 ), 21),

-- 集团版 - 自定义计费
('集团版-定制', '3', NULL, 4, 12, '集团版定制套餐，价格面议',
 JSON_OBJECT(
     'onlineChat', true,
     'teamCollaboration', true,
     'multiSeatAccess', true,
     'smartTranslation', true,
     'advancedTools', true,
     'apiAccess', true,
     'customizationService', true,
     'chinaMainlandAvailable', true,
     'seatsCount', 999,
     'selectDataLimit', 999,
     'receptionDataLimit', 999,
     'translationCharLimit', 999999
 ), 30);

-- 5. 查询示例（使用数字代码）

-- 计费周期代码说明查询
SELECT 0         as billing_cycle_code,
       '月付'    as billing_cycle_name,
       'monthly' as billing_cycle_en
UNION ALL
SELECT 1           as billing_cycle_code,
       '季付'      as billing_cycle_name,
       'quarterly' as billing_cycle_en
UNION ALL
SELECT 2          as billing_cycle_code,
       '年付'     as billing_cycle_name,
       'annually' as billing_cycle_en
UNION ALL
SELECT 3          as billing_cycle_code,
       '终身'     as billing_cycle_name,
       'lifetime' as billing_cycle_en
UNION ALL
SELECT 4        as billing_cycle_code,
       '自定义' as billing_cycle_name,
       'custom' as billing_cycle_en;

-- 查询套餐的计费周期信息（使用数字代码）
SELECT package_name,
       billing_cycle,
       CASE billing_cycle
           WHEN 0 THEN '月付'
           WHEN 1 THEN '季付'
           WHEN 2 THEN '年付'
           WHEN 3 THEN '终身'
           WHEN 4 THEN '自定义'
           ELSE '未知'
           END as billing_cycle_name,
       billing_period,
       price,
       CASE
           WHEN billing_cycle = 0 THEN CONCAT(billing_period, '个月')
           WHEN billing_cycle = 1 THEN CONCAT(billing_period, '个季度')
           WHEN billing_cycle = 2 THEN CONCAT(billing_period, '年')
           WHEN billing_cycle = 3 THEN '终身'
           WHEN billing_cycle = 4 THEN CONCAT(billing_period, '个月(自定义)')
           ELSE CONCAT(billing_period, '个周期')
           END as period_display,
       CASE
           WHEN billing_cycle = 0 THEN ROUND(price / billing_period, 2)
           WHEN billing_cycle = 1 THEN ROUND(price / (billing_period * 3), 2)
           WHEN billing_cycle = 2 THEN ROUND(price / (billing_period * 12), 2)
           WHEN billing_cycle = 3 THEN 0
           WHEN billing_cycle = 4 THEN ROUND(price / billing_period, 2)
           ELSE ROUND(price / billing_period, 2)
           END as monthly_cost
FROM im_chat_package
WHERE status = 1
ORDER BY package_type, sort_order;

-- 6. 按计费周期查询套餐

-- 查询月付套餐
SELECT package_name, price, billing_period, description
FROM im_chat_package
WHERE billing_cycle = 0
  AND status = 1
ORDER BY package_type;

-- 查询季付套餐
SELECT package_name, price, billing_period, description
FROM im_chat_package
WHERE billing_cycle = 1
  AND status = 1
ORDER BY package_type;

-- 查询年付套餐
SELECT package_name,
       price,
       billing_period,
       description,
       JSON_EXTRACT(feature_config, '$.discountRate') as discount_rate
FROM im_chat_package
WHERE billing_cycle = 2
  AND status = 1
ORDER BY package_type;

-- 查询终身套餐
SELECT package_name, price, description
FROM im_chat_package
WHERE billing_cycle = 3
  AND status = 1
ORDER BY package_type;

-- 7. 创建订阅时计算过期时间（使用数字代码）
-- INSERT INTO im_user_subscription (user_id, package_id, start_date, end_date, payment_amount, payment_currency)
-- SELECT
--     123 as user_id,  -- 用户ID
--     p.id as package_id,
--     NOW() as start_date,
--     calculate_subscription_end_date(NOW(), p.billing_cycle, p.billing_period) as end_date,
--     p.price as payment_amount,
--     p.currency as payment_currency
-- FROM im_chat_package p
-- WHERE p.id = 1;  -- 选择的套餐ID

-- 8. 查询不同计费周期的订阅统计
-- SELECT
--     CASE p.billing_cycle
--         WHEN 0 THEN '月付'
--         WHEN 1 THEN '季付'
--         WHEN 2 THEN '年付'
--         WHEN 3 THEN '终身'
--         WHEN 4 THEN '自定义'
--     END as billing_cycle_name,
--     p.billing_period,
--     COUNT(s.id) as subscription_count,
--     SUM(s.payment_amount) as total_revenue
-- FROM im_user_subscription s
-- JOIN im_chat_package p ON s.package_id = p.id
-- WHERE s.status = 1
-- GROUP BY p.billing_cycle, p.billing_period
-- ORDER BY subscription_count DESC;

-- 9. 为现有 billing_cycle_usage_examples.sql 中的查询更新数字代码
-- 将原有查询中的字符串替换为数字代码

-- 价格展示查询（数字代码版本）
SELECT package_name,
       package_type,
       price,
       billing_cycle,
       billing_period,
       CASE
           WHEN billing_cycle = 0 THEN CONCAT('$', price, '/', billing_period, '个月')
           WHEN billing_cycle = 1 THEN CONCAT('$', price, '/', billing_period, '个季度')
           WHEN billing_cycle = 2 THEN CONCAT('$', price, '/', billing_period, '年')
           WHEN billing_cycle = 3 THEN CONCAT('$', price, ' 终身')
           WHEN billing_cycle = 4 THEN CONCAT('$', price, '(定制', billing_period, '个月)')
           ELSE CONCAT('$', price, '/', billing_period, '个周期')
           END as price_display,
       description
FROM im_chat_package
WHERE status = 1
ORDER BY package_type, sort_order;

-- 10. 创建视图简化查询
CREATE VIEW v_package_billing_info AS
SELECT id,
       package_name,
       package_type,
       price,
       billing_cycle,
       CASE billing_cycle
           WHEN 0 THEN '月付'
           WHEN 1 THEN '季付'
           WHEN 2 THEN '年付'
           WHEN 3 THEN '终身'
           WHEN 4 THEN '自定义'
           ELSE '未知'
           END as billing_cycle_name,
       billing_period,
       CASE
           WHEN billing_cycle = 0 THEN CONCAT(billing_period, '个月')
           WHEN billing_cycle = 1 THEN CONCAT(billing_period, '个季度')
           WHEN billing_cycle = 2 THEN CONCAT(billing_period, '年')
           WHEN billing_cycle = 3 THEN '终身'
           WHEN billing_cycle = 4 THEN CONCAT(billing_period, '个月(自定义)')
           ELSE CONCAT(billing_period, '个周期')
           END as period_display,
       CASE
           WHEN billing_cycle = 0 THEN ROUND(price / billing_period, 2)
           WHEN billing_cycle = 1 THEN ROUND(price / (billing_period * 3), 2)
           WHEN billing_cycle = 2 THEN ROUND(price / (billing_period * 12), 2)
           WHEN billing_cycle = 3 THEN 0
           WHEN billing_cycle = 4 THEN ROUND(price / billing_period, 2)
           ELSE ROUND(price / billing_period, 2)
           END as monthly_cost,
       description,
       feature_config,
       status,
       sort_order
FROM im_chat_package;

-- 使用视图的示例查询
-- SELECT * FROM v_package_billing_info WHERE status = 1 ORDER BY package_type, sort_order;
