<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 聊天测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .chat-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .user-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .user-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .login-form {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 10px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"], input[type="password"], input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        button.register {
            background-color: #28a745;
        }

        button.register:hover {
            background-color: #218838;
        }

        button.disconnect {
            background-color: #dc3545;
        }

        button.disconnect:hover {
            background-color: #c82333;
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .chat-box {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            overflow-y: auto;
            background-color: #fafafa;
            margin-bottom: 10px;
        }

        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.sent {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            margin-right: 0;
            text-align: right;
        }

        .message.received {
            background-color: #e9ecef;
            color: #333;
            margin-left: 0;
            margin-right: auto;
            text-align: left;
        }

        .message.system {
            background-color: #fff3cd;
            color: #856404;
            font-style: italic;
            text-align: center;
            margin: 5px auto;
            max-width: 90%;
            font-size: 12px;
        }

        .message-input {
            display: flex;
            gap: 10px;
        }

        .message-input input {
            flex: 1;
        }

        .config-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .config-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .config-form {
            display: flex;
            gap: 20px;
            align-items: end;
        }

        .config-group {
            flex: 1;
        }

        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .config-group input, .config-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-group select {
            background-color: white;
            cursor: pointer;
        }

        .config-group input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>WebSocket 聊天测试工具</h1>
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <p style="color: #666; margin: 0;">
            <strong>使用说明：</strong>
            1. 登录两个用户获取真实用户ID
            2. 连接WebSocket
            3. 测试1对1聊天功能
            <br>
            <strong>测试方式：</strong>
            • 输入框发送：WebSocket直接发送
            • 测试API：REST API发送（使用WebSocketUtils.publishMessage）
            • 演示聊天：自动发送预设消息
        </p>
        <div id="onlineStatus"
             style="background: #f8f9fa; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6;">
            <strong>在线状态：</strong><span id="onlineCount">0</span>/2 用户在线
        </div>
    </div>

    <!-- 配置区域 -->
    <div class="config-section">
        <div class="config-title">服务器配置</div>
        <div class="config-form">
            <div class="config-group">
                <label for="serverHost">服务器地址:</label>
                <select id="serverHost" onchange="updateServerUrls()">
                    <option value="localhost">localhost (本地开发)</option>
                    <option value="*************">************* (远程服务器)</option>
                </select>
            </div>
            <div class="config-group">
                <label for="serverUrl">WebSocket服务器地址:</label>
                <input type="text" id="serverUrl" value="ws://localhost:8088/resource/visitor/chat" readonly>
            </div>
            <div class="config-group">
                <label for="loginUrl">登录API地址:</label>
                <input type="text" id="loginUrl" value="http://localhost:8088/api/merchants/login" readonly>
            </div>
            <div class="config-group">
                <label for="registerUrl">注册API地址:</label>
                <input type="text" id="registerUrl" value="http://localhost:8088/api/merchants/register" readonly>
            </div>
            <div class="config-group">
                <label for="chatSendUrl">聊天发送API地址:</label>
                <input type="text" id="chatSendUrl" value="http://localhost:8088/api/chat/send" readonly>
            </div>
            <div class="config-group">
                <label for="chatBroadcastUrl">广播消息API地址:</label>
                <input type="text" id="chatBroadcastUrl" value="http://localhost:8088/api/chat/broadcast" readonly>
            </div>
            <div class="config-group">
                <label>
                    <input type="checkbox" id="useLocalSimulation" checked> 本地模拟模式
                </label>
                <small style="display: block; color: #666; margin-top: 5px;">
                    勾选：浏览器内模拟 | 取消：真实WebSocket通信
                </small>
            </div>
            <div class="config-group">
                <button onclick="updateConfig()">更新配置</button>
            </div>
        </div>
    </div>

    <!-- 聊天区域 -->
    <div class="chat-container">
        <!-- 用户1 -->
        <div class="user-panel">
            <div class="user-title">用户1 (Alice)</div>

            <!-- 登录表单 -->
            <div class="login-form">
                <div class="form-group">
                    <label for="user1-email">邮箱:</label>
                    <input type="email" id="user1-email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="user1-password">密码:</label>
                    <input type="password" id="user1-password" value="Qq123456">
                </div>
                <div class="form-group">
                    <label for="user1-company">公司名称:</label>
                    <input type="text" id="user1-company" value="示例科技有限公司">
                </div>
                <button onclick="loginUser(1)">登录</button>
                <button onclick="registerUser(1)" class="register">注册</button>
                <button onclick="connectWebSocket(1)">连接WebSocket</button>
                <button onclick="disconnectWebSocket(1)" class="disconnect">断开连接</button>
                <button onclick="startDemo(1)" style="background-color: #17a2b8;">演示聊天</button>
                <button onclick="testRestAPI(1)" style="background-color: #6f42c1;">测试API</button>
            </div>

            <!-- 用户信息 -->
            <div id="user1-info"
                 style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin-bottom: 10px; font-size: 12px;">
                <strong>用户信息：</strong><br>
                商户ID: <span id="user1-realId">未登录</span>
            </div>

            <!-- 连接状态 -->
            <div id="user1-status" class="status disconnected">未连接</div>

            <!-- 聊天框 -->
            <div id="user1-chat" class="chat-box"></div>

            <!-- 消息输入 -->
            <div class="message-input">
                <input type="text" id="user1-message" placeholder="输入消息..." onkeypress="handleKeyPress(event, 1)">
                <button onclick="sendMessage(1)">发送</button>
                <button onclick="sendPing(1)">Ping</button>
                <button onclick="clearChat(1)" style="background-color: #6c757d;">清空</button>
            </div>
        </div>

        <!-- 用户2 -->
        <div class="user-panel">
            <div class="user-title">用户2 (Bob)</div>

            <!-- 登录表单 -->
            <div class="login-form">
                <div class="form-group">
                    <label for="user2-email">邮箱:</label>
                    <input type="email" id="user2-email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="user2-password">密码:</label>
                    <input type="password" id="user2-password" value="Qq123456">
                </div>
                <div class="form-group">
                    <label for="user2-company">公司名称:</label>
                    <input type="text" id="user2-company" value="另一家科技有限公司">
                </div>
                <button onclick="loginUser(2)">登录</button>
                <button onclick="registerUser(2)" class="register">注册</button>
                <button onclick="connectWebSocket(2)">连接WebSocket</button>
                <button onclick="disconnectWebSocket(2)" class="disconnect">断开连接</button>
                <button onclick="startDemo(2)" style="background-color: #17a2b8;">演示聊天</button>
                <button onclick="testRestAPI(2)" style="background-color: #6f42c1;">测试API</button>
            </div>

            <!-- 用户信息 -->
            <div id="user2-info"
                 style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin-bottom: 10px; font-size: 12px;">
                <strong>用户信息：</strong><br>
                商户ID: <span id="user2-realId">未登录</span>
            </div>

            <!-- 连接状态 -->
            <div id="user2-status" class="status disconnected">未连接</div>

            <!-- 聊天框 -->
            <div id="user2-chat" class="chat-box"></div>

            <!-- 消息输入 -->
            <div class="message-input">
                <input type="text" id="user2-message" placeholder="输入消息..." onkeypress="handleKeyPress(event, 2)">
                <button onclick="sendMessage(2)">发送</button>
                <button onclick="sendPing(2)">Ping</button>
                <button onclick="clearChat(2)" style="background-color: #6c757d;">清空</button>
            </div>
        </div>
    </div>
</div>

<script>
    // 全局配置
    let config = {
        serverUrl: 'ws://localhost:8088/visitor/chat',
        loginUrl: 'http://localhost:8088/api/merchants/login',
        registerUrl: 'http://localhost:8088/api/merchants/register',
        chatSendUrl: 'http://localhost:8088/api/chat/send',
        chatBroadcastUrl: 'http://localhost:8088/api/chat/broadcast',
        useLocalSimulation: true  // 是否使用本地模拟聊天（true=本地模拟，false=真实WebSocket通信）
    };

    // 用户状态
    const users = {
        1: {
            name: 'Alice',
            token: null,
            websocket: null,
            connected: false,
            realUserId: null  // 存储真实的用户ID
        },
        2: {
            name: 'Bob',
            token: null,
            websocket: null,
            connected: false,
            realUserId: null  // 存储真实的用户ID
        }
    };

    // 本地模拟消息广播（仅用于本地测试）
    function broadcastMessage(senderUserId, message) {
        const senderName = users[senderUserId].name;
        Object.keys(users).forEach(userId => {
            const id = parseInt(userId);
            if (id !== senderUserId && users[id].connected) {
                setTimeout(() => {
                    addChatMessage(id, senderName, message, 'other');
                }, 50);
            }
        });
    }

    // 更新服务器URL
    function updateServerUrls() {
        const serverHost = document.getElementById('serverHost').value;
        const port = '8088';

        // 根据选择的服务器更新所有URL
        const wsProtocol = 'ws://';
        const httpProtocol = 'http://';

        document.getElementById('serverUrl').value = `${wsProtocol}${serverHost}:${port}/resource/websocket`;
        document.getElementById('loginUrl').value = `${httpProtocol}${serverHost}:${port}/api/merchants/login`;
        document.getElementById('registerUrl').value = `${httpProtocol}${serverHost}:${port}/api/merchants/register`;
        document.getElementById('chatSendUrl').value = `${httpProtocol}${serverHost}:${port}/api/chat/send`;
        document.getElementById('chatBroadcastUrl').value = `${httpProtocol}${serverHost}:${port}/api/chat/broadcast`;

        // 自动更新配置
        updateConfig();
    }

    // 更新配置
    function updateConfig() {
        config.serverUrl = document.getElementById('serverUrl').value;
        config.loginUrl = document.getElementById('loginUrl').value;
        config.registerUrl = document.getElementById('registerUrl').value;
        config.chatSendUrl = document.getElementById('chatSendUrl').value;
        config.chatBroadcastUrl = document.getElementById('chatBroadcastUrl').value;
        config.useLocalSimulation = document.getElementById('useLocalSimulation').checked;

        const serverHost = document.getElementById('serverHost').value;
        const mode = config.useLocalSimulation ? '本地模拟模式' : '真实WebSocket模式';
        addSystemMessage(1, `配置已更新 - 服务器: ${serverHost}, 模式: ${mode}`);
        addSystemMessage(2, `配置已更新 - 服务器: ${serverHost}, 模式: ${mode}`);
    }

    // 用户注册
    async function registerUser(userId) {
        const email = document.getElementById(`user${userId}-email`).value;
        const password = document.getElementById(`user${userId}-password`).value;
        const companyName = document.getElementById(`user${userId}-company`).value;

        const registerData = {
            email: email,
            password: password,
            companyName: companyName
        };

        try {
            addSystemMessage(userId, '正在注册...');

            const response = await fetch(config.registerUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(registerData)
            });

            const result = await response.json();

            if (result.code === 200 && result.data && result.data.accessToken) {
                users[userId].token = result.data.accessToken;
                users[userId].realUserId = result.data.adminId; // adminId就是用户ID

                // 更新界面显示
                document.getElementById(`user${userId}-realId`).textContent = result.data.adminId;

                addSystemMessage(userId, `注册成功! 公司: ${companyName}`);
                addSystemMessage(userId, `商户ID(用户ID): ${result.data.adminId}`);
                addSystemMessage(userId, `Token: ${result.data.accessToken.substring(0, 20)}...`);
            } else {
                addSystemMessage(userId, `注册失败: ${result.msg || '未知错误'}`);
            }
        } catch (error) {
            addSystemMessage(userId, `注册错误: ${error.message}`);
        }
    }

    // 用户登录
    async function loginUser(userId) {
        const email = document.getElementById(`user${userId}-email`).value;
        const password = document.getElementById(`user${userId}-password`).value;

        const loginData = {
            email: email,
            password: password
        };

        try {
            addSystemMessage(userId, '正在登录...');

            const response = await fetch(config.loginUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(loginData)
            });

            const result = await response.json();

            if (result.code === 200 && result.data && result.data.accessToken) {
                users[userId].token = result.data.accessToken;
                users[userId].realUserId = result.data.adminId; // adminId就是用户ID

                // 更新界面显示
                document.getElementById(`user${userId}-realId`).textContent = result.data.adminId;

                addSystemMessage(userId, `登录成功! 管理员: ${result.data.adminName || 'Unknown'}`);
                addSystemMessage(userId, `商户ID(用户ID): ${result.data.adminId}`);
                addSystemMessage(userId, `管理员ID: ${result.data.adminId}`);
                addSystemMessage(userId, `Token: ${result.data.accessToken.substring(0, 20)}...`);
            } else {
                addSystemMessage(userId, `登录失败: ${result.msg || '未知错误'}`);
            }
        } catch (error) {
            addSystemMessage(userId, `登录错误: ${error.message}`);
        }
    }

    // 连接WebSocket
    function connectWebSocket(userId) {
        /*   if (!users[userId].token) {
               addSystemMessage(userId, '请先登录获取Token');
               return;
           }*/

        if (users[userId].websocket) {
            addSystemMessage(userId, 'WebSocket已连接');
            return;
        }

        try {
            updateStatus(userId, 'connecting', '连接中...');

            // 构建WebSocket URL，添加Authorization参数
            const wsUrl = `${config.serverUrl}?Authorization=Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJ2aXNpdG9yOjE5NDU4Mjg2MTc4NjcyNDM1MjEiLCJyblN0ciI6ImhDUXdrSTg4d2JoQWpPY3lwV3RTQ3JDNVBkWkRTZXhLIiwidXNlcklkIjoxOTQ1ODI4NjE3ODY3MjQzNTIxLCJ2aXNpdG9ySWQiOiIxMjMxMjMyIn0.OaVlqAkdZsUOyaRtwey2XPjACrOvC5fYOnYenRFgLzI`;

            const ws = new WebSocket(wsUrl);

            ws.onopen = function (event) {
                users[userId].websocket = ws;
                users[userId].connected = true;
                updateStatus(userId, 'connected', '已连接');
                addSystemMessage(userId, 'WebSocket连接成功');
            };

            ws.onmessage = function (event) {
                const message = event.data;
                if (message === 'pong') {
                    addSystemMessage(userId, '收到心跳响应: pong');
                } else {
                    try {
                        // 尝试解析JSON格式的消息
                        const parsedMessage = JSON.parse(message);

                        // 处理1对1聊天消息
                        if (parsedMessage.type === 'CHAT_MESSAGE') {
                            const fromUserId = parsedMessage.fromUserId;
                            const fromUserName = parsedMessage.from || parsedMessage.fromUserName;
                            const messageContent = parsedMessage.message;

                            // 使用真实用户ID进行比较，只有当消息不是自己发送的时候才显示
                            if (fromUserId !== users[userId].realUserId) {
                                addChatMessage(userId, fromUserName, messageContent, 'other');
                                addSystemMessage(userId, `✅ 收到来自${fromUserName}(ID:${fromUserId})的消息`);
                            } else {
                                addSystemMessage(userId, `忽略自己发送的消息(ID:${fromUserId})`);
                            }
                            return;
                        }


                    } catch (e) {
                        // 不是JSON格式，按普通消息处理
                    }

                    // 显示未识别的消息
                    addSystemMessage(userId, `收到未识别消息: ${message}`);
                }
            };

            ws.onclose = function (event) {
                users[userId].websocket = null;
                users[userId].connected = false;
                updateStatus(userId, 'disconnected', '已断开');
                addSystemMessage(userId, `WebSocket连接关闭: ${event.code} - ${event.reason}`);
            };

            ws.onerror = function (error) {
                addSystemMessage(userId, `WebSocket错误: ${error}`);
                updateStatus(userId, 'disconnected', '连接错误');
            };

        } catch (error) {
            addSystemMessage(userId, `连接失败: ${error.message}`);
            updateStatus(userId, 'disconnected', '连接失败');
        }
    }

    // 断开WebSocket连接
    function disconnectWebSocket(userId) {
        if (users[userId].websocket) {
            users[userId].websocket.close();
            users[userId].websocket = null;
            users[userId].connected = false;
            updateStatus(userId, 'disconnected', '已断开');
            addSystemMessage(userId, 'WebSocket连接已断开');
        }
    }

    // 发送消息
    function sendMessage(userId) {
        const messageInput = document.getElementById(`user${userId}-message`);
        const message = messageInput.value.trim();

        if (!message) {
            return;
        }

        if (!users[userId].websocket || !users[userId].connected) {
            addSystemMessage(userId, '请先连接WebSocket');
            return;
        }

        try {
            // 检查是否有真实用户ID
            if (!users[userId].realUserId) {
                addSystemMessage(userId, '请先登录获取真实用户ID');
                return;
            }

            const targetUserId = userId === 1 ? 2 : 1;

            // 检查目标用户是否已登录
            if (!users[targetUserId].realUserId) {
                addSystemMessage(userId, `目标用户${users[targetUserId].name}还未登录`);
                return;
            }

            // 构造1对1聊天消息格式（使用真实用户ID）
            const chatMessage = {
                type: 'CHAT_MESSAGE',
                from: users[userId].name,
                fromUserId: users[userId].realUserId,  // 使用真实用户ID
                to: users[targetUserId].name,
                toUserId: users[targetUserId].realUserId,  // 使用真实用户ID
                message: message,
                timestamp: new Date().toLocaleTimeString()
            };

            // 发送到WebSocket服务器，服务器会使用WebSocketUtils.publishMessage
            // 将消息发送给指定的接收人（通过sessionKeys）
            const messageToSend = JSON.stringify(chatMessage);
            users[userId].websocket.send(messageToSend);

            // 在发送者聊天框显示
            addChatMessage(userId, users[userId].name, message, 'self');

            // 根据配置决定是否使用本地模拟
            if (config.useLocalSimulation) {
                broadcastMessage(userId, message);
            }

            messageInput.value = '';
        } catch (error) {
            addSystemMessage(userId, `发送失败: ${error.message}`);
        }
    }

    // 发送心跳
    function sendPing(userId) {
        if (!users[userId].websocket || !users[userId].connected) {
            addSystemMessage(userId, '请先连接WebSocket');
            return;
        }

        try {
            users[userId].websocket.send('ping');
            addSystemMessage(userId, '发送心跳: ping');
        } catch (error) {
            addSystemMessage(userId, `心跳发送失败: ${error.message}`);
        }
    }

    // 处理回车键发送
    function handleKeyPress(event, userId) {
        if (event.key === 'Enter') {
            sendMessage(userId);
        }
    }

    // 清空聊天记录
    function clearChat(userId) {
        const chatBox = document.getElementById(`user${userId}-chat`);
        chatBox.innerHTML = '';
        addSystemMessage(userId, '聊天记录已清空');
    }

    // 演示聊天功能
    function startDemo(userId) {
        if (!users[userId].connected) {
            addSystemMessage(userId, '请先连接WebSocket');
            return;
        }

        const demoMessages = userId === 1 ? [
            "你好！我是Alice 👋",
            "今天天气真不错呢 ☀️",
            "WebSocket测试成功！ ✅"
        ] : [
            "嗨Alice！我是Bob 😊",
            "是的，很适合聊天 🚶‍♂️",
            "1对1通信很流畅 🚀"
        ];

        let index = 0;
        const sendDemoMessage = () => {
            if (index < demoMessages.length) {
                const messageInput = document.getElementById(`user${userId}-message`);
                messageInput.value = demoMessages[index];
                sendMessage(userId);
                index++;
                setTimeout(sendDemoMessage, 2000);
            }
        };

        addSystemMessage(userId, '开始演示聊天...');
        setTimeout(sendDemoMessage, 1000);
    }

    // 测试REST API发送消息
    async function testRestAPI(userId) {
        if (!users[userId].token) {
            addSystemMessage(userId, '请先登录获取Token');
            return;
        }

        if (!users[userId].realUserId) {
            addSystemMessage(userId, '未获取到真实用户ID，请重新登录');
            return;
        }

        const targetUserId = userId === 1 ? 2 : 1;

        // 检查目标用户是否已登录
        if (!users[targetUserId].realUserId) {
            addSystemMessage(userId, `目标用户${users[targetUserId].name}还未登录，无法获取真实用户ID`);
            return;
        }

        // 使用真实的用户ID
        const apiMessage = {
            fromUserId: users[userId].realUserId,  // 使用真实的用户ID
            fromUserName: users[userId].name,
            toUserId: users[targetUserId].realUserId,  // 使用目标用户的真实ID
            toUserName: users[targetUserId].name,
            message: `通过REST API发送的1对1消息 - 来自${users[userId].name}`
        };

        try {
            addSystemMessage(userId, `正在通过REST API发送消息...`);
            addSystemMessage(userId, `发送方ID: ${apiMessage.fromUserId}`);
            addSystemMessage(userId, `接收方ID: ${apiMessage.toUserId}`);
            addSystemMessage(userId, `消息内容: ${apiMessage.message}`);

            const response = await fetch(config.chatSendUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${users[userId].token}`
                },
                body: JSON.stringify(apiMessage)
            });

            const result = await response.json();

            if (result.code === 200) {
                addSystemMessage(userId, `✅ REST API消息发送成功: ${result.data}`);
                addSystemMessage(userId, `${users[targetUserId].name}(ID:${apiMessage.toUserId})应该会收到消息`);
            } else {
                addSystemMessage(userId, `❌ REST API消息发送失败: ${result.msg}`);
            }
        } catch (error) {
            addSystemMessage(userId, `❌ REST API调用失败: ${error.message}`);
        }
    }


    // 测试广播消息
    async function testBroadcast() {
        // 使用第一个有token的用户发送广播
        let senderUserId = null;
        let senderToken = null;

        for (let id in users) {
            if (users[id].token) {
                senderUserId = parseInt(id);
                senderToken = users[id].token;
                break;
            }
        }

        if (!senderToken) {
            addSystemMessage(1, '请先登录任意一个用户');
            addSystemMessage(2, '请先登录任意一个用户');
            return;
        }

        const broadcastMessage = `📢 系统广播消息 - ${new Date().toLocaleString()}`;

        try {
            addSystemMessage(senderUserId, '正在发送广播消息...');

            const response = await fetch(config.chatBroadcastUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${senderToken}`
                },
                body: JSON.stringify(broadcastMessage)
            });

            const result = await response.json();

            if (result.code === 200) {
                addSystemMessage(senderUserId, `广播消息发送成功: ${result.data}`);
            } else {
                addSystemMessage(senderUserId, `广播消息发送失败: ${result.msg}`);
            }
        } catch (error) {
            addSystemMessage(senderUserId, `广播消息发送失败: ${error.message}`);
        }
    }

    // 更新连接状态
    function updateStatus(userId, status, text) {
        const statusElement = document.getElementById(`user${userId}-status`);
        statusElement.className = `status ${status}`;
        statusElement.textContent = text;
        updateOnlineCount();
    }

    // 更新在线用户数量
    function updateOnlineCount() {
        const onlineCount = Object.values(users).filter(user => user.connected).length;
        document.getElementById('onlineCount').textContent = onlineCount;

        // 更新在线状态颜色
        const statusElement = document.getElementById('onlineStatus');
        if (onlineCount === 0) {
            statusElement.style.background = '#f8d7da';
            statusElement.style.borderColor = '#f5c6cb';
        } else if (onlineCount === 1) {
            statusElement.style.background = '#fff3cd';
            statusElement.style.borderColor = '#ffeaa7';
        } else {
            statusElement.style.background = '#d4edda';
            statusElement.style.borderColor = '#c3e6cb';
        }
    }

    // 添加系统消息
    function addSystemMessage(userId, message) {
        addMessage(userId, message, 'system');
    }

    // 添加聊天消息
    function addChatMessage(userId, senderName, message, type) {
        const chatBox = document.getElementById(`user${userId}-chat`);
        const messageElement = document.createElement('div');

        if (type === 'self') {
            messageElement.className = 'message sent';
            messageElement.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 2px;">${senderName} (我)</div>
                    <div>${message}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 2px;">${new Date().toLocaleTimeString()}</div>
                `;
        } else if (type === 'other') {
            messageElement.className = 'message received';
            messageElement.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 2px;">${senderName}</div>
                    <div>${message}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 2px;">${new Date().toLocaleTimeString()}</div>
                `;
        }

        chatBox.appendChild(messageElement);
        chatBox.scrollTop = chatBox.scrollHeight;
    }


    // 添加消息到聊天框
    function addMessage(userId, message, type) {
        const chatBox = document.getElementById(`user${userId}-chat`);
        const messageElement = document.createElement('div');
        messageElement.className = `message ${type}`;
        messageElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        chatBox.appendChild(messageElement);
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function () {
        updateOnlineCount();

        addSystemMessage(1, '👋 欢迎 Alice！请先登录获取真实用户ID');
        addSystemMessage(2, '👋 欢迎 Bob！请先登录获取真实用户ID');

        setTimeout(() => {
            addSystemMessage(1, '💡 登录后可与Bob进行1对1聊天');
            addSystemMessage(2, '💡 登录后可与Alice进行1对1聊天');
        }, 1000);
    });
</script>
</body>
</html>
