<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宜莱福多级营销计算器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .input-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }

        .input-group label {
            min-width: 120px;
            font-weight: bold;
            color: #333;
        }

        .input-group input, .input-group select {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            flex: 1;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            transition: transform 0.2s;
            margin-top: 15px;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
        }

        .results-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .result-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .result-card h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .level-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .level-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #667eea;
            transition: transform 0.2s;
        }

        .level-card:hover {
            transform: translateY(-5px);
        }

        .level-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .level-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .level-requirement {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }

        .level-details {
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-label {
            color: #666;
        }

        .detail-value {
            font-weight: bold;
            color: #333;
        }

        .income-range {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-top: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>宜莱福多级营销计算器</h1>
            <p>计算升级路径、团队需求和预期收益</p>
        </div>

        <div class="content">
            <div class="input-section">
                <h3>基础参数设置</h3>
                <div class="input-group">
                    <label>当前级别:</label>
                    <select id="currentLevel">
                        <option value="0">未开始</option>
                        <option value="1">珍珠</option>
                        <option value="2">蓝宝</option>
                        <option value="3">红宝</option>
                        <option value="4">绿宝</option>
                        <option value="5">钻石</option>
                        <option value="6">皇冠</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>当前团队人数:</label>
                    <input type="number" id="currentTeam" value="0" min="0">
                </div>
                <div class="input-group">
                    <label>目标级别:</label>
                    <select id="targetLevel">
                        <option value="1">珍珠</option>
                        <option value="2">蓝宝</option>
                        <option value="3">红宝</option>
                        <option value="4">绿宝</option>
                        <option value="5">钻石</option>
                        <option value="6">皇冠</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>团队活跃度(%):</label>
                    <input type="number" id="activityRate" value="70" min="1" max="100">
                </div>
                <button class="calculate-btn" onclick="calculatePath()">计算升级路径</button>
            </div>

            <div class="results-section">
                <div class="result-card">
                    <h3>升级需求分析</h3>
                    <div id="upgradeAnalysis"></div>
                </div>
                <div class="result-card">
                    <h3>收益预测</h3>
                    <div id="incomePrediction"></div>
                </div>
            </div>

            <div class="level-grid" id="levelGrid"></div>

            <div class="summary-stats" id="summaryStats"></div>

            <div class="warning">
                <strong>⚠️ 风险提醒：</strong>
                多级营销模式存在高风险，实际收益可能与预期有较大差异。请谨慎评估个人能力和风险承受度，建议咨询专业人士意见。
            </div>
        </div>
    </div>

    <script>
        const levels = [
            { id: 0, name: "未开始", requirement: 0, income: "0", color: "#6c757d" },
            { id: 1, name: "珍珠", requirement: 1, income: "几千元", color: "#e9ecef" },
            { id: 2, name: "蓝宝", requirement: 3, income: "5,000-10,000元", color: "#007bff" },
            { id: 3, name: "红宝", requirement: 12, income: "5-10万元", color: "#dc3545" },
            { id: 4, name: "绿宝", requirement: 29, income: "20万+", color: "#28a745" },
            { id: 5, name: "钻石", requirement: 57, income: "40-50万元", color: "#17a2b8" },
            { id: 6, name: "皇冠", requirement: 113, income: "80-100万元", color: "#ffc107" }
        ];

        const unitValue = 23730; // 1单 = 23,730元
        const pvPerUnit = 1770; // 1单 = 1770PV

        function calculatePath() {
            const currentLevel = parseInt(document.getElementById('currentLevel').value);
            const targetLevel = parseInt(document.getElementById('targetLevel').value);
            const currentTeam = parseInt(document.getElementById('currentTeam').value);
            const activityRate = parseInt(document.getElementById('activityRate').value) / 100;

            if (targetLevel <= currentLevel) {
                alert('目标级别必须高于当前级别！');
                return;
            }

            const targetLevelData = levels[targetLevel];
            const currentLevelData = levels[currentLevel];

            // 计算需要的团队规模
            const requiredUnits = targetLevelData.requirement;
            const effectiveTeamSize = Math.ceil(requiredUnits / activityRate);
            const additionalMembers = Math.max(0, effectiveTeamSize - currentTeam);

            // 计算预期收益
            const monthlyIncome = estimateIncome(targetLevel);
            const totalInvestment = requiredUnits * unitValue;
            const roi = ((monthlyIncome * 12) / totalInvestment * 100).toFixed(1);

            // 显示升级分析
            displayUpgradeAnalysis(currentLevelData, targetLevelData, additionalMembers, requiredUnits, activityRate);

            // 显示收益预测
            displayIncomePrediction(monthlyIncome, totalInvestment, roi, targetLevel);

            // 显示所有级别详情
            displayAllLevels(currentLevel, targetLevel);

            // 显示统计摘要
            displaySummaryStats(additionalMembers, monthlyIncome, totalInvestment);
        }

        function estimateIncome(level) {
            const incomeRanges = {
                1: 3000, // 珍珠：几千元，取3000
                2: 7500, // 蓝宝：5,000-10,000元，取7500
                3: 75000, // 红宝：5-10万元，取75000
                4: 250000, // 绿宝：20万+，取250000
                5: 450000, // 钻石：40-50万元，取450000
                6: 900000 // 皇冠：80-100万元，取900000
            };
            return incomeRanges[level] || 0;
        }

        function displayUpgradeAnalysis(currentLevel, targetLevel, additionalMembers, requiredUnits, activityRate) {
            const html = `
                <div class="detail-item">
                    <span class="detail-label">当前级别:</span>
                    <span class="detail-value">${currentLevel.name}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">目标级别:</span>
                    <span class="detail-value">${targetLevel.name}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">需要团队规模:</span>
                    <span class="detail-value">${Math.ceil(requiredUnits / activityRate)}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">需要新增成员:</span>
                    <span class="detail-value">${additionalMembers}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">四周小区要求:</span>
                    <span class="detail-value">${requiredUnits}单</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">总业绩要求:</span>
                    <span class="detail-value">${(requiredUnits * unitValue).toLocaleString()}元</span>
                </div>
            `;
            document.getElementById('upgradeAnalysis').innerHTML = html;
        }

        function displayIncomePrediction(monthlyIncome, totalInvestment, roi, targetLevel) {
            const html = `
                <div class="detail-item">
                    <span class="detail-label">预期月收入:</span>
                    <span class="detail-value">${monthlyIncome.toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">年收入预测:</span>
                    <span class="detail-value">${(monthlyIncome * 12).toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">总投入估算:</span>
                    <span class="detail-value">${totalInvestment.toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">年化ROI:</span>
                    <span class="detail-value">${roi}%</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">回本周期:</span>
                    <span class="detail-value">${Math.ceil(totalInvestment / monthlyIncome)}个月</span>
                </div>
            `;
            document.getElementById('incomePrediction').innerHTML = html;
        }

        function displayAllLevels(currentLevel, targetLevel) {
            let html = '';
            levels.forEach((level, index) => {
                if (index === 0) return; // 跳过"未开始"级别

                const isCurrent = index === currentLevel;
                const isTarget = index === targetLevel;
                const isAchieved = index <= currentLevel;
                const isInPath = index >= currentLevel && index <= targetLevel;

                const cardClass = isCurrent ? 'level-card current' :
                                isTarget ? 'level-card target' :
                                isAchieved ? 'level-card achieved' : 'level-card';

                const progress = isInPath ? ((index - currentLevel) / (targetLevel - currentLevel) * 100) : 0;

                html += `
                    <div class="${cardClass}" style="border-left-color: ${level.color}">
                        <div class="level-header">
                            <span class="level-name">${level.name}</span>
                            <span class="level-requirement">${level.requirement}单</span>
                        </div>
                        <div class="level-details">
                            <div class="detail-item">
                                <span class="detail-label">四周小区要求:</span>
                                <span class="detail-value">${level.requirement}单</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">业绩要求:</span>
                                <span class="detail-value">${(level.requirement * unitValue).toLocaleString()}元</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">团队规模:</span>
                                <span class="detail-value">${Math.ceil(level.requirement / 0.7)}人</span>
                            </div>
                        </div>
                        <div class="income-range">
                            月收入: ${level.income}
                        </div>
                        ${isInPath ? `
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progress}%"></div>
                            </div>
                            <small>升级进度: ${progress.toFixed(1)}%</small>
                        ` : ''}
                    </div>
                `;
            });
            document.getElementById('levelGrid').innerHTML = html;
        }

        function displaySummaryStats(additionalMembers, monthlyIncome, totalInvestment) {
            const html = `
                <div class="stat-card">
                    <div class="stat-value">${additionalMembers}</div>
                    <div class="stat-label">需要新增成员</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${monthlyIncome.toLocaleString()}</div>
                    <div class="stat-label">预期月收入(元)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${totalInvestment.toLocaleString()}</div>
                    <div class="stat-label">总投入估算(元)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${Math.ceil(totalInvestment / monthlyIncome)}</div>
                    <div class="stat-label">回本周期(月)</div>
                </div>
            `;
            document.getElementById('summaryStats').innerHTML = html;
        }

        // 页面加载时初始化
        window.onload = function() {
            calculatePath();
        };
    </script>
</body>
</html>
