<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宜莱福多级营销计算器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .input-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }

        .input-group label {
            min-width: 120px;
            font-weight: bold;
            color: #333;
        }

        .input-group input, .input-group select {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            flex: 1;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            transition: transform 0.2s;
            margin-top: 15px;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
        }

        .results-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .result-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .result-card h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .level-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .level-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #667eea;
            transition: transform 0.2s;
        }

        .level-card:hover {
            transform: translateY(-5px);
        }

        .level-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .level-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .level-requirement {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }

        .level-details {
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-label {
            color: #666;
        }

        .detail-value {
            font-weight: bold;
            color: #333;
        }

        .income-range {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-top: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>宜莱福多级营销计算器</h1>
            <p>计算升级路径、团队需求和预期收益</p>
        </div>

        <div class="content">
            <div class="input-section">
                <h3>目标收入计算器</h3>
                <div class="input-group">
                    <label>目标月收入(元):</label>
                    <input type="number" id="targetIncome" value="50000" min="1000" step="1000">
                </div>
                <div class="input-group">
                    <label>目标持续时间(月):</label>
                    <input type="number" id="targetDuration" value="12" min="1" max="60">
                </div>
                <div class="input-group">
                    <label>团队活跃度(%):</label>
                    <input type="number" id="activityRate" value="70" min="1" max="100">
                </div>
                <div class="input-group">
                    <label>团队流失率(%):</label>
                    <input type="number" id="churnRate" value="20" min="0" max="100">
                </div>
                <div class="input-group">
                    <label>每月新增成员:</label>
                    <input type="number" id="monthlyRecruitment" value="5" min="0" max="100">
                </div>
                <button class="calculate-btn" onclick="calculateTargetIncome()">计算目标方案</button>

                <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
                    <h4>💡 使用说明</h4>
                    <p>输入您希望达到的月收入目标，系统会计算：</p>
                    <ul style="margin-left: 20px;">
                        <li>需要达到的级别</li>
                        <li>需要的团队规模</li>
                        <li>每月需要招募的人数</li>
                        <li>收入可持续性分析</li>
                    </ul>
                </div>
            </div>

            <div class="results-section">
                <div class="result-card">
                    <h3>目标级别分析</h3>
                    <div id="targetLevelAnalysis"></div>
                </div>
                <div class="result-card">
                    <h3>团队需求分析</h3>
                    <div id="teamRequirementAnalysis"></div>
                </div>
            </div>

            <div class="result-card" style="margin-bottom: 30px;">
                <h3>可持续性分析</h3>
                <div id="sustainabilityAnalysis"></div>
            </div>

            <div class="level-grid" id="levelGrid"></div>

            <div class="summary-stats" id="summaryStats"></div>

            <div class="warning">
                <strong>⚠️ 风险提醒：</strong>
                多级营销模式存在高风险，实际收益可能与预期有较大差异。请谨慎评估个人能力和风险承受度，建议咨询专业人士意见。
            </div>
        </div>
    </div>

    <script>
        const levels = [
            { id: 0, name: "未开始", requirement: 0, income: "0", color: "#6c757d" },
            { id: 1, name: "珍珠", requirement: 1, income: "几千元", color: "#e9ecef" },
            { id: 2, name: "蓝宝", requirement: 3, income: "5,000-10,000元", color: "#007bff" },
            { id: 3, name: "红宝", requirement: 12, income: "5-10万元", color: "#dc3545" },
            { id: 4, name: "绿宝", requirement: 29, income: "20万+", color: "#28a745" },
            { id: 5, name: "钻石", requirement: 57, income: "40-50万元", color: "#17a2b8" },
            { id: 6, name: "皇冠", requirement: 113, income: "80-100万元", color: "#ffc107" }
        ];

        const unitValue = 23730; // 1单 = 23,730元
        const pvPerUnit = 1770; // 1单 = 1770PV

                        function calculateTargetIncome() {
            const targetIncome = parseInt(document.getElementById('targetIncome').value);
            const targetDuration = parseInt(document.getElementById('targetDuration').value);
            const activityRate = parseInt(document.getElementById('activityRate').value) / 100;
            const churnRate = parseInt(document.getElementById('churnRate').value) / 100;
            const monthlyRecruitment = parseInt(document.getElementById('monthlyRecruitment').value);

            // 找到能达到目标收入的最低级别
            const targetLevel = findTargetLevel(targetIncome);

            if (targetLevel === null) {
                alert('目标收入过高，无法达到！最高级别皇冠月收入约90万元。');
                return;
            }

            const targetLevelData = levels[targetLevel];
            const requiredUnits = targetLevelData.requirement;
            const requiredTeamSize = Math.ceil(requiredUnits / activityRate);

            // 计算需要的招募策略
            const recruitmentStrategy = calculateRecruitmentStrategy(requiredTeamSize, churnRate, monthlyRecruitment, targetDuration);

            // 计算可持续性
            const sustainabilityData = calculateSustainabilityWithRecruitment(targetLevel, requiredTeamSize, churnRate, monthlyRecruitment, targetDuration);

            // 显示分析结果
            displayTargetLevelAnalysis(targetLevel, targetIncome, requiredUnits, requiredTeamSize);
            displayTeamRequirementAnalysis(recruitmentStrategy, requiredTeamSize, activityRate);
            displaySustainabilityAnalysis(sustainabilityData, targetLevel);
            displayAllLevels(0, targetLevel);
            displaySummaryStats(recruitmentStrategy.initialRecruitment, targetIncome, requiredUnits * unitValue, sustainabilityData);
        }

        function findTargetLevel(targetIncome) {
            for (let i = levels.length - 1; i >= 1; i--) {
                if (estimateIncome(i) >= targetIncome) {
                    return i;
                }
            }
            return null;
        }

        function calculateRecruitmentStrategy(requiredTeamSize, churnRate, monthlyRecruitment, duration) {
            let currentTeamSize = 0;
            let totalRecruitment = 0;
            let monthsToReach = 0;

            // 计算达到目标团队规模需要的时间
            for (let month = 1; month <= duration; month++) {
                // 新增成员
                currentTeamSize += monthlyRecruitment;
                totalRecruitment += monthlyRecruitment;

                // 流失成员
                const lostMembers = Math.floor(currentTeamSize * churnRate);
                currentTeamSize = Math.max(0, currentTeamSize - lostMembers);

                if (currentTeamSize >= requiredTeamSize && monthsToReach === 0) {
                    monthsToReach = month;
                }
            }

            return {
                initialRecruitment: totalRecruitment,
                monthsToReach: monthsToReach,
                finalTeamSize: currentTeamSize,
                monthlyRecruitment: monthlyRecruitment,
                totalRecruitment: totalRecruitment
            };
        }

        function calculateSustainabilityWithRecruitment(targetLevel, initialTeamSize, churnRate, monthlyRecruitment, duration) {
            const data = [];
            let currentTeamSize = initialTeamSize;
            let currentLevel = targetLevel;

            for (let month = 1; month <= duration; month++) {
                // 新增成员
                currentTeamSize += monthlyRecruitment;

                // 流失成员
                const lostMembers = Math.floor(currentTeamSize * churnRate);
                currentTeamSize = Math.max(0, currentTeamSize - lostMembers);

                // 检查级别维持
                const requiredUnits = levels[currentLevel].requirement;
                const effectiveTeamSize = Math.ceil(requiredUnits / 0.7);

                if (currentTeamSize < effectiveTeamSize) {
                    // 降级逻辑
                    for (let i = currentLevel - 1; i >= 1; i--) {
                        const levelRequirement = levels[i].requirement;
                        const levelTeamSize = Math.ceil(levelRequirement / 0.7);
                        if (currentTeamSize >= levelTeamSize) {
                            currentLevel = i;
                            break;
                        }
                    }
                    if (currentTeamSize < Math.ceil(levels[1].requirement / 0.7)) {
                        currentLevel = 0;
                    }
                }

                const monthlyIncome = currentLevel > 0 ? estimateIncome(currentLevel) : 0;

                data.push({
                    month: month,
                    teamSize: currentTeamSize,
                    level: currentLevel,
                    levelName: levels[currentLevel].name,
                    income: monthlyIncome,
                    lostMembers: lostMembers,
                    newMembers: monthlyRecruitment
                });
            }

            return data;
        }

        function estimateIncome(level) {
            const incomeRanges = {
                1: 3000, // 珍珠：几千元，取3000
                2: 7500, // 蓝宝：5,000-10,000元，取7500
                3: 75000, // 红宝：5-10万元，取75000
                4: 250000, // 绿宝：20万+，取250000
                5: 450000, // 钻石：40-50万元，取450000
                6: 900000 // 皇冠：80-100万元，取900000
            };
            return incomeRanges[level] || 0;
        }

        function calculateSustainability(targetLevel, teamSize, churnRate, months) {
            const data = [];
            let currentTeamSize = teamSize;
            let currentLevel = targetLevel;

            for (let month = 1; month <= months; month++) {
                // 计算团队流失
                const lostMembers = Math.floor(currentTeamSize * churnRate);
                currentTeamSize = Math.max(0, currentTeamSize - lostMembers);

                // 计算当前级别（基于团队规模）
                const requiredUnits = levels[currentLevel].requirement;
                const effectiveTeamSize = Math.ceil(requiredUnits / 0.7); // 假设70%活跃度

                if (currentTeamSize < effectiveTeamSize) {
                    // 降级逻辑
                    for (let i = currentLevel - 1; i >= 1; i--) {
                        const levelRequirement = levels[i].requirement;
                        const levelTeamSize = Math.ceil(levelRequirement / 0.7);
                        if (currentTeamSize >= levelTeamSize) {
                            currentLevel = i;
                            break;
                        }
                    }
                    if (currentTeamSize < Math.ceil(levels[1].requirement / 0.7)) {
                        currentLevel = 0; // 未达到任何级别
                    }
                }

                const monthlyIncome = currentLevel > 0 ? estimateIncome(currentLevel) : 0;

                data.push({
                    month: month,
                    teamSize: currentTeamSize,
                    level: currentLevel,
                    levelName: levels[currentLevel].name,
                    income: monthlyIncome,
                    lostMembers: lostMembers
                });
            }

            return data;
        }

        function displayTargetLevelAnalysis(targetLevel, targetIncome, requiredUnits, requiredTeamSize) {
            const targetLevelData = levels[targetLevel];
            const html = `
                <div class="detail-item">
                    <span class="detail-label">目标月收入:</span>
                    <span class="detail-value">${targetIncome.toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">需要达到级别:</span>
                    <span class="detail-value">${targetLevelData.name}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">四周小区要求:</span>
                    <span class="detail-value">${requiredUnits}单</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">需要团队规模:</span>
                    <span class="detail-value">${requiredTeamSize}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">总业绩要求:</span>
                    <span class="detail-value">${(requiredUnits * unitValue).toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">预期月收入:</span>
                    <span class="detail-value">${estimateIncome(targetLevel).toLocaleString()}元</span>
                </div>
            `;
            document.getElementById('targetLevelAnalysis').innerHTML = html;
        }

        function displayTeamRequirementAnalysis(recruitmentStrategy, requiredTeamSize, activityRate) {
            const html = `
                <div class="detail-item">
                    <span class="detail-label">需要团队规模:</span>
                    <span class="detail-value">${requiredTeamSize}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">达到目标时间:</span>
                    <span class="detail-value">${recruitmentStrategy.monthsToReach > 0 ? recruitmentStrategy.monthsToReach + '个月' : '无法达到'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">每月需要招募:</span>
                    <span class="detail-value">${recruitmentStrategy.monthlyRecruitment}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">总招募人数:</span>
                    <span class="detail-value">${recruitmentStrategy.totalRecruitment}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">最终团队规模:</span>
                    <span class="detail-value">${recruitmentStrategy.finalTeamSize}人</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">招募成功率要求:</span>
                    <span class="detail-value">${((requiredTeamSize / recruitmentStrategy.totalRecruitment) * 100).toFixed(1)}%</span>
                </div>
            `;
            document.getElementById('teamRequirementAnalysis').innerHTML = html;
        }

                function displaySustainabilityAnalysis(data, targetLevel) {
            const firstMonthIncome = data[0].income;
            const lastMonthIncome = data[data.length - 1].income;
            const avgIncome = data.reduce((sum, item) => sum + item.income, 0) / data.length;
            const totalIncome = data.reduce((sum, item) => sum + item.income, 0);

            const incomeDecline = ((firstMonthIncome - lastMonthIncome) / firstMonthIncome * 100).toFixed(1);
            const monthsWithIncome = data.filter(item => item.income > 0).length;
            const monthsAtTargetLevel = data.filter(item => item.level === targetLevel).length;

            let html = `
                <div class="detail-item">
                    <span class="detail-label">首月收入:</span>
                    <span class="detail-value">${firstMonthIncome.toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">末月收入:</span>
                    <span class="detail-value">${lastMonthIncome.toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">平均月收入:</span>
                    <span class="detail-value">${avgIncome.toFixed(0).toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">总收入:</span>
                    <span class="detail-value">${totalIncome.toLocaleString()}元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">收入下降幅度:</span>
                    <span class="detail-value" style="color: ${incomeDecline > 0 ? '#dc3545' : '#28a745'}">${incomeDecline}%</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">有收入月数:</span>
                    <span class="detail-value">${monthsWithIncome}/${data.length}个月</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">维持目标级别月数:</span>
                    <span class="detail-value">${monthsAtTargetLevel}/${data.length}个月</span>
                </div>
            `;

            // 添加月度详细数据
            html += '<div style="margin-top: 20px; max-height: 300px; overflow-y: auto;">';
            html += '<h4>月度详细数据</h4>';
            html += '<table style="width: 100%; border-collapse: collapse; font-size: 14px;">';
            html += '<tr style="background: #f8f9fa;"><th style="padding: 8px; border: 1px solid #ddd;">月份</th><th style="padding: 8px; border: 1px solid #ddd;">团队规模</th><th style="padding: 8px; border: 1px solid #ddd;">级别</th><th style="padding: 8px; border: 1px solid #ddd;">月收入</th><th style="padding: 8px; border: 1px solid #ddd;">新增</th><th style="padding: 8px; border: 1px solid #ddd;">流失</th></tr>';

            data.forEach(item => {
                html += `<tr>
                    <td style="padding: 8px; border: 1px solid #ddd;">第${item.month}月</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${item.teamSize}人</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${item.levelName}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${item.income.toLocaleString()}元</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">+${item.newMembers}人</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">-${item.lostMembers}人</td>
                </tr>`;
            });

            html += '</table></div>';

            document.getElementById('sustainabilityAnalysis').innerHTML = html;
        }

        function displayAllLevels(currentLevel, targetLevel) {
            let html = '';
            levels.forEach((level, index) => {
                if (index === 0) return; // 跳过"未开始"级别

                const isCurrent = index === currentLevel;
                const isTarget = index === targetLevel;
                const isAchieved = index <= currentLevel;
                const isInPath = index >= currentLevel && index <= targetLevel;

                const cardClass = isCurrent ? 'level-card current' :
                                isTarget ? 'level-card target' :
                                isAchieved ? 'level-card achieved' : 'level-card';

                const progress = isInPath ? ((index - currentLevel) / (targetLevel - currentLevel) * 100) : 0;

                html += `
                    <div class="${cardClass}" style="border-left-color: ${level.color}">
                        <div class="level-header">
                            <span class="level-name">${level.name}</span>
                            <span class="level-requirement">${level.requirement}单</span>
                        </div>
                        <div class="level-details">
                            <div class="detail-item">
                                <span class="detail-label">四周小区要求:</span>
                                <span class="detail-value">${level.requirement}单</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">业绩要求:</span>
                                <span class="detail-value">${(level.requirement * unitValue).toLocaleString()}元</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">团队规模:</span>
                                <span class="detail-value">${Math.ceil(level.requirement / 0.7)}人</span>
                            </div>
                        </div>
                        <div class="income-range">
                            月收入: ${level.income}
                        </div>
                        ${isInPath ? `
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progress}%"></div>
                            </div>
                            <small>升级进度: ${progress.toFixed(1)}%</small>
                        ` : ''}
                    </div>
                `;
            });
            document.getElementById('levelGrid').innerHTML = html;
        }

        function displaySummaryStats(additionalMembers, monthlyIncome, totalInvestment, sustainabilityData) {
            const avgIncome = sustainabilityData.reduce((sum, item) => sum + item.income, 0) / sustainabilityData.length;
            const totalIncome = sustainabilityData.reduce((sum, item) => sum + item.income, 0);

            const html = `
                <div class="stat-card">
                    <div class="stat-value">${additionalMembers}</div>
                    <div class="stat-label">需要新增成员</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${monthlyIncome.toLocaleString()}</div>
                    <div class="stat-label">首月收入(元)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${avgIncome.toFixed(0).toLocaleString()}</div>
                    <div class="stat-label">平均月收入(元)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${totalIncome.toLocaleString()}</div>
                    <div class="stat-label">总收入(元)</div>
                </div>
            `;
            document.getElementById('summaryStats').innerHTML = html;
        }

        // 页面加载时初始化
        window.onload = function() {
            calculateTargetIncome();
        };
    </script>
</body>
</html>
