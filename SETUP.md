# 快速启动指南

## 🚀 立即开始

### 1. 创建数据库表

#### 选择建表方案：

**🟢 推荐：简化版（仅注册登录功能）**

```bash
mysql -u root -prootroot im-chat < script/sql/merchant_admin_tables_simple.sql
```

**🔵 标准版（无外键约束，符合DDD规范）**

```bash
mysql -u root -prootroot im-chat < script/sql/merchant_admin_tables_no_fk.sql
```

**⚠️ 原始版（有外键约束，存在设计问题）**

```bash
mysql -u root -prootroot im-chat < script/sql/merchant_admin_tables.sql
```

**📝 详细对比：**

| 版本  | 表数量 | 外键约束   | 适用场景   | 优缺点                    |
|-----|-----|--------|--------|------------------------|
| 简化版 | 2张表 | ❌ 无    | 快速开发测试 | ✅ 最简单 ❌ 功能有限           |
| 标准版 | 4张表 | ❌ 无    | 生产环境推荐 | ✅ 符合DDD ✅ 高性能 ✅ 支持软删除  |
| 原始版 | 4张表 | ⚠️ 有问题 | 不推荐    | ❌ 违背DDD ❌ 性能问题 ❌ 软删除冲突 |

**🎯 建议：**

- **开发测试**：使用简化版
- **生产部署**：使用标准版

### 2. 启动应用

```bash
cd im-admin
mvn spring-boot:run
```

### 3. 测试API

#### 注册新商户

```bash
curl -X POST http://localhost:8080/api/merchants/simple-register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "companyName": "测试公司",
    "password": "123456"
  }'
```

#### 登录

```bash
curl -X POST http://localhost:8080/api/merchants/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "123456"
  }'
```

## ✅ 问题解决

### 转换器问题已修复

- ❌ 之前：`cannot find converter from Merchant to MerchantDO`
- ✅ 现在：使用手动转换器，确保可靠性

### 表名规范化

- ❌ 之前：`merchant`、`merchant_administrator`
- ✅ 现在：`im_merchant`、`im_merchant_administrator`

### 配置说明

确保数据库配置正确（`im-admin/src/main/resources/application-local.yml`）：

```yaml
url: ***********************************?...
username: root
password: rootroot
```

## 📋 检查清单

- [ ] 数据库 `im-chat` 已创建
- [ ] 执行了建表脚本 `script/sql/merchant_admin_tables.sql`
- [ ] 应用启动无错误
- [ ] 注册接口测试成功
- [ ] 登录接口测试成功

## 🔧 如果仍有问题

1. **检查数据库连接**：确保 MySQL 服务运行，用户名密码正确
2. **检查表是否创建**：`SHOW TABLES LIKE 'im_%'`
3. **查看应用日志**：检查启动过程中的错误信息
4. **验证依赖**：确保所有 Maven 依赖已下载

现在应用应该可以正常运行了！🎉 